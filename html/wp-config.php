<?php
if (!defined("RSSSL_HEADERS_ACTIVE") && file_exists( ABSPATH . "wp-content/advanced-headers.php")) {
	require_once ABSPATH . "wp-content/advanced-headers.php";
}

//Begin Really Simple Security session cookie settings
@ini_set('session.cookie_httponly', true);
@ini_set('session.cookie_secure', true);
@ini_set('session.use_only_cookies', true);
//END Really Simple Security cookie settings
define('WP_CACHE', true); // Added by FlyingPress

/**

 * The base configuration for WordPress

 *

 * The wp-config.php creation script uses this file during the installation.

 * You don't have to use the website, you can copy this file to "wp-config.php"

 * and fill in the values.

 *

 * This file contains the following configurations:

 *

 * * Database settings

 * * Secret keys

 * * Database table prefix

 * * ABSPATH

 *

 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/

 *

 * @package WordPress

 */

// ** Database settings - You can get this info from your web host ** //

/** The name of the database for WordPress */

define( 'DB_NAME', 'appdb' );

/** Database username */

define( 'DB_USER', 'appuser' );

/** Database password */

define( 'DB_PASSWORD', 'userpassword' );

/** Database hostname */

define( 'DB_HOST', 'mysql-bibbvoice7.com' );

/** Database charset to use in creating database tables. */

define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */

define( 'DB_COLLATE', '' );

/**#@+

 * Authentication unique keys and salts.

 *

 * Change these to different unique phrases! You can generate these using

 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.

 *

 * You can change these at any point in time to invalidate all existing cookies.

 * This will force all users to have to log in again.

 *

 * @since 2.6.0

 */

define( 'AUTH_KEY',         '~&^)W<C?MV@H9+AIhv9;Q2Bc[.2z8v``JQ8SB}Uq,E I+T.LwW3nK smj#7%<0eX' );

define( 'SECURE_AUTH_KEY',  'JPcMq`2D7KF7C>NE8D)W6LIm)|b^_hNQAS;E+h<MEu!.f[WV[O7}RWc-yHMV+vT`' );

define( 'LOGGED_IN_KEY',    '8!|y#Ztd9B)D{axgD@,OU[>M)m(,h#NtT>NQh|[Ss(&GbtuRKjnvkQ|>&[i{v(^x' );

define( 'NONCE_KEY',        'uXr>B!gc<c&*%n6htvaiGA.}r2RA7g5(L$QN=>!=94KEO!LzSLh}{8]V8sz!H^X_' );

define( 'AUTH_SALT',        '2_+2G%/9SgaRDvI!>nzH-<iEA;(2ubqI2_=;@ /o:BZrxCl$Cvp87b}52:xTQsV(' );

define( 'SECURE_AUTH_SALT', 'd-sIb1e]=CS)`O.S3-mhrWp2AYXJbg-fD7.<A1M:A-L$:M|7hRA/DV(3Z`)0 0_I' );

define( 'LOGGED_IN_SALT',   '71v; .$0yfRP2m<@s FJa>YWD]O.?a{=r~li@}6-@^z_=k0d*`m,=DL_P9[IZwfo' );

define( 'NONCE_SALT',       '%A8a5~([D^Z*w88x:nas($c:g]dOB1X6(M(l54qYcland-ie5ZI5`s7PloKIO!:A' );

/**#@-*/

/**

 * WordPress database table prefix.

 *

 * You can have multiple installations in one database if you give each

 * a unique prefix. Only numbers, letters, and underscores please!

 */

define( 'PRIME_MOVER_DB_ENCRYPTION_KEY', '949cf123f1e8d876083da1aeb5dc1625c55f05cd4659cfc026708ce6b8fbfb12' );
define( 'WP_SITEURL', 'https://bibbvoice7.com.local' );
define( 'WP_HOME', 'https://bibbvoice7.com.local' );
$table_prefix = 'wp_';

/**

 * For developers: WordPress debugging mode.

 *

 * Change this to true to enable the display of notices during development.

 * It is strongly recommended that plugin and theme developers use WP_DEBUG

 * in their development environments.

 *

 * For information on other constants that can be used for debugging,

 * visit the documentation.

 *

 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/

 */

define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG_DISPLAY', false );


/* Add any custom values between this line and the "stop editing" line. */



/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */

if ( ! defined( 'ABSPATH' ) ) {

	define( 'ABSPATH', __DIR__ . '/' );

}

/** Sets up WordPress vars and included files. */

require_once ABSPATH . 'wp-settings.php';

