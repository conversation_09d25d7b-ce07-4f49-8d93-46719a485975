2025-09-12 02:53:56 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteImportId method: Deleted import ID:  eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteExportId method: Deleted export ID:  eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteExportResultId method: Deleted export result ID: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteImportResultId method: Deleted import result ID:  import_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteDownloadResultId method: Deleted download result ID: download_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteDownloadSizeId method: Deleted download size ID: download_size_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteDownloadTmpFileId method: Deleted download tmp file ID: download_tmp_path_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteUploadDropBoxSize method: Deleted dropbox upload size ID: dropboxupload_size_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteUploadGdriveSize method: Deleted GDrive upload size ID: gdriveupload_size_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:53:56 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, boot);
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 1 BOOT-UP EVENT -DIFF MODE: 
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Local server: 
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:53:56 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCleanUp::maybeCleanUpFallBackUserMeta method: CLEANING UP FALLBACK USER META ON prime_mover_before_doing_export ACTION HOOK USING user ID: 1.
2025-09-12 02:53:56 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCleanUp::maybeCleanUpFallBackUserMeta method: Array
(
    [prime_mover_excluded_settings_db] => 
    [prime_mover_user_enc_key_settings] => 
    [prime_mover_current_settings] => 
    [prime_mover_current_gearbox_packages] => 
)

2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Starting export);
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Creating temp folder);
2025-09-12 02:53:56 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::createTempfolderForThisSiteExport method: PEAK MEMORY USAGE : 6.9950485229492 MiB
2025-09-12 02:53:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:53:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:53:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Dumping database);
2025-09-12 02:53:59 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverLockUtilities::writeLockUsers method: DB LOCK: LOCKING event update to the lock file:
2025-09-12 02:53:59 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverLockUtilities::writeLockUsers method: Array
(
    [0] => 39ff4a8fd4df856151a1622630b3b607d1306649e172d47232c85886e28b93eb
)

2025-09-12 02:53:59 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::backupControlPanelSettings method: DB LOCK: Moving settings from options to user meta using prime_mover_current_settings meta key to LOCK this in prime_mover_before_db_dump_export hook using user ID: 1.
2025-09-12 02:53:59 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::backupGearBoxPackages method: DB LOCK: Moving settings from options to user meta using prime_mover_current_gearbox_packages meta key to LOCK this in prime_mover_before_db_dump_export hook.
2025-09-12 02:53:59 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::backupOptionRelatedSettings method: DB LOCK: Moving settings from options to user meta using prime_mover_user_enc_key_settings meta key to LOCK this in prime_mover_before_db_dump_export hook using user ID: 1.
2025-09-12 02:53:59 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::backupExcludedOptions method: DB LOCK: Moving settings from options to user meta using prime_mover_excluded_settings_db meta key to LOCK this in prime_mover_before_db_dump_export hook using user ID: 1.
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverMultilingualCompat::initializeCharSetParameters method: Logging initialized charsets and collate before MySQLdump: 
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverMultilingualCompat::initializeCharSetParameters method: Array
(
    [target_charset] => utf8mb4
    [charset_same] => 1
    [source_collate] => utf8mb4_unicode_520_ci
    [sourcecollate_equivalence] => Array
        (
        )

    [sourcecharset_masterlists] => Array
        (
        )

)

2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::dumpDbForExport method: Starting MySQLdump using PHP
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: mysql:host=mysql-bibbvoice7.com;dbname=appdb
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: PHPDump Batch Size: 5000
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_actionscheduler_actions
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_actionscheduler_actions
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:53:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 2
2025-09-12 02:54:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Dumping database
2025-09-12 02:54:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Dumping database with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 3
2025-09-12 02:54:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Dumping database
2025-09-12 02:54:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Dumping database with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:14 => Logged export event for blog ID 0 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: 15 seconds time out hits while dumping database. Index to resume: 45000, Table to resume: wp_mailpoet_statistics_newsletters, Batch size used: 5000
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Dumping database 45% done.);
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverMultilingualCompat::initializeCharSetParameters method: Logging initialized charsets and collate before MySQLdump: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverMultilingualCompat::initializeCharSetParameters method: Array
(
    [target_charset] => utf8mb4
    [charset_same] => 1
    [source_collate] => utf8mb4_unicode_520_ci
    [sourcecollate_equivalence] => Array
        (
        )

    [sourcecharset_masterlists] => Array
        (
        )

)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::dumpDbForExport method: Starting MySQLdump using PHP
2025-09-12 02:54:16 => Logged export event for blog ID 0 from Codexonics\PrimeMoverFramework\streams\PrimeMoverDatabaseUtilities::maybeFilterPdoDsn method: No port connection already established - skipping PDO instance check.
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: mysql:host=mysql-bibbvoice7.com;dbname=appdb
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: PHPDump Batch Size: 5000
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 4
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Dumping database 45% done.
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Dumping database 45% done. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_mailpoet_statistics_newsletters
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 5
2025-09-12 02:54:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Dumping database 45% done.
2025-09-12 02:54:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Dumping database 45% done. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:31 => Logged export event for blog ID 0 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: 15 seconds time out hits while dumping database. Index to resume: 10000, Table to resume: wp_wfknownfilelist, Batch size used: 5000
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Dumping database 94% done.);
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverMultilingualCompat::initializeCharSetParameters method: Logging initialized charsets and collate before MySQLdump: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverMultilingualCompat::initializeCharSetParameters method: Array
(
    [target_charset] => utf8mb4
    [charset_same] => 1
    [source_collate] => utf8mb4_unicode_520_ci
    [sourcecollate_equivalence] => Array
        (
        )

    [sourcecharset_masterlists] => Array
        (
        )

)

2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::dumpDbForExport method: Starting MySQLdump using PHP
2025-09-12 02:54:33 => Logged export event for blog ID 0 from Codexonics\PrimeMoverFramework\streams\PrimeMoverDatabaseUtilities::maybeFilterPdoDsn method: No port connection already established - skipping PDO instance check.
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: mysql:host=mysql-bibbvoice7.com;dbname=appdb
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::executeDumpUsingPHP method: PHPDump Batch Size: 5000
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_wfknownfilelist
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_wfknownfilelist
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_wfknownfilelist
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:33 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: MySQLdump settings: 
2025-09-12 02:54:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
    [include-tables] => Array
        (
            [0] => wp_wfknownfilelist
        )

    [exclude-tables] => Array
        (
        )

    [compress] => None
    [init_commands] => Array
        (
        )

    [no-data] => Array
        (
        )

    [reset-auto-increment] => 
    [add-drop-database] => 
    [add-drop-table] => 1
    [add-drop-trigger] => 1
    [add-locks] => 1
    [complete-insert] => 
    [databases] => 
    [default-character-set] => utf8mb4
    [disable-keys] => 1
    [extended-insert] => 1
    [events] => 
    [hex-blob] => 
    [insert-ignore] => 
    [no-autocommit] => 
    [no-create-info] => 
    [lock-tables] => 1
    [routines] => 
    [single-transaction] => 1
    [skip-triggers] => 
    [skip-tz-utc] => 
    [skip-comments] => 
    [skip-dump-date] => 
    [skip-definer] => 
    [where] => 
)

2025-09-12 02:54:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: PDO settings: 
2025-09-12 02:54:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverExporter::triggerMySQLDumpPHP method: Array
(
)

2025-09-12 02:54:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 6
2025-09-12 02:54:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Dumping database 94% done.
2025-09-12 02:54:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Dumping database 94% done. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:35 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverLockUtilities::writeLockUsers method: DB LOCK: UNLOCKING event update to the lock file:
2025-09-12 02:54:35 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverLockUtilities::writeLockUsers method: Array
(
)

2025-09-12 02:54:35 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::restoreGearBoxPackages method: DB LOCK: MOVED SETTINGS prime_mover_current_gearbox_packages from user meta to options in prime_mover_after_db_dump_export hook using user ID: 1.
2025-09-12 02:54:35 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::restoreOptionRelatedSettings method: DB LOCK: MOVED SETTINGS prime_mover_user_enc_key_settings from user meta to options in prime_mover_after_db_dump_export hook using user ID: 1.
2025-09-12 02:54:35 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::restoreExcludedOptions method: DB LOCK: MOVED SETTINGS prime_mover_excluded_settings_db from user meta to options in prime_mover_after_db_dump_export hook using user ID: 1.
2025-09-12 02:54:35 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\utilities\PrimeMoverComponentAuxiliary::restoreControlPanelSettings method: DB LOCK: MOVED SETTINGS prime_mover_current_settings from user meta to options in prime_mover_after_db_dump_export hook using user ID: 1.
2025-09-12 02:54:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::dumpDbForExport method: PEAK MEMORY USAGE : 7.2570037841797 MiB
2025-09-12 02:54:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:54:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:54:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:54:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Archiving database..0B bytes done.);
2025-09-12 02:54:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 7
2025-09-12 02:54:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Archiving database..0B bytes done.
2025-09-12 02:54:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Archiving database..0B bytes done. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 8
2025-09-12 02:54:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Archiving database..0B bytes done.
2025-09-12 02:54:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Archiving database..0B bytes done. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:54:52 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\build\splitbrain\PHPArchive\Tar::addFile method: 15 seconds Time out reach while archiving /var/www/html/wp-content/uploads/prime-mover-export-files/1/thebibbvYSIyhsnbVrHp9VXblogid_1/1.sql.enc on position 223585792
2025-09-12 02:54:54 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:54:54 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:54:54 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:54:54 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Archiving database..213M bytes done.);
2025-09-12 02:54:54 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\build\splitbrain\PHPArchive\Tar::addFile method: Resuming reading /var/www/html/wp-content/uploads/prime-mover-export-files/1/thebibbvYSIyhsnbVrHp9VXblogid_1/1.sql.enc on position 223585792
2025-09-12 02:54:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::zipDbDump method: PEAK MEMORY USAGE : 7.0374526977539 MiB
2025-09-12 02:54:58 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:54:58 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:54:58 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:54:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:54:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 9
2025-09-12 02:54:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:54:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Archiving database..213M bytes done.
2025-09-12 02:54:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Archiving database..213M bytes done. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:00 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:00 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:00 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:02 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:02 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:02 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:04 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:04 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:04 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootPrintKeys method: Footprint keys for validation :
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootPrintKeys method: Array
(
    [0] => plugins
    [1] => stylesheet
    [2] => template
    [3] => using_child_theme
    [4] => footprint_blog_id
    [5] => site_url
    [6] => wp_root
    [7] => db_prefix
    [8] => scheme
    [9] => upload_information_path
    [10] => upload_information_url
    [11] => prime_mover_plugin_version
)

2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootprintConfiguration method: Custom log for site footprint data before check:
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootprintConfiguration method: Array
(
    [plugins] => Array
        (
            [prime-mover-pro/prime-mover.php] => 2.0.8
            [add-search-to-menu/add-search-to-menu.php] => 5.5.11
            [adrotate-pro/adrotate-pro.php] => 5.7.3
            [classic-editor/classic-editor.php] => 1.6.7
            [code-snippets/code-snippets.php] => 3.7.0
            [external-media-without-import/external-media-without-import.php] => 1.1.2
            [featured-image-from-url/featured-image-from-url.php] => 5.2.7
            [flying-press/flying-press.php] => 5.0.7
            [guest-author/index.php] => 2.6
            [leaky-paywall/leaky-paywall.php] => 4.22.3
            [list-category-posts/list-category-posts.php] => 0.91.0
            [mailpoet/mailpoet.php] => 5.14.2
            [one-user-avatar/one-user-avatar.php] => 2.5.0
            [pdf-embedder/pdf_embedder.php] => 4.9.2
            [real-media-library/index.php] => 4.22.54
            [really-simple-ssl-pro/really-simple-ssl-pro.php] => 9.4.1
            [slider-image/slider.php] => 4.0.6
            [sticky-toc-advance-table-of-contents/sticky-toc-advance-table-of-contents.php] => 1.0.2
            [styler-for-wpforms/styler-for-wpforms.php] => 3.6
            [twitter-cards-meta/twitter-cards-meta.php] => 2.9.1
            [user-menus/user-menus.php] => 1.3.2
            [weather-atlas/weather-atlas.php] => 3.0.4
            [wordfence/wordfence.php] => 8.1.0
            [wp-custom-avatar/wp-custom-avatar.php] => 1.2.1
            [wp-import-export-lite/wp-import-export-lite.php] => 3.9.30
            [wp-php-version-display/wp-php-version-display.php] => 2.0
        )

    [stylesheet] => Array
        (
            [Newspaper] => 7.3
        )

    [template] => Array
        (
            [Newspaper] => 7.3
        )

    [using_child_theme] => no
    [footprint_blog_id] => 1
    [site_url] => bibbvoice7.com.local
    [wp_root] => /var/www/html/
    [db_prefix] => hrniwx0_
    [scheme] => https://
    [upload_information_path] => /var/www/html/wp-content/uploads
    [upload_information_url] => https://bibbvoice7.com.local/wp-content/uploads
    [prime_mover_plugin_version] => 2.0.8
)

2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootprintConfiguration method: Footprint data validity is: 1
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootprintConfiguration method: Custom log for site footprint data after check:
2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCompatibility::primeMoverGenerateFootprintConfiguration method: Array
(
    [plugins] => Array
        (
            [prime-mover-pro/prime-mover.php] => 2.0.8
            [add-search-to-menu/add-search-to-menu.php] => 5.5.11
            [adrotate-pro/adrotate-pro.php] => 5.7.3
            [classic-editor/classic-editor.php] => 1.6.7
            [code-snippets/code-snippets.php] => 3.7.0
            [external-media-without-import/external-media-without-import.php] => 1.1.2
            [featured-image-from-url/featured-image-from-url.php] => 5.2.7
            [flying-press/flying-press.php] => 5.0.7
            [guest-author/index.php] => 2.6
            [leaky-paywall/leaky-paywall.php] => 4.22.3
            [list-category-posts/list-category-posts.php] => 0.91.0
            [mailpoet/mailpoet.php] => 5.14.2
            [one-user-avatar/one-user-avatar.php] => 2.5.0
            [pdf-embedder/pdf_embedder.php] => 4.9.2
            [real-media-library/index.php] => 4.22.54
            [really-simple-ssl-pro/really-simple-ssl-pro.php] => 9.4.1
            [slider-image/slider.php] => 4.0.6
            [sticky-toc-advance-table-of-contents/sticky-toc-advance-table-of-contents.php] => 1.0.2
            [styler-for-wpforms/styler-for-wpforms.php] => 3.6
            [twitter-cards-meta/twitter-cards-meta.php] => 2.9.1
            [user-menus/user-menus.php] => 1.3.2
            [weather-atlas/weather-atlas.php] => 3.0.4
            [wordfence/wordfence.php] => 8.1.0
            [wp-custom-avatar/wp-custom-avatar.php] => 1.2.1
            [wp-import-export-lite/wp-import-export-lite.php] => 3.9.30
            [wp-php-version-display/wp-php-version-display.php] => 2.0
        )

    [stylesheet] => Array
        (
            [Newspaper] => 7.3
        )

    [template] => Array
        (
            [Newspaper] => 7.3
        )

    [using_child_theme] => no
    [footprint_blog_id] => 1
    [site_url] => bibbvoice7.com.local
    [wp_root] => /var/www/html/
    [db_prefix] => hrniwx0_
    [scheme] => https://
    [upload_information_path] => /var/www/html/wp-content/uploads
    [upload_information_url] => https://bibbvoice7.com.local/wp-content/uploads
    [prime_mover_plugin_version] => 2.0.8
)

2025-09-12 02:55:06 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Generating config);
2025-09-12 02:55:07 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:07 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 10
2025-09-12 02:55:07 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:07 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Generating config
2025-09-12 02:55:07 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Generating config with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:08 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:08 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:08 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:10 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:10 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:10 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:12 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:12 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:12 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:14 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:14 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:14 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:14 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 0% done);
2025-09-12 02:55:14 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0592880249023 MiB
2025-09-12 02:55:15 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:15 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 11
2025-09-12 02:55:15 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:15 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 0% done
2025-09-12 02:55:15 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 0% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 4% done);
2025-09-12 02:55:16 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0582427978516 MiB
2025-09-12 02:55:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 8% done);
2025-09-12 02:55:18 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0582046508789 MiB
2025-09-12 02:55:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 12% done);
2025-09-12 02:55:20 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0581283569336 MiB
2025-09-12 02:55:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 16% done);
2025-09-12 02:55:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0580902099609 MiB
2025-09-12 02:55:23 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:23 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 12
2025-09-12 02:55:23 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:23 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 16% done
2025-09-12 02:55:23 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 16% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 20% done);
2025-09-12 02:55:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0579986572266 MiB
2025-09-12 02:55:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 24% done);
2025-09-12 02:55:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0582122802734 MiB
2025-09-12 02:55:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 28% done);
2025-09-12 02:55:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0581588745117 MiB
2025-09-12 02:55:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 32% done);
2025-09-12 02:55:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0581130981445 MiB
2025-09-12 02:55:31 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:31 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 13
2025-09-12 02:55:31 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:31 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 32% done
2025-09-12 02:55:31 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 32% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 36% done);
2025-09-12 02:55:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0574493408203 MiB
2025-09-12 02:55:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 40% done);
2025-09-12 02:55:34 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.057373046875 MiB
2025-09-12 02:55:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 44% done);
2025-09-12 02:55:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0573425292969 MiB
2025-09-12 02:55:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 48% done);
2025-09-12 02:55:37 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0572662353516 MiB
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 14
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 48% done
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 48% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 52% done);
2025-09-12 02:55:39 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0572280883789 MiB
2025-09-12 02:55:41 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:41 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:41 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:41 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 56% done);
2025-09-12 02:55:41 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0577697753906 MiB
2025-09-12 02:55:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 60% done);
2025-09-12 02:55:43 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0576934814453 MiB
2025-09-12 02:55:45 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:45 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:45 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:45 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 64% done);
2025-09-12 02:55:45 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0576629638672 MiB
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 15
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 64% done
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 64% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 68% done);
2025-09-12 02:55:47 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0572357177734 MiB
2025-09-12 02:55:49 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:49 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:49 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:49 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 72% done);
2025-09-12 02:55:49 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0571594238281 MiB
2025-09-12 02:55:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 76% done);
2025-09-12 02:55:51 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0570831298828 MiB
2025-09-12 02:55:53 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:53 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:53 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:53 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 80% done);
2025-09-12 02:55:53 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0570297241211 MiB
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 16
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 80% done
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 80% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 84% done);
2025-09-12 02:55:55 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0569763183594 MiB
2025-09-12 02:55:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 88% done);
2025-09-12 02:55:57 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0569458007812 MiB
2025-09-12 02:55:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:55:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:55:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:55:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 92% done);
2025-09-12 02:55:59 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0568695068359 MiB
2025-09-12 02:56:01 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:01 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:01 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:01 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List plugin files: 96% done);
2025-09-12 02:56:01 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generatePluginFilesList method: PEAK MEMORY USAGE : 7.0568084716797 MiB
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 17
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = List plugin files: 96% done
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: List plugin files: 96% done with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:03 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Archiving plugin, starting.);
2025-09-12 02:56:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:56:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 18
2025-09-12 02:56:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:56:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Archiving plugin, starting.
2025-09-12 02:56:11 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Archiving plugin, starting. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\archiver\PrimeMoverArchiver::addDirectory method: End of file is reach for file list. Closing.
2025-09-12 02:56:17 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::optionallyExportPluginsThemes method: PEAK MEMORY USAGE : 7.0577011108398 MiB
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 19
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Archiving plugin, starting.
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Archiving plugin, starting. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, List theme files: 0% done);
2025-09-12 02:56:19 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generateThemesFilesList method: PEAK MEMORY USAGE : 7.0565338134766 MiB
2025-09-12 02:56:21 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:21 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:21 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:21 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Archiving theme, starting.);
2025-09-12 02:56:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\archiver\PrimeMoverArchiver::addDirectory method: End of file is reach for file list. Closing.
2025-09-12 02:56:22 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::maybeExportThemes method: PEAK MEMORY USAGE : 7.057746887207 MiB
2025-09-12 02:56:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:24 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:26 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::zippedFolder method: PEAK MEMORY USAGE : 7.0000457763672 MiB
2025-09-12 02:56:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:56:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 20
2025-09-12 02:56:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:56:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = Archiving theme, starting.
2025-09-12 02:56:27 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: Archiving theme, starting. with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Deleting temp folder);
2025-09-12 02:56:28 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::deleteTemporaryFolder method: PEAK MEMORY USAGE : 7.0558395385742 MiB
2025-09-12 02:56:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, Generate download URL);
2025-09-12 02:56:30 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::generateDownloadUrl method: PEAK MEMORY USAGE : 7.0745544433594 MiB
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: export_processor_ajax: Validation success
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export parameters cleared and validated, starting the export.
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::initializeProgressTracker method: Initializing progress tracker
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemFunctions::doPostExportProcessing method: PEAK MEMORY USAGE : 7.0024490356445 MiB
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: export successfully completed in 156.09352016449 seconds
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: Average peak memory used is 7.0581344604492 MiB
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: Maximum peak memory used is 7.2570037841797 MiB
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: Actual memory values recorded: 
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: Array
(
    [0] => 7334840
    [1] => 7609520
    [2] => 7379304
    [3] => 7402200
    [4] => 7401104
    [5] => 7401064
    [6] => 7400984
    [7] => 7400944
    [8] => 7400848
    [9] => 7401072
    [10] => 7401016
    [11] => 7400968
    [12] => 7400272
    [13] => 7400192
    [14] => 7400160
    [15] => 7400080
    [16] => 7400040
    [17] => 7400608
    [18] => 7400528
    [19] => 7400496
    [20] => 7400048
    [21] => 7399968
    [22] => 7399888
    [23] => 7399832
    [24] => 7399776
    [25] => 7399744
    [26] => 7399664
    [27] => 7399600
    [28] => 7400536
    [29] => 7399312
    [30] => 7400584
    [31] => 7340080
    [32] => 7398584
    [33] => 7418208
    [34] => 7342600
)

2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: Average CPU usage: 
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemChecks::computePerformanceStats method: Array
(
    [0] => 0.66
    [1] => 0.54
    [2] => 0.3
)

2025-09-12 02:56:32 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCleanUp::maybeCleanUpFallBackUserMeta method: CLEANING UP FALLBACK USER META ON prime_mover_after_completing_export ACTION HOOK USING user ID: 1.
2025-09-12 02:56:32 => Logged common event for blog ID 1 from Codexonics\PrimeMoverFramework\compatibility\PrimeMoverCleanUp::maybeCleanUpFallBackUserMeta method: Array
(
    [prime_mover_excluded_settings_db] => 
    [prime_mover_user_enc_key_settings] => 
    [prime_mover_current_settings] => 
    [prime_mover_current_gearbox_packages] => 
)

2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Export location defined
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::logStopTracking method: Stop tracking requested
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::logStopTracking method: Export result:
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::logStopTracking method: Array
(
    [multisite_export_options] => development_package
    [prime_mover_export_targetid] => 1
    [prime_mover_export_type] => single-site-export
    [multisite_export_location] => export_directory
    [prime_mover_encrypt_db] => true
    [prime_mover_force_utf8] => false
    [prime_mover_dropbox_upload] => false
    [prime_mover_userexport_setting] => false
    [prime_mover_gdrive_upload] => false
    [export_start_time] => 1757645636.167
    [prime_mover_is_super_db_user] => 1
    [prime_mover_db_dump_size] => 5000
    [prime_mover_req_secure_transport] => OFF
    [original_blogid] => 1
    [enable_db_encryption] => 1
    [temp_folder_path] => /var/www/html/wp-content/uploads/prime-mover-export-files/1/thebibbvYSIyhsnbVrHp9VXblogid_1/
    [target_zip_path] => /var/www/html/wp-content/uploads/prime-mover-export-files/1/thebibbvYSIyhsnbVrHp9VXblogid_1.wprime
    [wprime_readme_path] => /var/www/html/wp-content/uploads/prime-mover-export-files/1/thebibbvYSIyhsnbVrHp9VXblogid_1/wprime-readme.txt
    [autobackup_init_user_metas] => Array
        (
            [0] => auto_backup_prime_mover_initb86122ff
            [1] => auto_backup_prime_mover_b86122ff
        )

    [peak_memory_usage_log] => Array
        (
            [0] => 7334840
            [1] => 7609520
            [2] => 7379304
            [3] => 7402200
            [4] => 7401104
            [5] => 7401064
            [6] => 7400984
            [7] => 7400944
            [8] => 7400848
            [9] => 7401072
            [10] => 7401016
            [11] => 7400968
            [12] => 7400272
            [13] => 7400192
            [14] => 7400160
            [15] => 7400080
            [16] => 7400040
            [17] => 7400608
            [18] => 7400528
            [19] => 7400496
            [20] => 7400048
            [21] => 7399968
            [22] => 7399888
            [23] => 7399832
            [24] => 7399776
            [25] => 7399744
            [26] => 7399664
            [27] => 7399600
            [28] => 7400536
            [29] => 7399312
            [30] => 7400584
            [31] => 7340080
            [32] => 7398584
            [33] => 7418208
            [34] => 7342600
        )

    [mayberandomizedbprefix] => 1
    [randomizedbprefixstring] => hrniwx0_
    [autouser_id_adjust] => Array
        (
            [0] => Array
                (
                    [geodir_attachments] => Array
                        (
                            [primary] => ID
                            [column] => user_id
                            [unique] => no
                        )

                )

            [1] => Array
                (
                    [geodir_post_review] => Array
                        (
                            [primary] => id
                            [column] => user_id
                            [unique] => no
                        )

                )

            [2] => Array
                (
                    [mailpoet_user_flags] => Array
                        (
                            [primary] => id
                            [column] => user_id
                            [unique] => no
                        )

                )

            [3] => Array
                (
                    [wfls_2fa_secrets] => Array
                        (
                            [primary] => id
                            [column] => user_id
                            [unique] => no
                        )

                )

            [4] => Array
                (
                    [wpforms_entries] => Array
                        (
                            [primary] => entry_id
                            [column] => user_id
                            [unique] => no
                        )

                )

            [5] => Array
                (
                    [wpforms_entry_meta] => Array
                        (
                            [primary] => id
                            [column] => user_id
                            [unique] => no
                        )

                )

            [6] => Array
                (
                    [wpforms_logs] => Array
                        (
                            [primary] => id
                            [column] => user_id
                            [unique] => no
                        )

                )

        )

    [tbl_specific_collations] => Array
        (
            [0] => utf8mb4_unicode_ci
            [1] => latin1_swedish_ci
            [2] => utf8_general_ci
        )

    [db_port] => 3306
    [pdo_connection_mode] => no_port_conn
    [dump_percent_progress] => 94%
    [php_db_dump_original_table_row_counts] => 555640
    [php_db_dump_ongoing_rows_dumped] => 519697
    [db_dump_in_progress] => 1
    [php_db_dump_clean_tables] => Array
        (
            [103] => wp_wfknownfilelist
            [104] => wp_wflivetraffichuman
            [105] => wp_wflocs
            [106] => wp_wflogins
            [107] => wp_wfls_2fa_secrets
            [108] => wp_wfls_role_counts
            [109] => wp_wfls_settings
            [110] => wp_wfnotifications
            [111] => wp_wfpendingissues
            [112] => wp_wfreversecache
            [113] => wp_wfsecurityevents
            [114] => wp_wfsnipcache
            [115] => wp_wfstatus
            [116] => wp_wftrafficrates
            [117] => wp_wfwaffailures
            [118] => wp_wpforms_entries
            [119] => wp_wpforms_entry_fields
            [120] => wp_wpforms_entry_meta
            [121] => wp_wpforms_file_restrictions
            [122] => wp_wpforms_logs
            [123] => wp_wpforms_payment_meta
            [124] => wp_wpforms_payments
            [125] => wp_wpforms_protected_files
            [126] => wp_wpforms_tasks_meta
            [127] => wp_wpie_template
            [128] => wp_yoast_indexable
            [129] => wp_yoast_indexable_hierarchy
            [130] => wp_yoast_migrations
            [131] => wp_yoast_primary_term
            [132] => wp_yoast_seo_links
        )

    [php_db_dump_index_to_resume] => 10000
    [php_db_dump_left_off] => Array
        (
            [0] => 10000
        )

    [completed_target_dump_path] => /var/www/html/wp-content/uploads/prime-mover-export-files/1/thebibbvYSIyhsnbVrHp9VXblogid_1/1.sql.enc
    [tar_encrypt_iv] => 
    [export_system_footprint] => Array
        (
            [plugins] => Array
                (
                    [prime-mover-pro/prime-mover.php] => 2.0.8
                    [add-search-to-menu/add-search-to-menu.php] => 5.5.11
                    [adrotate-pro/adrotate-pro.php] => 5.7.3
                    [classic-editor/classic-editor.php] => 1.6.7
                    [code-snippets/code-snippets.php] => 3.7.0
                    [external-media-without-import/external-media-without-import.php] => 1.1.2
                    [featured-image-from-url/featured-image-from-url.php] => 5.2.7
                    [flying-press/flying-press.php] => 5.0.7
                    [guest-author/index.php] => 2.6
                    [leaky-paywall/leaky-paywall.php] => 4.22.3
                    [list-category-posts/list-category-posts.php] => 0.91.0
                    [mailpoet/mailpoet.php] => 5.14.2
                    [one-user-avatar/one-user-avatar.php] => 2.5.0
                    [pdf-embedder/pdf_embedder.php] => 4.9.2
                    [real-media-library/index.php] => 4.22.54
                    [really-simple-ssl-pro/really-simple-ssl-pro.php] => 9.4.1
                    [slider-image/slider.php] => 4.0.6
                    [sticky-toc-advance-table-of-contents/sticky-toc-advance-table-of-contents.php] => 1.0.2
                    [styler-for-wpforms/styler-for-wpforms.php] => 3.6
                    [twitter-cards-meta/twitter-cards-meta.php] => 2.9.1
                    [user-menus/user-menus.php] => 1.3.2
                    [weather-atlas/weather-atlas.php] => 3.0.4
                    [wordfence/wordfence.php] => 8.1.0
                    [wp-custom-avatar/wp-custom-avatar.php] => 1.2.1
                    [wp-import-export-lite/wp-import-export-lite.php] => 3.9.30
                    [wp-php-version-display/wp-php-version-display.php] => 2.0
                    [prime-mover/prime-mover.php] => 2.0.8
                )

            [stylesheet] => Array
                (
                    [Newspaper] => 7.3
                )

            [template] => Array
                (
                    [Newspaper] => 7.3
                )

            [using_child_theme] => no
            [footprint_blog_id] => 1
            [site_url] => bibbvoice7.com.local
            [wp_root] => /var/www/html/
            [db_prefix] => hrniwx0_
            [scheme] => https://
            [upload_information_path] => /var/www/html/wp-content/uploads
            [upload_information_url] => https://bibbvoice7.com.local/wp-content/uploads
            [prime_mover_plugin_version] => 2.0.8
            [exported_db_tables] => Array
                (
                    [0] => hrniwx0_actionscheduler_actions
                    [1] => hrniwx0_actionscheduler_claims
                    [2] => hrniwx0_actionscheduler_groups
                    [3] => hrniwx0_actionscheduler_logs
                    [4] => hrniwx0_adrotate
                    [5] => hrniwx0_adrotate_groups
                    [6] => hrniwx0_adrotate_linkmeta
                    [7] => hrniwx0_adrotate_schedule
                    [8] => hrniwx0_adrotate_stats
                    [9] => hrniwx0_adrotate_stats_archive
                    [10] => hrniwx0_adrotate_tracker
                    [11] => hrniwx0_commentmeta
                    [12] => hrniwx0_comments
                    [13] => hrniwx0_fifu_invalid_media_su
                    [14] => hrniwx0_fifu_meta_in
                    [15] => hrniwx0_fifu_meta_out
                    [16] => hrniwx0_geodir_attachments
                    [17] => hrniwx0_geodir_countries
                    [18] => hrniwx0_geodir_custom_fields
                    [19] => hrniwx0_geodir_custom_sort_fields
                    [20] => hrniwx0_geodir_gd_place_detail
                    [21] => hrniwx0_geodir_post_icon
                    [22] => hrniwx0_geodir_post_review
                    [23] => hrniwx0_huge_itslider_images_backup
                    [24] => hrniwx0_huge_itslider_params_backup
                    [25] => hrniwx0_huge_itslider_sliders_backup
                    [26] => hrniwx0_hugeit_slider_slide
                    [27] => hrniwx0_hugeit_slider_slider
                    [28] => hrniwx0_links
                    [29] => hrniwx0_mailpoet_automation_run_logs
                    [30] => hrniwx0_mailpoet_automation_run_subjects
                    [31] => hrniwx0_mailpoet_automation_runs
                    [32] => hrniwx0_mailpoet_automation_triggers
                    [33] => hrniwx0_mailpoet_automation_versions
                    [34] => hrniwx0_mailpoet_automations
                    [35] => hrniwx0_mailpoet_custom_fields
                    [36] => hrniwx0_mailpoet_dynamic_segment_filters
                    [37] => hrniwx0_mailpoet_feature_flags
                    [38] => hrniwx0_mailpoet_forms
                    [39] => hrniwx0_mailpoet_log
                    [40] => hrniwx0_mailpoet_mapping_to_external_entities
                    [41] => hrniwx0_mailpoet_migrations
                    [42] => hrniwx0_mailpoet_newsletter_links
                    [43] => hrniwx0_mailpoet_newsletter_option
                    [44] => hrniwx0_mailpoet_newsletter_option_fields
                    [45] => hrniwx0_mailpoet_newsletter_posts
                    [46] => hrniwx0_mailpoet_newsletter_segment
                    [47] => hrniwx0_mailpoet_newsletter_templates
                    [48] => hrniwx0_mailpoet_newsletters
                    [49] => hrniwx0_mailpoet_scheduled_task_subscribers
                    [50] => hrniwx0_mailpoet_scheduled_tasks
                    [51] => hrniwx0_mailpoet_segments
                    [52] => hrniwx0_mailpoet_sending_queues
                    [53] => hrniwx0_mailpoet_settings
                    [54] => hrniwx0_mailpoet_statistics_bounces
                    [55] => hrniwx0_mailpoet_statistics_clicks
                    [56] => hrniwx0_mailpoet_statistics_forms
                    [57] => hrniwx0_mailpoet_statistics_newsletters
                    [58] => hrniwx0_mailpoet_statistics_opens
                    [59] => hrniwx0_mailpoet_statistics_unsubscribes
                    [60] => hrniwx0_mailpoet_statistics_woocommerce_purchases
                    [61] => hrniwx0_mailpoet_stats_notifications
                    [62] => hrniwx0_mailpoet_subscriber_custom_field
                    [63] => hrniwx0_mailpoet_subscriber_ips
                    [64] => hrniwx0_mailpoet_subscriber_segment
                    [65] => hrniwx0_mailpoet_subscriber_tag
                    [66] => hrniwx0_mailpoet_subscribers
                    [67] => hrniwx0_mailpoet_tags
                    [68] => hrniwx0_mailpoet_user_agents
                    [69] => hrniwx0_mailpoet_user_flags
                    [70] => hrniwx0_options
                    [71] => hrniwx0_postmeta
                    [72] => hrniwx0_posts
                    [73] => hrniwx0_realmedialibrary
                    [74] => hrniwx0_realmedialibrary_meta
                    [75] => hrniwx0_realmedialibrary_posts
                    [76] => hrniwx0_realmedialibrary_tmp
                    [77] => hrniwx0_rsssl_csp_log
                    [78] => hrniwx0_rsssl_event_logs
                    [79] => hrniwx0_rsssl_geo_block
                    [80] => hrniwx0_rsssl_xmlrpc
                    [81] => hrniwx0_snippets
                    [82] => hrniwx0_tasks
                    [83] => hrniwx0_term_relationships
                    [84] => hrniwx0_term_taxonomy
                    [85] => hrniwx0_termmeta
                    [86] => hrniwx0_terms
                    [87] => hrniwx0_w2dc_content_fields
                    [88] => hrniwx0_w2dc_content_fields_groups
                    [89] => hrniwx0_w2dc_levels
                    [90] => hrniwx0_w2dc_levels_relationships
                    [91] => hrniwx0_w2dc_locations_levels
                    [92] => hrniwx0_w2dc_locations_relationships
                    [93] => hrniwx0_wfauditevents
                    [94] => hrniwx0_wfblockediplog
                    [95] => hrniwx0_wfblocks7
                    [96] => hrniwx0_wfconfig
                    [97] => hrniwx0_wfcrawlers
                    [98] => hrniwx0_wffilechanges
                    [99] => hrniwx0_wffilemods
                    [100] => hrniwx0_wfhits
                    [101] => hrniwx0_wfhoover
                    [102] => hrniwx0_wfissues
                    [103] => hrniwx0_wfknownfilelist
                    [104] => hrniwx0_wflivetraffichuman
                    [105] => hrniwx0_wflocs
                    [106] => hrniwx0_wflogins
                    [107] => hrniwx0_wfls_2fa_secrets
                    [108] => hrniwx0_wfls_role_counts
                    [109] => hrniwx0_wfls_settings
                    [110] => hrniwx0_wfnotifications
                    [111] => hrniwx0_wfpendingissues
                    [112] => hrniwx0_wfreversecache
                    [113] => hrniwx0_wfsecurityevents
                    [114] => hrniwx0_wfsnipcache
                    [115] => hrniwx0_wfstatus
                    [116] => hrniwx0_wftrafficrates
                    [117] => hrniwx0_wfwaffailures
                    [118] => hrniwx0_wpforms_entries
                    [119] => hrniwx0_wpforms_entry_fields
                    [120] => hrniwx0_wpforms_entry_meta
                    [121] => hrniwx0_wpforms_file_restrictions
                    [122] => hrniwx0_wpforms_logs
                    [123] => hrniwx0_wpforms_payment_meta
                    [124] => hrniwx0_wpforms_payments
                    [125] => hrniwx0_wpforms_protected_files
                    [126] => hrniwx0_wpforms_tasks_meta
                    [127] => hrniwx0_wpie_template
                    [128] => hrniwx0_yoast_indexable
                    [129] => hrniwx0_yoast_indexable_hierarchy
                    [130] => hrniwx0_yoast_migrations
                    [131] => hrniwx0_yoast_primary_term
                    [132] => hrniwx0_yoast_seo_links
                )

            [site_title] => The Bibb Voice
            [auto_user_adjustment] => Array
                (
                    [0] => Array
                        (
                            [geodir_attachments] => Array
                                (
                                    [primary] => ID
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                    [1] => Array
                        (
                            [geodir_post_review] => Array
                                (
                                    [primary] => id
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                    [2] => Array
                        (
                            [mailpoet_user_flags] => Array
                                (
                                    [primary] => id
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                    [3] => Array
                        (
                            [wfls_2fa_secrets] => Array
                                (
                                    [primary] => id
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                    [4] => Array
                        (
                            [wpforms_entries] => Array
                                (
                                    [primary] => entry_id
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                    [5] => Array
                        (
                            [wpforms_entry_meta] => Array
                                (
                                    [primary] => id
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                    [6] => Array
                        (
                            [wpforms_logs] => Array
                                (
                                    [primary] => id
                                    [column] => user_id
                                    [unique] => no
                                )

                        )

                )

            [tbl_specific_collations] => Array
                (
                    [0] => utf8mb4_unicode_ci
                    [1] => latin1_swedish_ci
                    [2] => utf8_general_ci
                )

            [source_content_url] => https://bibbvoice7.com.local/wp-content
            [source_content_dir] => /var/www/html/wp-content
        )

    [include_users] => 
    [original_plugins_count] => 25
    [total_media_files] => 18597
    [plugins_to_list] => Array
        (
        )

    [original_themes_count] => 1
    [themes_to_list] => Array
        (
        )

    [wprime_closed] => 1
    [download_url] => https://bibbvoice7.com.local/wp-admin/?prime_mover_export_hash=879358e5ca7b4a5530bbdb44dd4d9b0e086641dc9c04a74e1568c5003d985e72&prime_mover_blogid=1
    [generated_filename] => thebibbvoice_09-12-2025_0256am_blogid_1.wprime
    [generated_archive_path] => 1/thebibbvYSIyhsnbVrHp9VXblogid_1.wprime
)

2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Saving export result output:
2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverSystemProcessors::primeMoverExportProcessor method: Array
(
    [status] => 1
    [export_location] => export_directory
    [restore_url] => https://bibbvoice7.com.local/wp-admin/?prime_mover_export_hash=879358e5ca7b4a5530bbdb44dd4d9b0e086641dc9c04a74e1568c5003d985e72&prime_mover_blogid=1
    [message] => Export saved !        
        <button style="display:none" type="button" class="js-prime-mover-copy-clipboard button-link" 
        id="js-prime-mover-copy-url-clipboard-1" data-clipboard-text="" data-clipboard-blogid="1">
            Copy URL to clipboard        </button>            
    
)

2025-09-12 02:56:32 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::updateTrackerProgress method: Update tracker progress: updateSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e, stoptracking);
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_progress_processor: Validation success
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker count: 21
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Tracker progress non-cached request
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: getSiteOption(eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e) = stoptracking
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Getting latest progress: stoptracking with process id: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Standard process detected: stoptracking
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonProgressProcessor method: Array
(
    [export_status] => stoptracking
    [export_result] => Array
        (
            [status] => 1
            [export_location] => export_directory
            [restore_url] => https://bibbvoice7.com.local/wp-admin/?prime_mover_export_hash=879358e5ca7b4a5530bbdb44dd4d9b0e086641dc9c04a74e1568c5003d985e72&prime_mover_blogid=1
            [message] => Export saved !        
        <button style="display:none" type="button" class="js-prime-mover-copy-clipboard button-link" 
        id="js-prime-mover-copy-url-clipboard-1" data-clipboard-text="" data-clipboard-blogid="1">
            Copy URL to clipboard        </button>            
    
            [process_id] => eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
        )

    [download_result] => 
)

2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverValidationHandlers::validateInputParameters method: common_shutdown_processor: Validation success
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonShutDownProcessor method: Shutdown request fully validated, ready to shutdown export process
2025-09-12 02:56:35 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteImportId method: Deleted import ID:  eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteExportId method: Deleted export ID:  eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteExportResultId method: Deleted export result ID: eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteImportResultId method: Deleted import result ID:  import_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteDownloadResultId method: Deleted download result ID: download_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteDownloadSizeId method: Deleted download size ID: download_size_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged import event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteDownloadTmpFileId method: Deleted download tmp file ID: download_tmp_path_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteUploadDropBoxSize method: Deleted dropbox upload size ID: dropboxupload_size_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::primeMoverDeleteUploadGdriveSize method: Deleted GDrive upload size ID: gdriveupload_size_eb114d854d8b804920ad64815f38ac6872111dcc5428c7c09bd0db9817dae81e
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonShutDownProcessor method: Shutdown request completed, response:
2025-09-12 02:56:35 => Logged export event for blog ID 1 from Codexonics\PrimeMoverFramework\classes\PrimeMoverProgressHandlers::commonShutDownProcessor method: Array
(
    [status] => shutdown_processed
)

