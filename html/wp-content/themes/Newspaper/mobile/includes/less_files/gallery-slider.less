/*  ----------------------------------------------------------------------------
    less for gallery slider
*/
.td-gallery-slider {
  margin-left: -15px;
  margin-right: -15px;
}

.post_td_gallery {
  font-family: @font1;
  color: #fff;
  margin-bottom: 24px;
  background-color: #222;
  overflow: hidden;
  clear: both;
  position: relative;
}

.td-gallery-slide-top {
  position: relative;
  min-height: 60px;
  background-color: #111;
}

.td-gallery-title {
  font-size: 22px;
  line-height: 24px;
  font-weight: bold;
  padding: 0 15px;
  text-align: center;
}

/* controls wrapper */
.td-gallery-controls-wrapper {
  height: 40px;
  margin: auto !important;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
}

.td-gallery-slide-count {
  display: block;
  font-style: italic;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 3px;
}

.td-gallery-slide-prev-next-but {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  display: block;
  z-index: 2;
  text-align: center;
  height: 50px;
  color: #fff;
  width: 100%;
  opacity: 0.7;
  pointer-events: none;

  .td-icon-left {
    font-size: 40px;
    padding: 5px 17px 2px 10px;
    float: left;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: auto;
  }

  .td-icon-right {
    font-size: 40px;
    padding: 5px 10px 2px 17px;
    float: right;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: auto;
  }
}

.td-gallery-slide-copywrite {
  float: right;
  padding: 6px 15px;
  font-style: italic;
  line-height: 15px;
  font-size: 11px;
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.8);
  text-align: center;
  width: 100%;
}

.td-button {
  margin: 0 4.2px 0 4.3px;
}

.td-doubleSlider-1 {
  width: auto;
  height: 240px;

  .td-slide-galery-figure {
    text-align: center;
    position: relative;
    width: 100%;
    height: 240px;

  }


  .td-slide-item img{
    max-height: 100%;
    max-width: 100%;
  }

  .td-slide-item {
    width: 100%;

    .td-slide-galery-figure img {
      margin: auto;
      overflow: auto;
      position: absolute;
      left: -50%;
      right: -50%;
      top: -50%;
      bottom: -50%;
    }

    .td-slide-caption {
      position: absolute;
      bottom: 0;
      left: 0;
      text-align: left;

      span {
        font-family: @font1;
        font-size: 12px;
        color: #fff;
        line-height: 18px;
        width: 100%;
        float: left;
        padding: 5px 10px;
        background-color: rgba(0, 0, 0, 0.6);
      }

      /* if caption or description text is present */
      &.td-gallery-slide-content {
        width: 100%;
      }
    }
  }

}//end td-doubleSlider-1


// td-doubleSlider-2 removed in mobile theme
