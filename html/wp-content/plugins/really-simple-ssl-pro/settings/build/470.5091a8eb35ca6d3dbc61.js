"use strict";(self.webpackChunkreally_simple_ssl=self.webpackChunkreally_simple_ssl||[]).push([[470],{5470:(e,s,l)=>{l.r(s),l.d(s,{default:()=>d});var m=l(790);const i=()=>(0,m.jsxs)("div",{className:"rsssl-wizard-menu rsssl-grid-item rsssl-menu-placeholder",children:[(0,m.jsx)("div",{className:"rsssl-grid-item-header",children:(0,m.jsx)("h1",{className:"rsssl-h4"})}),(0,m.jsx)("div",{className:"rsssl-grid-item-content"})]});var n=l(7723),r=l(7135);const a=e=>{const{selectedSubMenuItem:s,selectedMainMenuItem:l,subMenu:i,menu:t}=(0,r.A)(),d=u(s,e.menuItem);let c=d?" rsssl-active":"";c+=e.menuItem.featured?" rsssl-featured":"",c+=e.menuItem.new?" rsssl-new":"",c+=e.menuItem.premium&&!rsssl_settings.pro_plugin_active?" rsssl-premium":"";let h=e.menuItem.directLink||"#"+l+"/"+e.menuItem.id;return(0,m.jsx)(m.Fragment,{children:e.menuItem.visible&&(0,m.jsxs)(m.Fragment,{children:[e.isMainMenu?(0,m.jsx)("div",{className:"rsssl-main-menu",children:(0,m.jsx)("div",{className:"rsssl-menu-item"+c,children:(0,m.jsxs)("a",{href:h,children:[(0,m.jsx)("span",{children:e.menuItem.title}),e.menuItem.featured&&(0,m.jsx)("span",{className:"rsssl-menu-item-beta-pill",children:(0,n.__)("Beta","really-simple-ssl")}),e.menuItem.new&&(0,m.jsx)("span",{className:"rsssl-menu-item-new-pill",children:(0,n.__)("New","really-simple-ssl")})]})})}):(0,m.jsx)("div",{className:"rsssl-menu-item"+c,children:(0,m.jsxs)("a",{href:h,children:[(0,m.jsx)("span",{children:e.menuItem.title}),e.menuItem.featured&&(0,m.jsx)("span",{className:"rsssl-menu-item-beta-pill",children:(0,n.__)("Beta","really-simple-ssl")}),e.menuItem.new&&(0,m.jsx)("span",{className:"rsssl-menu-item-new-pill",children:(0,n.__)("New","really-simple-ssl")})]})}),e.menuItem.menu_items&&d&&(0,m.jsx)("div",{className:"rsssl-submenu-item",children:(j=e.menuItem.menu_items,Array.isArray(j)?j:[j]).map(((e,s)=>e.visible&&(0,m.jsx)(a,{menuItem:e,isMainMenu:!1},"submenuItem"+s)))})]})});var j},t=a,u=(e,s)=>{if(e===s.id)return!0;if(s.menu_items)for(const l of s.menu_items)if(l.id===e)return!0;return!1},d=()=>{const{subMenu:e,subMenuLoaded:s}=(0,r.A)();return s?(0,m.jsxs)("div",{className:"rsssl-wizard-menu rsssl-grid-item",children:[(0,m.jsx)("div",{className:"rsssl-grid-item-header",children:(0,m.jsx)("h1",{className:"rsssl-h4",children:e.title})}),(0,m.jsx)("div",{className:"rsssl-grid-item-content",children:(0,m.jsx)("div",{className:"rsssl-wizard-menu-items",children:e.menu_items.map(((e,s)=>(0,m.jsx)(t,{menuItem:e,isMainMenu:!0},"menuItem-"+s)))})}),(0,m.jsx)("div",{className:"rsssl-grid-item-footer"})]}):(0,m.jsx)(i,{})}}}]);