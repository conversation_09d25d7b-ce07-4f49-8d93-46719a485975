{"translation-revision-date": "2025-06-11 11:04+0000", "generator": "WP-CLI/2.11.0", "source": "settings/src/Settings/RiskConfiguration/NotificationTester.js", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "lang": "fr_FR", "plural-forms": "nplurals=2; plural=(n > 1);"}, "All notifications are triggered successfully, please check your email to double-check if you can receive emails.": ["Toutes les notifications bien été déclenchées. Veuillez vérifier votre boîte pour vous assurer que vous pouvez recevoir des e-mails."], "Test notifications": ["Notifications de test"], "The notification test only works if you save the setting first.": ["Le test de notification fonctionne uniquement si vous enregistrez d’abord le réglage."], "You have not enabled the email notifications in the general settings.": ["Vous n’avez pas activé les notifications par e-mail dans les réglages généraux."]}}}