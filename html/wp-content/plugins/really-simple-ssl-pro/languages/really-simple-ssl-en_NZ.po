msgid ""
msgstr ""
"Project-Id-Version: really-simple-ssl-pro 9.4.1\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: English (UK)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-03-15 12:37+0000\n"
"PO-Revision-Date: 2025-06-11 11:04+0000\n"
"Language: en_GB\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.8.0; wp-6.8.1; php-8.1.32\n"

#: pro/security/wordpress/permission-detection/permission-detection.php:432
msgid " Insecure file permissions detected on your server. Click “Fix” to let Really Simple Security resolve this, or %sdownload%s the affected files list, to resolve this manually."
msgstr " Insecure file permissions detected on your server. Click “Fix” to let Really Simple Security resolve this, or %sdownload%s the affected files list, to resolve this manually."

#: settings/config/menu.php:710
msgid " Please note that you can always save and finish the wizard later, use our %sdocumentation%s for additional information or log a %ssupport ticket%s if you need our assistance."
msgstr " Please note that you can always save and finish the wizard later, use our %sdocumentation%s for additional information or log a %ssupport ticket%s if you need our assistance."

#. translators: %1$s: The date the password expires. %2$d: The number of days until the password expires.
#: pro/security/wordpress/class-rsssl-password-security.php:191
msgid "%1$s - expires in %2$d days"
msgstr "%1$s - expires in %2$d days"

#: pro/security/wordpress/class-rsssl-limit-login-admin.php:268
msgid "%1s %2$s added to %3$s."
msgstr "%1s %2$s added to %3$s."

#: lets-encrypt/class-letsencrypt-handler.php:611
msgid "%d seconds"
msgstr "%d seconds"

#: lets-encrypt/class-letsencrypt-handler.php:609
msgid "%d:%02d minutes"
msgstr "%d:%02d minutes"

#: lets-encrypt/class-letsencrypt-handler.php:605
msgid "%d:%02d:%02d hours"
msgstr "%d:%02d:%02d hours"

#: settings/config/fields/limit-login-attempts.php:58
#: settings/config/fields/limit-login-attempts.php:82
#: settings/config/fields/two-fa.php:75
msgid "%s day"
msgstr "%s day"

#: settings/config/fields/two-fa.php:76
#: settings/config/fields/two-fa.php:77
#: settings/config/fields/two-fa.php:78
msgid "%s days"
msgstr "%s days"

#: settings/config/fields/limit-login-attempts.php:56
#: settings/config/fields/limit-login-attempts.php:80
msgid "%s hour"
msgstr "%s hour"

#: settings/config/fields/limit-login-attempts.php:57
#: settings/config/fields/limit-login-attempts.php:81
msgid "%s hours"
msgstr "%s hours"

#: onboarding/class-onboarding.php:15
#: pro/class-admin.php:14
#: pro/class-headers.php:13
#: pro/class-importer.php:12
#: pro/class-scan.php:44
#: pro/csp-violation-endpoint.php:10
#: security/hardening.php:9
msgid "%s is a singleton class and you cannot create a second instance."
msgstr "%s is a singleton class and you cannot create a second instance."

#: settings/config/fields/limit-login-attempts.php:54
#: settings/config/fields/limit-login-attempts.php:55
#: settings/config/fields/limit-login-attempts.php:78
#: settings/config/fields/limit-login-attempts.php:79
msgid "%s minutes"
msgstr "%s minutes"

#: settings/config/fields/limit-login-attempts.php:84
msgid "%s month"
msgstr "%s month"

#: settings/config/fields/limit-login-attempts.php:85
msgid "%s months"
msgstr "%s months"

#. translators: %s: Name of the country that was removed from the blocked list.
#: pro/security/wordpress/class-rsssl-geo-block.php:800
#: pro/security/wordpress/class-rsssl-geo-block.php:849
msgid "%s not in the list."
msgstr "%s not in the list."

#. translators: %s: count
#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:202
#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:325
msgid "%s unused code remaining."
msgid_plural "%s unused codes remaining."
msgstr[0] "%s unused code remaining."
msgstr[1] "%s unused codes remaining."

#. translators: %s: The captcha provider name.
#: pro/security/wordpress/class-rsssl-captcha-config.php:134
msgid "%s validation failed"
msgstr "%s validation failed"

#: security/wordpress/vulnerabilities.php:324
msgid "%s vulnerability has been detected."
msgid_plural "%s vulnerabilities have been detected."
msgstr[0] "%s vulnerability has been detected."
msgstr[1] "%s vulnerabilities have been detected."

#: settings/config/fields/limit-login-attempts.php:83
msgid "%s week"
msgstr "%s week"

#: pro/security/wordpress/vulnerabilities-pro.php:685
msgid "%s, Force Update: End Process"
msgstr "%s, Force Update: End Process"

#: pro/security/wordpress/vulnerabilities-pro.php:626
msgid "%s, Force Update: Failed"
msgstr "%s, Force Update: Failed"

#: pro/security/wordpress/vulnerabilities-pro.php:597
msgid "%s, Force Update: Scheduled."
msgstr "%s, Force Update: Scheduled."

#: pro/security/wordpress/vulnerabilities-pro.php:656
msgid "%s, Force Update: Successful"
msgstr "%s, Force Update: Successful"

#: pro/security/wordpress/vulnerabilities-pro.php:716
msgid "%s, Quarantine: Scheduled"
msgstr "%s, Quarantine: Scheduled"

#: pro/security/wordpress/vulnerabilities-pro.php:749
msgid "%s, Quarantine: Successful"
msgstr "%s, Quarantine: Successful"

#: pro/class-licensing.php:545
#: pro/class-licensing.php:641
msgid "%s/%s activations available."
msgstr "%s/%s activations available."

#: security/wordpress/vulnerabilities.php:1534
msgid "%s: %s vulnerability found"
msgid_plural "%s: %s vulnerabilities found"
msgstr[0] "%s: %s vulnerability found"
msgstr[1] "%s: %s vulnerabilities found"

#: pro/class-licensing.php:714
msgid "%sActivate%s your license."
msgstr "%sActivate%s your licence."

#: pro/class-scan.php:214
msgctxt "'date' at 'time'"
msgid "%s at %s"
msgstr "%s at %s"

#: settings/config/fields/security-headers.php:15
msgid "(recommended)"
msgstr "(recommended)"

#: class-admin.php:2002
#: lets-encrypt/class-letsencrypt-handler.php:290
msgid "(unknown)"
msgstr "(unknown)"

#: security/notices.php:40
msgid ".htaccess does not exist"
msgstr ".htaccess does not exist"

#: class-wp-cli.php:1223
msgid ".htaccess file (%s) is not writable. Redirects cannot be configured automatically."
msgstr ".htaccess file (%s) is not writable. Redirects cannot be configured automatically."

#: security/notices.php:59
msgid ".htaccess in uploads not writable"
msgstr ".htaccess in uploads not writable"

#: security/notices.php:32
msgid ".htaccess not writable"
msgstr ".htaccess not writable"

#: class-admin.php:2222
msgid ".htaccess redirect."
msgstr ".htaccess redirect."

#: settings/config/fields/firewall.php:131
msgid "1 day"
msgstr "1 day"

#: settings/config/fields/firewall.php:129
msgid "1 hour"
msgstr "1 hour"

#: settings/config/fields/access-control.php:60
msgid "1 year"
msgstr "1 year"

#: settings/config/fields/access-control.php:61
msgid "2 years"
msgstr "2 years"

#: settings/config/fields/firewall.php:128
msgid "30 minutes"
msgstr "30 minutes"

#: class-admin.php:2203
#: class-admin.php:2209
msgid "301 .htaccess redirect"
msgstr "301 .htaccess redirect"

#: settings/config/fields/encryption.php:19
msgid "301 .htaccess redirect (read instructions first)"
msgstr "301 .htaccess redirect (read instructions first)"

#: class-site-health.php:365
msgid "301 .htaccess redirect is not enabled."
msgstr "301 .htaccess redirect is not enabled."

#: settings/config/fields/encryption.php:18
msgid "301 PHP redirect"
msgstr "301 PHP redirect"

#: class-admin.php:2184
msgid "301 redirect to https set."
msgstr "301 redirect to https set."

#: class-site-health.php:312
msgid "301 SSL redirect enabled"
msgstr "301 SSL redirect enabled"

#: settings/config/fields/firewall.php:130
msgid "4 hours"
msgstr "4 hours"

#: settings/config/menu.php:555
#: settings/config/menu.php:556
msgid "404 Blocking"
msgstr "404 Blocking"

#: class-site-health.php:241
#: settings/config/disable-fields-filter.php:57
msgid "404 errors detected on your homepage"
msgstr "404 errors detected on your homepage"

#: class-admin.php:2421
#: settings/config/fields/firewall.php:104
#: settings/config/fields/firewall.php:126
#: settings/config/fields/firewall.php:148
msgid "404 errors detected on your homepage. 404 blocking is unavailable, to prevent blocking of legitimate visitors. It is strongly recommended to resolve these errors."
msgstr "404 errors detected on your homepage. 404 blocking is unavailable, to prevent blocking of legitimate visitors. It is strongly recommended to resolve these errors."

#: class-site-health.php:249
msgid "404 errors detected on your homepage. This means that the page requests images, scripts or other resources that are no longer available. It can interfere with your Firewall as well."
msgstr "404 errors detected on your homepage. This means that the page requests images, scripts or other resources that are no longer available. This could interfere with the Firewall as well."

#: pro/security/wordpress/firewall/models/class-rsssl-404-block.php:173
msgid "404 threshold exceeded"
msgstr "404 threshold exceeded"

#: settings/config/fields/access-control.php:79
msgid "48 hours (default)"
msgstr "48 hours (default)"

#: settings/config/fields/access-control.php:59
msgid "6 months"
msgstr "6 months"

#: settings/config/fields/access-control.php:78
msgid "8 hours (recommended)"
msgstr "8 hours (recommended)"

#: pro/security/wordpress/class-rsssl-event-listener.php:174
msgid "<strong>Error</strong>: Captcha validation failed."
msgstr "<strong>Error</strong>: Captcha validation failed."

#: security/wordpress/vulnerabilities.php:1568
msgid "A %s vulnerability is found in %s."
msgstr "A %s vulnerability is found in %s."

#: settings/config/menu.php:143
msgid "A correctly configured Content Security Policy can protect your visitors from the most common web attacks. It all starts with denying and upgrading insecure requests on your website."
msgstr "A correctly configured Content Security Policy can protect your visitors from the most common web attacks. It all starts with denying and upgrading insecure requests on your website."

#: settings/config/fields/hardening-extended.php:14
msgid "A debug.log is publicly accessibile and has a standard location. This will change the location to a randomly named folder in /wp-content/"
msgstr "A debug.log is publicly accessibile and has a standard location. This will change the location to a randomly named folder in /wp-content/"

#: class-admin.php:2033
msgid "A definition of a site url or home url was detected in your wp-config.php, but the file is not writable."
msgstr "A definition of a site url or home url was detected in your wp-config.php, but the file is not writable."

#: security/firewall-manager.php:633
msgid "A firewall rule was enabled, but /the wp-content/ folder is not writable."
msgstr "A firewall rule was enabled, but /the wp-content/ folder is not writable."

#: security/firewall-manager.php:649
msgid "A firewall rule was enabled, but the firewall does not seem to get loaded correctly."
msgstr "A firewall rule was enabled, but the firewall does not seem to get loaded correctly."

#: security/firewall-manager.php:627
msgid "A firewall rule was enabled, but the wp-config.php is not writable."
msgstr "A firewall rule was enabled, but the wp-config.php is not writable."

#: settings/config/fields/firewall.php:101
msgid "A lockout will occur if an IP address exceeds the threshold within the given timeframe. Select ‘%s’ if you want to disable 404 blocking."
msgstr "A lockout will occur if an IP address exceeds the threshold within the given timeframe. Select ‘%s’ if you want to disable 404 blocking."

#: class-multisite.php:111
msgid "A networkwide SSL activation process has been started, but has not been completed. Please go to the SSL settings page to complete the process."
msgstr "A networkwide SSL activation process has been started, but has not been completed. Please go to the SSL settings page to complete the process."

#: pro/security/wordpress/vulnerabilities-pro.php:751
msgid "A quarantine for %s has been successful on %s."
msgstr "A quarantine for %s has been successful on %s."

#: pro/security/wordpress/vulnerabilities-pro.php:718
msgid "A quarantine for %s scheduled on %s."
msgstr "A quarantine for %s scheduled on %s."

#: pro/class-admin.php:621
msgid "A redirect to http was detected. This might result in redirect loops."
msgstr "A redirect to http was detected. This might result in redirect loops."

#: pro/csp-violation-endpoint.php:195
msgid "A required database table is missing. Please check if you have permissions to add this database table."
msgstr "A required database table is missing. Please check if you have permissions to add this database table."

#: pro/security/wordpress/block-admin-creation.php:331
msgid "A user account with administrator privileges was created outside the WordPress dashboard on %s"
msgstr "A user account with administrator privileges was created outside the WordPress dashboard on %s."

#: assets/templates/two_fa/profile-settings.php:119
msgid "A verification code has been sent to the email address associated with your account to verify functionality."
msgstr "A verification code has been sent to the email address associated with your account to verify functionality."

#: assets/templates/two_fa/onboarding.php:116
#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:214
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:354
msgid "A verification code has been sent to the email address associated with your account."
msgstr "A verification code has been sent to the email address associated with your account."

#: upgrade/upgrade-to-pro.php:78
msgid "Able to create destination folder"
msgstr "Able to create destination folder"

#: settings/config/fields/security-headers.php:170
msgid "About Cross Origin Policies"
msgstr "About Cross Origin Policies"

#: settings/config/fields/security-headers.php:24
msgid "About Essential Security Headers"
msgstr "About Essential Security Headers"

#: settings/config/fields/hardening-basic.php:19
msgid "About Hardening"
msgstr "About Hardening"

#: settings/config/fields/security-headers.php:85
msgid "About HTTP Strict Transport Security"
msgstr "About HTTP Strict Transport Security"

#: settings/config/fields/limit-login-attempts.php:17
msgid "About Limit Login Attempts"
msgstr "About Limit Login Attempts"

#: mailer/class-mail.php:67
msgid "About notifications"
msgstr "About notifications"

#: settings/config/fields/security-headers.php:318
msgid "About the Content Security Policy"
msgstr "About the Content Security Policy"

#: settings/config/fields/encryption.php:89
msgid "About the Mixed Content Scan"
msgstr "About the Mixed Content Scan"

#: settings/config/fields/security-headers.php:220
msgid "About the Permission Policy"
msgstr "About the Permission Policy"

#: settings/config/fields/vulnerability-detection.php:21
msgid "About Vulnerabilities"
msgstr "About Vulnerabilities"

#: settings/config/fields/hardening-xml.php:37
msgid "About XML-RPC"
msgstr "About XML-RPC"

#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:275
#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:293
#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:323
msgid "Access denied."
msgstr "Access denied."

#. translators: %s: Name of the country that was removed from the blocked list.
#: pro/security/wordpress/class-rsssl-geo-block.php:825
msgid "Access from %s is now allowed."
msgstr "Access from %s is now allowed."

#. translators: %s: Name of the country that was removed from the blocked list.
#: pro/security/wordpress/class-rsssl-geo-block.php:663
msgid "Access from %s is now blocked."
msgstr "Access from %s is now blocked."

#. translators: %s: Name of the country that was removed from the blocked list.
#: pro/security/wordpress/class-rsssl-geo-block.php:473
msgid "Access from all countries in %s is now allowed."
msgstr "Access from all countries in %s is now allowed."

#. translators: %s: Name of the country that was removed from the blocked list.
#: pro/security/wordpress/class-rsssl-geo-block.php:353
msgid "Access from all countries in %s is now blocked."
msgstr "Access from all countries in %s is now blocked."

#: pro/security/wordpress/class-rsssl-geo-block.php:869
msgid "Access from the selected countries is now allowed."
msgstr "Access from the selected countries is now allowed."

#: pro/security/wordpress/class-rsssl-geo-block.php:704
msgid "Access from the selected countries is now blocked."
msgstr "Access from the selected countries is now blocked."

#: pro/security/wordpress/class-rsssl-geo-block.php:532
msgid "Access from the selected regions is now allowed."
msgstr "Access from the selected regions is now allowed."

#: pro/security/wordpress/class-rsssl-geo-block.php:413
msgid "Access from the selected regions is now blocked."
msgstr "Access from the selected regions is now blocked."

#: lets-encrypt/class-letsencrypt-handler.php:1197
#: lets-encrypt/functions.php:401
msgid "According to our information, your hosting provider does not allow any kind of SSL installation, other then their own paid certificate. For an alternative hosting provider with SSL, see this %sarticle%s."
msgstr "According to our information, your hosting provider does not allow any kind of SSL installation, other then their own paid certificate. For an alternative hosting provider with SSL, see this %sarticle%s."

#: lets-encrypt/class-letsencrypt-handler.php:1204
#: lets-encrypt/functions.php:391
msgid "According to our information, your hosting provider supplies your account with an SSL certificate by default. Please contact your %shosting support%s if this is not the case."
msgstr "According to our information, your hosting provider supplies your account with an SSL certificate by default. Please contact your %shosting support%s if this is not the case."

#: pro/security/wordpress/class-rsssl-password-security.php:859
msgid "Account Locked"
msgstr "Account Locked"

#: settings/config/fields/vulnerability-detection.php:209
msgid "Action"
msgstr "Action"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:31
msgid "Actions"
msgstr "Actions"

#: onboarding/class-onboarding.php:204
#: settings/config/menu.php:755
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPluginsData.js:74
#: settings/src/Onboarding/OnboardingControls.js:192
#: settings/src/Settings/License/License.js:49
msgid "Activate"
msgstr "Activate"

#: class-wp-cli.php:1340
msgid "Activate a license key. Usage: wp rsssl activate_license YOUR_LICENSE_KEY."
msgstr "Activate a license key. Usage: wp rsssl activate_license YOUR_LICENSE_KEY."

#: class-wp-cli.php:1270
msgid "Activate all recommended features."
msgstr "Activate all recommended features."

#: class-wp-cli.php:1280
msgid "Activate essential security headers."
msgstr "Activate essential security headers."

#: class-wp-cli.php:1320
msgid "Activate limit login attempts."
msgstr "Activate limit login attempts."

#: class-multisite.php:98
msgid "Activate networkwide to fix this."
msgstr "Activate network-wide to fix this."

#: class-wp-cli.php:1310
msgid "Activate password security features."
msgstr "Activate password security features."

#: class-site-health.php:340
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Dashboard/Progress/ProgressFooter.js:24
#: settings/src/Onboarding/OnboardingControls.js:168
msgid "Activate SSL"
msgstr "Activate SSL"

#: class-wp-cli.php:1242
msgid "Activate SSL on the site."
msgstr "Activate SSL on the site."

#: class-multisite.php:128
msgid "Activate SSL per site or install a wildcard certificate to fix this."
msgstr "Activate SSL per site or install a wildcard certificate to fix this."

#: class-wp-cli.php:1290
msgid "Activate the firewall."
msgstr "Activate the firewall."

#: class-wp-cli.php:1300
msgid "Activate Two-Factor Authentication."
msgstr "Activate Two-Factor Authentication."

#: class-wp-cli.php:1330
msgid "Activate vulnerability scanning."
msgstr "Activate vulnerability scanning."

#: pro/class-licensing.php:113
msgid "Activate your license for automatic updates."
msgstr "Activate your licence for automatic updates."

#: onboarding/class-onboarding.php:199
msgid "Activate your license key"
msgstr "Activate your license key"

#: upgrade/upgrade-to-pro.php:105
msgid "Activating plugin..."
msgstr "Activating plugin..."

#: class-wp-cli.php:1438
msgid "Add a blocked IP to the limit login attempts table."
msgstr "Add a blocked IP to the limit login attempts table."

#: class-wp-cli.php:1484
msgid "Add a blocked username to the limit login attempts table."
msgstr "Add a blocked username to the limit login attempts table."

#: class-wp-cli.php:1357
msgid "Add a lock file for safe mode."
msgstr "Add a lock file for safe mode."

#: class-wp-cli.php:1418
msgid "Add a trusted IP to the firewall."
msgstr "Add a trusted IP to the firewall."

#: class-wp-cli.php:1428
msgid "Add a trusted IP to the limit login attempts table."
msgstr "Add a trusted IP to the limit login attempts table."

#: class-wp-cli.php:1474
msgid "Add a trusted username to the limit login attempts table."
msgstr "Add a trusted username to the limit login attempts table."

#: settings/config/fields/security-headers.php:347
msgid "Add additional domains which can embed your website, if needed. Comma seperated."
msgstr "Add additional domains which can embed your website, if needed. Comma separated."

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:111
msgid "Add Device"
msgstr "Add Device"

#: class-wp-cli.php:1377
msgid "Add IP block."
msgstr "Add IP block."

#: pro/security/wordpress/firewall/models/class-rsssl-404-block.php:108
msgid "Added to watchlist by 404 interceptor"
msgstr "Added to watchlist by 404 interceptor"

#: pro/security/wordpress/class-rsssl-geo-block.php:281
msgid "Administrator IP"
msgstr "Administrator IP"

#: settings/config/menu.php:245
msgid "Advanced"
msgstr "Advanced"

#: class-admin.php:3326
#: settings/config/menu.php:254
msgid "Advanced Hardening"
msgstr "Advanced Hardening"

#: settings/config/menu.php:252
msgid "Advanced hardening features complement the basic hardening functions by protecting your site against advanced threats and attacks."
msgstr "Advanced hardening features complement the basic hardening functions by protecting your site against advanced threats and attacks."

#: settings/config/menu.php:255
msgid "Advanced hardening features to protect your site against sophisticated threats and attacks."
msgstr "Advanced hardening features to protect your site against sophisticated threats and attacks."

#: onboarding/class-onboarding.php:505
msgid "Advanced WordPress Hardening"
msgstr "Advanced WordPress Hardening"

#: pro/security/wordpress/traits/trait-rsssl-country.php:30
msgid "Afghanistan"
msgstr "Afghanistan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:292
msgid "Africa"
msgstr "Africa"

#: lets-encrypt/functions.php:393
#: lets-encrypt/functions.php:397
#: lets-encrypt/functions.php:407
msgid "After completing the installation, you can continue to the next step to complete your configuration."
msgstr "After completing the installation, you can continue to the next step to complete your configuration."

#: lets-encrypt/class-letsencrypt-handler.php:1205
msgid "After completing the installation, you can let Really Simple Security automatically configure your site for SSL by using the 'Activate SSL' button."
msgstr "After completing the installation, you can let Really Simple Security automatically configure your site for SSL by using the 'Activate SSL' button."

#: settings/config/fields/security-headers.php:102
msgid "After enabling this feature, you can submit your site to %shstspreload.org%s"
msgstr "After enabling this feature, you can submit your site to %shstspreload.org%s"

#: settings/config/fields/limit-login-attempts.php:27
msgid "After this number of failed login attempts the user and IP address will be temporarily blocked."
msgstr "After this number of failed login attempts the user and IP address will be temporarily blocked."

#: pro/security/wordpress/traits/trait-rsssl-country.php:31
msgid "Aland Islands"
msgstr "Aland Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:32
msgid "Albania"
msgstr "Albania"

#: pro/security/wordpress/traits/trait-rsssl-country.php:33
msgid "Algeria"
msgstr "Algeria"

#: lets-encrypt/class-letsencrypt-handler.php:1662
msgid "Alias domain check is not relevant for a subdomain"
msgstr "Alias domain check is not relevant for a subdomain"

#: pro/security/wordpress/limitlogin/class-rsssl-admin-config-countries.php:232
#: settings/config/menu.php:339
#: settings/config/menu.php:486
#: settings/config/menu.php:651
#: settings/config/menu.php:688
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/DataTable/DataTableWrapper.js:151
#: settings/src/Settings/EventLog/EventLogDataTable.js:213
#: settings/src/Settings/firewall/UserAgentTable.js:257
#: settings/src/Settings/GeoBlockList/BlockListDatatable.js:345
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:502
#: settings/src/Settings/GeoBlockList/WhiteListDatatable.js:398
#: settings/src/Settings/LearningMode/LearningMode.js:147
#: settings/src/Settings/LearningMode/LearningMode.js:320
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:435
#: settings/src/Settings/LimitLoginAttempts/IpAddressDatatable.js:298
msgid "All"
msgstr "All"

#: pro/security/wordpress/class-rsssl-limit-login-admin.php:363
msgid "All %1s were added to %2s"
msgstr "All %1s were added to %2s"

#: security/notices.php:142
msgid "All recommended hardening features enabled."
msgstr "All recommended hardening features enabled."

#: settings/config/fields/security-headers.php:214
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:278
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:295
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:473
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:484
#: settings/src/Settings/LearningMode/ChangeStatus.js:7
#: settings/src/Settings/LearningMode/LearningMode.js:276
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:310
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:321
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:330
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:401
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:412
msgid "Allow"
msgstr "Allow"

#: settings/config/fields/two-fa.php:71
msgid "Allow grace period"
msgstr "Allow grace period"

#: settings/config/menu.php:163
msgid "Allow only necessary third party resources to be loaded on your website, thus preventing common attacks. Use our unique learning mode to automatically configure your Content Security Policy."
msgstr "Allow only necessary third party resources to be loaded on your website, thus preventing common attacks. Use our unique learning mode to automatically configure your Content Security Policy."

#: settings/config/fields/two-fa.php:49
msgid "Allow secure log in with Passkeys"
msgstr "Allow secure log in with Passkeys"

#: settings/config/fields/firewall.php:145
msgid "Allow visitors that might accidentally exceed the threshold to unblock themselves using a Captcha."
msgstr "Allow visitors that might accidentally exceed the threshold to unblock themselves using a Captcha."

#: settings/config/fields/security-headers.php:338
msgid "Allow your domain to be embedded"
msgstr "Allow your domain to be embedded"

#: settings/config/menu.php:514
#: settings/config/menu.php:608
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/LearningMode/LearningMode.js:148
msgid "Allowed"
msgstr "Allowed"

#: settings/config/fields/hardening-extended.php:92
msgid "Allows you to enter a custom login URL."
msgstr "Allows you to enter a custom login URL."

#: pro/security/wordpress/traits/trait-rsssl-country.php:34
msgid "American Samoa"
msgstr "American Samoa"

#: pro/security/wordpress/vulnerabilities-pro.php:628
#: pro/security/wordpress/vulnerabilities-pro.php:687
msgid "An automatic update for %s failed on %s."
msgstr "An automatic update for %s failed on %s."

#: pro/security/wordpress/vulnerabilities-pro.php:599
msgid "An automatic update for %s has been scheduled due to the discovery of a vulnerability on %s"
msgstr "An automatic update for %s has been scheduled due to the discovery of a vulnerability on %s"

#: pro/security/wordpress/vulnerabilities-pro.php:658
msgid "An automatic update for %s has been successful on %s."
msgstr "An automatic update for %s has been successful on %s."

#: pro/security/wordpress/class-rsssl-geo-block.php:710
msgid "An error occurred while adding countries to the list."
msgstr "An error occurred while adding countries to the list."

#: pro/security/wordpress/class-rsssl-geo-block.php:419
msgid "An error occurred while adding regions to the list."
msgstr "An error occurred while adding regions to the list."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:70
msgid "An error occurred while processing the response"
msgstr "An error occurred while processing the response"

#: pro/security/wordpress/class-rsssl-geo-block.php:875
msgid "An error occurred while removing countries from the list."
msgstr "An error occurred while removing countries from the list."

#: pro/security/wordpress/class-rsssl-geo-block.php:538
msgid "An error occurred while removing regions from the list."
msgstr "An error occurred while removing regions from the list."

#: upgrade/upgrade-to-pro.php:525
#: upgrade/upgrade-to-pro.php:557
msgid "An error occurred, please try again."
msgstr "An error occurred, please try again."

#: mailer/class-mail.php:212
#: upgrade/upgrade-to-pro.php:338
#: upgrade/upgrade-to-pro.php:339
msgid "An error occurred:"
msgstr "An error occurred:"

#: pro/security/wordpress/class-rsssl-geo-block.php:653
#: pro/security/wordpress/class-rsssl-geo-block.php:694
#: pro/security/wordpress/class-rsssl-geo-block.php:812
#: pro/security/wordpress/class-rsssl-geo-block.php:859
#: pro/security/wordpress/class-rsssl-geo-block.php:1462
msgid "An error occurred: "
msgstr "An error occurred: "

#: security/notices.php:60
msgid "An option that requires the .htaccess file in the uploads directory is enabled, but the file is not writable."
msgstr "An option that requires the .htaccess file in the uploads directory is enabled, but the file is not writable."

#: security/notices.php:41
msgid "An option that requires the .htaccess file is enabled, but the file does not exist."
msgstr "An option that requires the .htaccess file is enabled, but the file does not exist."

#: security/notices.php:33
msgid "An option that requires the .htaccess file is enabled, but the file is not writable."
msgstr "An option that requires the .htaccess file is enabled, but the file is not writable."

#: onboarding/class-onboarding.php:282
msgid "An SSL certificate has been detected"
msgstr "An SSL certificate has been detected"

#: class-admin.php:3329
msgid "And many more powerful Security features.."
msgstr "And many more powerful Security features.."

#: pro/security/wordpress/traits/trait-rsssl-country.php:35
msgid "Andorra"
msgstr "Andorra"

#: pro/security/wordpress/traits/trait-rsssl-country.php:36
msgid "Angola"
msgstr "Angola"

#: pro/security/wordpress/traits/trait-rsssl-country.php:37
msgid "Anguilla"
msgstr "Anguilla"

#: pro/security/wordpress/traits/trait-rsssl-country.php:38
#: pro/security/wordpress/traits/trait-rsssl-country.php:293
msgid "Antarctica"
msgstr "Antarctica"

#: pro/security/wordpress/traits/trait-rsssl-country.php:39
msgid "Antigua and Barbuda"
msgstr "Antigua and Barbuda"

#: security/wordpress/two-fa/class-rsssl-two-factor.php:625
msgid "API login for user disabled."
msgstr "API login for user disabled."

#: pro/security/wordpress/traits/trait-rsssl-country.php:40
msgid "Argentina"
msgstr "Argentina"

#: pro/security/wordpress/traits/trait-rsssl-country.php:41
msgid "Armenia"
msgstr "Armenia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:42
msgid "Aruba"
msgstr "Aruba"

#: settings/config/fields/hardening-basic.php:104
msgid "As a security precaution, the username ‘admin’ has been changed on %s. From now on, you can login with '%s' or an email address."
msgstr "As a security precaution, the username ‘admin’ has been changed on %s. From now on, you can login with '%s' or an email address."

#. translators: %s is replaced with the plugin name.
#: class-admin.php:2320
msgid "As Really Simple Security handles all the functionality this plugin provides, we recommend to disable this plugin to prevent unexpected behavior."
msgstr "As Really Simple Security handles all the functionality this plugin provides, we recommend to disable this plugin to prevent unexpected behaviour."

#: lets-encrypt/class-letsencrypt-handler.php:752
msgid "As your order will be regenerated, you'll need to update your DNS text records."
msgstr "As your order will be regenerated, you'll need to update your DNS text records."

#: pro/security/wordpress/traits/trait-rsssl-country.php:294
msgid "Asia"
msgstr "Asia"

#: lets-encrypt/integrations/cpanel/functions.php:91
msgid "Attempting to install certificate using AutoSSL..."
msgstr "Attempting to install certificate using AutoSSL..."

#: lets-encrypt/integrations/cpanel/functions.php:107
#: lets-encrypt/integrations/directadmin/functions.php:35
msgid "Attempting to install certificate..."
msgstr "Attempting to install certificate..."

#: lets-encrypt/integrations/cpanel/functions.php:100
msgid "Attempting to set DNS txt record..."
msgstr "Attempting to set DNS txt record..."

#: settings/config/fields/limit-login-attempts.php:31
#: settings/config/fields/limit-login-attempts.php:32
#: settings/config/fields/limit-login-attempts.php:33
#: settings/config/fields/limit-login-attempts.php:34
msgid "attempts"
msgstr "attempts"

#: pro/security/wordpress/traits/trait-rsssl-country.php:43
msgid "Australia"
msgstr "Australia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:44
msgid "Austria"
msgstr "Austria"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:457
msgid "Authenticate"
msgstr "Authenticate"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:208
msgid "Authenticate via Passkey"
msgstr "Authenticate via Passkey"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:91
msgid "Authenticating User..."
msgstr "Authenticating User..."

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:260
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:265
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:270
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:275
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:524
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:529
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:534
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:539
msgid "Authentication"
msgstr "Authentication"

#: assets/templates/two_fa/totp-config.php:11
msgid "Authentication code is incorrect."
msgstr "Authentication code is incorrect."

#: assets/templates/two_fa/profile-settings.php:95
#: assets/templates/two_fa/totp-config.php:42
#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:444
msgid "Authentication Code:"
msgstr "Authentication Code:"

#. translators: %s: email address
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:462
msgid "Authentication codes will be sent to %s."
msgstr "Authentication codes will be sent to %s."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:92
msgid "Authentication failed, retry?"
msgstr "Authentication failed, retry?"

#: security/wordpress/two-fa/class-rsssl-two-factor.php:989
msgid "Authentication provider not specified or invalid."
msgstr "Authentication provider not specified or invalid."

#: security/wordpress/two-fa/class-rsssl-two-factor-settings.php:636
msgid "Authenticator App"
msgstr "Authenticator App"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:614
msgid "Authenticator app"
msgstr "Authenticator app"

#: settings/config/menu.php:321
msgid "Authenticator App (TOTP)"
msgstr "Authenticator App (TOTP)"

#: lets-encrypt/class-letsencrypt-handler.php:728
msgid "Authorization not completed yet."
msgstr "Authorisation not completed yet."

#: settings/config/menu.php:224
msgid "Automated Measures"
msgstr "Automated Measures"

#: class-admin.php:2114
#: onboarding/class-onboarding.php:288
msgid "Automatic certificate detection is not possible on your server."
msgstr "Automatic certificate detection is not possible on your server."

#: lets-encrypt/config/notices.php:104
msgid "Automatic renewal of your certificate was not possible. The SSL certificate should be %srenewed%s manually."
msgstr "Automatic renewal of your certificate was not possible. The SSL certificate should be %srenewed%s manually."

#: pro/security/wordpress/traits/trait-rsssl-country.php:45
msgid "Azerbaijan"
msgstr "Azerbaijan"

#: assets/templates/two_fa/profile-settings.php:42
#: assets/templates/two_fa/profile-settings.php:48
msgid "Backup Codes"
msgstr "Backup Codes"

#: pro/security/wordpress/traits/trait-rsssl-country.php:46
msgid "Bahamas"
msgstr "Bahamas"

#: pro/security/wordpress/traits/trait-rsssl-country.php:47
msgid "Bahrain"
msgstr "Bahrain"

#: pro/security/wordpress/traits/trait-rsssl-country.php:48
msgid "Bangladesh"
msgstr "Bangladesh"

#: pro/security/wordpress/traits/trait-rsssl-country.php:49
msgid "Barbados"
msgstr "Barbados"

#: security/wordpress/vulnerabilities.php:1573
msgid "Based on your settings, Really Simple Security will take appropriate action, or you will need to solve it manually."
msgstr "Based on your settings, Really Simple Security will take appropriate action, or you will need to solve it manually."

#: settings/config/menu.php:239
msgid "Basic"
msgstr "Basic"

#: progress/class-progress.php:90
msgid "Basic security configuration completed!"
msgstr "Basic security configuration completed!"

#: pro/security/wordpress/block-admin-creation.php:319
msgid "Because of your settings in Really Simple Security, this user has been set to subscriber until you change the role manually."
msgstr "Because of your settings in Really Simple Security, this user has been set to subscriber until you change the role manually."

#: class-multisite.php:97
msgid "Because the $_SERVER[\"HTTPS\"] variable is not set, your website may experience redirect loops."
msgstr "Because the $_SERVER[\"HTTPS\"] variable is not set, your website may experience redirect loops."

#: pro/security/wordpress/traits/trait-rsssl-country.php:50
msgid "Belarus"
msgstr "Belarus"

#: pro/security/wordpress/traits/trait-rsssl-country.php:51
msgid "Belgium"
msgstr "Belgium"

#: pro/security/wordpress/traits/trait-rsssl-country.php:52
msgid "Belize"
msgstr "Belize"

#: settings/config/menu.php:730
msgid "Below you will find the instructions for different hosting environments and configurations. If you start the process with the necessary instructions and credentials the next steps will be done in no time."
msgstr "Below you will find the instructions for different hosting environments and configurations. If you start the process with the necessary instructions and credentials, the next steps will be done in no time."

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:295
msgid "Below you will find your login code for %1$s. It's valid for 15 minutes. %2$s"
msgstr "Below you will find your login code for %1$s. It's valid for 15 minutes. %2$s"

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:289
msgid "Below you'll find the email activation code for %1$s. It's valid for 15 minutes. %2$s"
msgstr "Below you'll find the email activation code for %1$s. It's valid for 15 minutes. %2$s"

#: pro/security/wordpress/traits/trait-rsssl-country.php:53
msgid "Benin"
msgstr "Benin"

#: pro/security/wordpress/traits/trait-rsssl-country.php:54
msgid "Bermuda"
msgstr "Bermuda"

#: pro/security/wordpress/traits/trait-rsssl-country.php:55
msgid "Bhutan"
msgstr "Bhutan"

#: class-admin.php:2334
msgid "Black Friday sale! Get 40% Off Really Simple Security Pro"
msgstr "Black Friday sale! Get 40% Off Really Simple Security Pro"

#: pro/security/notices.php:34
msgid "Block admin creation was disabled, because the registration of admin users has failed."
msgstr "Block admin creation was disabled, because the registration of admin users has failed."

#: settings/config/fields/hardening-basic.php:101
msgid "Block the username 'admin'"
msgstr "Block the username 'admin'"

#: settings/config/fields/hardening-basic.php:151
msgid "Block user registrations when login and display name are the same"
msgstr "Block user registrations when login and display name are the same"

#: pro/security/wordpress/class-rsssl-geo-block.php:1489
#: settings/config/fields/firewall.php:168
#: settings/config/fields/firewall.php:213
#: settings/config/fields/limit-login-attempts.php:122
#: settings/config/fields/limit-login-attempts.php:167
#: settings/config/fields/limit-login-attempts.php:213
#: settings/config/menu.php:506
#: settings/config/menu.php:574
#: settings/config/menu.php:600
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/LearningMode/LearningMode.js:149
msgid "Blocked"
msgstr "Blocked"

#: settings/config/menu.php:462
msgid "Blocked IP addresses will be automatically unblocked after the above-configured interval. In the table below you can instantly unblock IP addresses."
msgstr "Blocked IP addresses will be automatically unblocked after the above-configured interval. In the table below you can instantly unblock IP addresses."

#: pro/class-scan.php:1654
#: pro/class-scan.php:1685
#: pro/class-scan.php:1761
#: pro/class-scan.php:1798
#: pro/class-scan.php:1847
msgid "Blocked URL: %s"
msgstr "Blocked URL: %s"

#: settings/config/menu.php:428
msgid "Blocked usernames will be automatically unblocked after the above-configured interval. In the table below you can instantly unblock usernames."
msgstr "Blocked usernames will be automatically unblocked after the above-configured interval. In the table below you can instantly unblock usernames."

#: settings/config/menu.php:617
msgid "Blocklists"
msgstr "Blocklists"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:83
msgid "Bluetooth device"
msgstr "Bluetooth device"

#: pro/security/wordpress/traits/trait-rsssl-country.php:56
msgid "Bolivia"
msgstr "Bolivia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:57
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius and Saba"

#: pro/security/wordpress/traits/trait-rsssl-country.php:58
msgid "Bosnia and Herzegovina"
msgstr "Bosnia and Herzegovina"

#: pro/security/wordpress/traits/trait-rsssl-country.php:59
msgid "Botswana"
msgstr "Botswana"

#: pro/security/wordpress/traits/trait-rsssl-country.php:60
msgid "Bouvet Island"
msgstr "Bouvet Island"

#: pro/security/wordpress/traits/trait-rsssl-country.php:61
msgid "Brazil"
msgstr "Brazil"

#: pro/security/wordpress/traits/trait-rsssl-country.php:62
msgid "British Indian Ocean Territory"
msgstr "British Indian Ocean Territory"

#: settings/config/fields/security-headers.php:221
msgid "Browser features are plentiful, but most are not needed on your website."
msgstr "Browser features are plentiful, but most are not needed on your website."

#: pro/security/wordpress/traits/trait-rsssl-country.php:63
msgid "Brunei Darussalam"
msgstr "Brunei Darussalam"

#: pro/security/wordpress/traits/trait-rsssl-country.php:64
msgid "Bulgaria"
msgstr "Bulgaria"

#: lets-encrypt/class-letsencrypt-handler.php:788
msgid "Bundle not available yet..."
msgstr "Bundle not available yet..."

#: pro/security/wordpress/traits/trait-rsssl-country.php:65
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pro/security/wordpress/traits/trait-rsssl-country.php:66
msgid "Burundi"
msgstr "Burundi"

#: settings/config/fields/hardening-basic.php:59
msgid "By default, WordPress shows if a username or email address exists when a login fails. This will change it to generic feedback."
msgstr "By default, WordPress shows if a username or email address exists when a login fails. This will change it to generic feedback."

#: pro/class-scan.php:1615
msgid "cached file, deactivate cache to see the actual source"
msgstr "cached file, deactivate cache to see the actual source"

#: pro/security/wordpress/traits/trait-rsssl-country.php:67
msgid "Cambodia"
msgstr "Cambodia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:68
msgid "Cameroon"
msgstr "Cameroon"

#: pro/class-scan.php:1686
msgid "Can be fixed automatically by pressing the Fix button. If fixing fails, the source file can be edited manually by pressing the Edit button."
msgstr "Can be fixed automatically by pressing the Fix button. If fixing fails, the source file can be edited manually by pressing the Edit button."

#: pro/class-scan.php:1686
msgid "Can be fixed manually by editing the respective mu-plugin file in the /wp-content/mu-plugins/ directory."
msgstr "Can be fixed manually by editing the respective mu-plugin file in the /wp-content/mu-plugins/ directory."

#: pro/security/wordpress/traits/trait-rsssl-country.php:69
msgid "Canada"
msgstr "Canada"

#: upgrade/upgrade-to-pro.php:336
#: modal/build/433.ef6236b038b45328e4c7.js:1
#: modal/src/components/Modal/RssslModal.js:68
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Settings/firewall/UserAgentModal.js:151
#: settings/src/Settings/GeoBlockList/TrustIpAddressModal.js:140
#: settings/src/Settings/LearningMode/ManualCspAdditionModal.js:190
#: settings/src/Settings/LimitLoginAttempts/AddIpAddressModal.js:110
#: settings/src/Settings/LimitLoginAttempts/AddUserModal.js:90
msgid "Cancel"
msgstr "Cancel"

#: pro/class-scan.php:1724
msgid "Cannot be fixed automatically, as the mixed content is coming from an external domain. Contact the owner of the domain to update the CSS/JS file"
msgstr "Cannot be fixed automatically, as the mixed content is coming from an external domain. Contact the owner of the domain to update the CSS/JS file"

#: pro/security/wordpress/traits/trait-rsssl-country.php:70
msgid "Cape Verde"
msgstr "Cape Verde"

#: settings/config/menu.php:41
#: settings/config/menu.php:44
msgid "Captcha"
msgstr "Captcha"

#: settings/config/fields/general.php:200
msgid "Captcha has not yet been verified, you need to complete the process of a Captcha to verify it's availability."
msgstr "Captcha has not yet been verified, you need to complete the process of a Captcha to verify it's availability."

#: settings/config/fields/general.php:114
msgid "Captcha provider"
msgstr "Captcha provider"

#: pro/security/wordpress/class-rsssl-captcha-config.php:182
msgid "Captcha was not verified"
msgstr "Captcha was not verified"

#: pro/security/wordpress/class-rsssl-captcha-config.php:183
msgid "Captcha was not verified successfully. Please try again."
msgstr "Captcha was not verified successfully. Please try again."

#: pro/security/wordpress/class-rsssl-captcha-config.php:182
msgid "Captcha was verified"
msgstr "Captcha was verified"

#: pro/security/wordpress/class-rsssl-captcha-config.php:183
msgid "Captcha was verified successfully and you can now enable it in the supported features."
msgstr "Captcha was verified successfully and you can now enable it in the supported features."

#: pro/security/wordpress/traits/trait-rsssl-country.php:71
msgid "Cayman Islands"
msgstr "Cayman Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:72
msgid "Central African Republic"
msgstr "Central African Republic"

#: lets-encrypt/class-letsencrypt-handler.php:698
msgid "Certificate already generated. It was renewed if required."
msgstr "Certificate already generated. It was renewed if required."

#: lets-encrypt/class-letsencrypt-handler.php:749
msgid "Certificate not created."
msgstr "Certificate not created."

#: pro/security/wordpress/traits/trait-rsssl-country.php:73
msgid "Chad"
msgstr "Chad"

#: lets-encrypt/class-letsencrypt-handler.php:1335
msgid "Challenge directory not writable."
msgstr "Challenge directory not writable."

#: settings/config/fields/hardening-extended.php:20
msgid "Change debug.log file location"
msgstr "Change debug.log file location"

#: pro/security/wordpress/class-rsssl-password-security.php:871
msgid "Change password"
msgstr "Change password"

#: settings/config/fields/access-control.php:63
msgid "Change passwords every"
msgstr "Change passwords every"

#: pro/security/wordpress/class-rsssl-password-security.php:882
msgid "Change your password"
msgstr "Change your password"

#: security/sync-settings.php:35
msgid "Changed debug.log location to:"
msgstr "Changed debug.log location to:"

#: settings/config/fields/encryption.php:13
msgid "Changing redirect methods should be done with caution. Please make sure you have read our instructions beforehand at the right-hand side."
msgstr "Changing redirect methods should be done with caution. Please make sure you have read our instructions beforehand at the right-hand side."

#: class-admin.php:2049
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Onboarding/Items/ListItem.js:46
msgid "Check again"
msgstr "Check again"

#: class-admin.php:2118
msgid "Check manually"
msgstr "Check manually"

#: upgrade/upgrade-to-pro.php:339
msgid "Check your %slicense%s."
msgstr "Check your %slicence%s."

#: lets-encrypt/config/fields.php:70
msgid "Checking alias domain..."
msgstr "Checking alias domain..."

#: lets-encrypt/config/fields.php:440
msgid "Checking certs directory..."
msgstr "Checking certs directory..."

#: lets-encrypt/config/fields.php:453
msgid "Checking challenge directory reachable over http..."
msgstr "Checking challenge directory reachable over http..."

#: lets-encrypt/config/fields.php:428
msgid "Checking challenge directory..."
msgstr "Checking challenge directory..."

#: lets-encrypt/config/fields.php:550
msgid "Checking for subdomain setup..."
msgstr "Checking for subdomain setup..."

#: lets-encrypt/config/fields.php:76
msgid "Checking for website configuration..."
msgstr "Checking for website configuration..."

#: lets-encrypt/config/fields.php:422
msgid "Checking host..."
msgstr "Checking host..."

#: lets-encrypt/config/fields.php:58
msgid "Checking if CURL is available..."
msgstr "Checking if cURL is available..."

#: pro/class-scan.php:355
msgid "Checking if external js or css files contain http links, %s of %s"
msgstr "Checking if external JS or CSS files contain http links, %s of %s"

#: upgrade/upgrade-to-pro.php:77
msgid "Checking if plugin folder exists..."
msgstr "Checking if plugin folder exists..."

#: lets-encrypt/config/fields.php:506
msgid "Checking if Terms & Conditions are accepted..."
msgstr "Checking if Terms & Conditions are accepted..."

#: lets-encrypt/config/fields.php:434
msgid "Checking key directory..."
msgstr "Checking key directory..."

#: lets-encrypt/config/fields.php:446
msgid "Checking permissions..."
msgstr "Checking permissions..."

#: pro/class-scan.php:303
msgid "Checking posts for external URLs, %s of %s"
msgstr "Checking posts for external URLs, %s of %s"

#: lets-encrypt/config/fields.php:64
msgid "Checking server software..."
msgstr "Checking server software..."

#: lets-encrypt/config/fields.php:52
msgid "Checking SSL certificate..."
msgstr "Checking SSL certificate..."

#: pro/class-scan.php:327
msgid "Checking which postmeta contain external resources"
msgstr "Checking which postmeta contain external resources"

#: pro/class-scan.php:340
msgid "Checking which resources can't load over ssl, %s of %s"
msgstr "Checking which resources can't load over ssl, %s of %s"

#: pro/class-scan.php:316
msgid "Checking widgets for external URLs"
msgstr "Checking widgets for external URLs"

#: pro/security/wordpress/traits/trait-rsssl-country.php:74
msgid "Chile"
msgstr "Chile"

#: pro/security/wordpress/traits/trait-rsssl-country.php:75
msgid "China"
msgstr "China"

#: settings/config/menu.php:303
msgid "Choose between Email Verification (less secure and not recommended for administrators) or the TOTP method with an authenticator app, depending on your convenience and security needs."
msgstr "Choose between Email Verification (less secure and not recommended for administrators) or the TOTP method with an authenticator app, depending on your convenience and security needs."

#: settings/config/fields/hardening-basic.php:118
msgid "Choose new username to replace 'admin'"
msgstr "Choose new username to replace 'admin'"

#: settings/config/fields/security-headers.php:146
msgid "Choose the max-age for HSTS"
msgstr "Choose the max-age for HSTS"

#: settings/config/fields/general.php:110
msgid "Choose your provider"
msgstr "Choose your provider"

#: pro/security/wordpress/traits/trait-rsssl-country.php:76
msgid "Christmas Island"
msgstr "Christmas Island"

#: class-admin.php:3332
msgid "Claim your 6 Free months"
msgstr "Claim your 6 Free months"

#: pro/csp-violation-endpoint.php:232
msgid "Click 'Enforce' to enforce the configuration on your site."
msgstr "Click 'Enforce' to enforce the configuration on your site."

#: mailer/class-mail.php:121
msgid "Click the button below to confirm your email address, or copy the following URL: %s"
msgstr "Click the button below to confirm your email address, or copy the following URL: %s"

#: lets-encrypt/config/fields.php:307
#: lets-encrypt/config/fields.php:312
msgid "CloudWays API key"
msgstr "CloudWays API key"

#: lets-encrypt/config/fields.php:291
msgid "CloudWays user email"
msgstr "CloudWays user email"

#: pro/security/wordpress/traits/trait-rsssl-country.php:77
msgid "Cocos (Keeling) Islands"
msgstr "Cocos (Keeling) Islands"

#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:159
msgid "Code was was invalid, try \"Resend Code\""
msgstr "Code was was invalid, try \"Resend Code\""

#: pro/security/wordpress/traits/trait-rsssl-country.php:78
msgid "Colombia"
msgstr "Colombia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:79
msgid "Comoros"
msgstr "Comoros"

#: class-admin.php:2363
msgid "Complete email validation and enable notifications to make sure you will receive security warnings."
msgstr "Complete email validation and enable notifications to make sure you will receive security warnings."

#: class-admin.php:1943
msgid "Completed"
msgstr "Completed"

#: settings/settings.php:583
msgid "Complianz - Consent Management as it should be"
msgstr "Complianz - Consent Management as it should be"

#: settings/config/fields/vulnerability-detection.php:157
msgid "Component"
msgstr "Component"

#: security/wordpress/vulnerabilities.php:648
msgid "Components will be quarantined if the update process fails."
msgstr "Components will be quarantined if the update process fails."

#: security/wordpress/two-fa/traits/trait-rsssl-email-trait.php:101
msgid "Compromised password for %s has been reset"
msgstr "Compromised password for %s has been reset"

#: security/wordpress/two-fa/traits/trait-rsssl-email-trait.php:145
msgid "Compromised password reset"
msgstr "Compromised password reset"

#: settings/config/menu.php:208
msgid "Configuration"
msgstr "Configuration"

#: upgrade/upgrade-to-pro.php:134
msgid "Configure your Cookie Notice, Consent Management and Cookie Policy with our Wizard and Cookie Scan. Supports GDPR, DSGVO, TTDSG, LGPD, POPIA, RGPD, CCPA and PIPEDA."
msgstr "Configure your Cookie Notice, Consent Management and Cookie Policy with our Wizard and Cookie Scan. Supports GDPR, DSGVO, TTDSG, LGPD, POPIA, RGPD, CCPA and PIPEDA."

#: lets-encrypt/class-letsencrypt-handler.php:506
msgid "Configured for HTTP challenge"
msgstr "Configured for HTTP challenge"

#: pro/security/wordpress/traits/trait-rsssl-country.php:80
msgid "Congo"
msgstr "Congo"

#: pro/security/wordpress/traits/trait-rsssl-country.php:81
msgid "Congo, Democratic Republic of the Congo"
msgstr "Congo, Democratic Republic of the Congo"

#: onboarding/class-onboarding.php:328
msgid "Consent Management as it should be."
msgstr "Consent management as it should be."

#: settings/config/menu.php:132
msgid "Content Security Policy"
msgstr "Content Security Policy"

#: settings/config/menu.php:133
msgid "Content Security Policy Headers"
msgstr "Content Security Policy Headers"

#: pro/csp-violation-endpoint.php:256
msgid "Content Security Policy maximum size exceeded"
msgstr "Content Security Policy maximum size exceeded"

#: settings/config/fields/firewall.php:241
#: settings/config/fields/limit-login-attempts.php:242
msgid "Continent"
msgstr "Continent"

#: settings/config/menu.php:510
#: settings/config/menu.php:604
msgid "Continents"
msgstr "Continents"

#: assets/templates/two_fa/onboarding.php:171
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:261
msgid "Continue"
msgstr "Continue"

#: lets-encrypt/class-letsencrypt-handler.php:296
msgid "Continue to renew."
msgstr "Continue to renew."

#: settings/config/menu.php:265
msgid "Control and monitor the use of XML-RPC on your site with learning mode."
msgstr "Control and monitor the use of XML-RPC on your site with learning mode."

#: settings/config/menu.php:124
msgid "Control browser features that could allow third parties to misuse data collected by microphone, camera, GEO Location etc, when enabled for your website."
msgstr "Control browser features that could allow third parties to misuse data collected by microphone, camera, GEO Location etc, when enabled for your website."

#: pro/security/wordpress/traits/trait-rsssl-country.php:82
msgid "Cook Islands"
msgstr "Cook Islands"

#: assets/templates/two_fa/profile-settings.php:90
#: assets/templates/two_fa/totp-config.php:36
msgid "Copy setup key"
msgstr "Copy setup key"

#: pro/security/wordpress/class-rsssl-password-security.php:850
#: pro/security/wordpress/class-rsssl-password-security.php:873
msgid "Copy this URL to your browser to change your password: %1$s "
msgstr "Copy this URL to your browser to change your password: %1$s "

#: placeholders/class-placeholder.php:357
#: placeholders/class-placeholder.php:398
#: placeholders/class-placeholder.php:424
#: placeholders/class-placeholder.php:449
#: pro/class-scan.php:1705
#: pro/class-scan.php:1780
#: pro/class-scan.php:1818
#: pro/class-scan.php:1865
#: pro/class-scan.php:2192
msgid "Copyright warning!"
msgstr "Copyright warning!"

#: pro/security/wordpress/traits/trait-rsssl-country.php:83
msgid "Costa Rica"
msgstr "Costa Rica"

#: pro/security/wordpress/traits/trait-rsssl-country.php:84
msgid "Cote D'Ivoire"
msgstr "Cote D'Ivoire"

#: lets-encrypt/integrations/cpanel/cpanel.php:310
#: lets-encrypt/integrations/cpanel/cpanel.php:329
msgid "Could not automatically add TXT record. Please proceed manually, following the steps below."
msgstr "Could not automatically add TXT record. Please proceed manually, following the steps below."

#: security/wordpress/block-code-execution-uploads.php:24
msgid "Could not copy code execution test file."
msgstr "Could not copy code execution test file."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:670
#: security/wordpress/two-fa/class-rsssl-two-factor.php:1437
msgid "Could not copy text: "
msgstr "Could not copy text: "

#: lets-encrypt/class-letsencrypt-handler.php:1714
msgid "Could not create test folder and file."
msgstr "Could not create test folder and file."

#: security/wordpress/block-code-execution-uploads.php:14
msgid "Could not find code execution test file."
msgstr "Could not find code execution test file."

#: lets-encrypt/class-letsencrypt-handler.php:1329
msgid "Could not reach challenge directory over %s."
msgstr "Could not reach challenge directory over %s."

#: pro/csp-violation-endpoint.php:80
msgid "Could not recognize given URI."
msgstr "Could not recognize given URI."

#: upgrade/upgrade-to-pro.php:425
msgid "Could not rename folder!"
msgstr "Could not rename folder!"

#: lets-encrypt/integrations/cloudways/cloudways.php:252
msgid "Could not retrieve server list"
msgstr "Could not retrieve server list"

#: class-admin.php:2113
#: onboarding/class-onboarding.php:288
msgid "Could not test certificate"
msgstr "Could not test certificate"

#: lets-encrypt/class-letsencrypt-handler.php:1685
msgid "Could not verify alias domain."
msgstr "Could not verify alias domain."

#: lets-encrypt/class-letsencrypt-handler.php:582
msgid "Could not verify TXT record for domain %s"
msgstr "Could not verify TXT record for domain %s"

#: settings/config/fields/hardening-xml.php:61
msgid "Count"
msgstr "Count"

#: settings/config/fields/firewall.php:234
#: settings/config/fields/firewall.php:279
#: settings/config/fields/limit-login-attempts.php:234
#: settings/config/fields/limit-login-attempts.php:286
msgid "Country"
msgstr "Country"

#. translators: %s is replaced with the country name.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:324
msgid "Country %s added to geo-IP blocklist"
msgstr "Country %s added to geo-IP blocklist"

#. translators: %s is replaced with the country name.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:330
msgid "Country %s removed from geo-IP blocklist"
msgstr "Country %s removed from geo-IP blocklist"

#: pro/security/wordpress/class-rsssl-geo-block.php:638
#: pro/security/wordpress/class-rsssl-geo-block.php:682
msgid "Country already in the list."
msgstr "Country already in the list."

#: lets-encrypt/config/fields.php:166
#: lets-encrypt/config/fields.php:169
msgid "CPanel host"
msgstr "cPanel host"

#: lets-encrypt/config/fields.php:209
msgid "CPanel password"
msgstr "cPanel password"

#: lets-encrypt/class-letsencrypt-handler.php:341
msgid "CPanel recognized. Possibly the certificate can be installed automatically."
msgstr "cPanel recognised. Possibly the certificate can be installed automatically."

#: lets-encrypt/config/fields.php:190
msgid "CPanel username"
msgstr "cPanel username"

#: settings/config/menu.php:557
#: settings/config/menu.php:558
msgid "Crawlers might scan your site looking for possible exploits. One way to detect this is the fact that they trigger more 404 (not found) errors than legitimate visitors would. Below you can set the threshold and lockout duration for 404 blocking."
msgstr "Crawlers might scan your site looking for possible exploits. One way to detect this is the fact that they trigger more 404 (not found) errors than legitimate visitors would. Below you can set the threshold and lockout duration for 404 blocking."

#: lets-encrypt/config/fields.php:474
#: lets-encrypt/config/fields.php:512
msgid "Creating account..."
msgstr "Creating account..."

#: settings/config/fields/vulnerability-detection.php:62
#: settings/config/fields/vulnerability-detection.php:84
#: settings/config/fields/vulnerability-detection.php:197
msgid "Critical"
msgstr "Critical"

#: security/hardening.php:21
#: security/wordpress/vulnerabilities.php:87
msgid "critical"
msgstr "critical"

#: settings/config/fields/vulnerability-detection.php:106
msgid "Critical (default)"
msgstr "Critical (default)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:85
msgid "Croatia"
msgstr "Croatia"

#: settings/config/fields/security-headers.php:204
msgid "Cross Origin Embedder Policy"
msgstr "Cross Origin Embedder Policy"

#: settings/config/fields/security-headers.php:174
msgid "Cross Origin Opener Policy"
msgstr "Cross Origin Opener Policy"

#: settings/config/fields/security-headers.php:189
msgid "Cross Origin Resource Policy"
msgstr "Cross Origin Resource Policy"

#: pro/csp-violation-endpoint.php:99
msgid "CSP entry added successfully!"
msgstr "CSP entry added successfully!"

#: pro/csp-violation-endpoint.php:89
msgid "CSP entry already exists!"
msgstr "CSP entry already exists!"

#: pro/csp-violation-endpoint.php:100
msgid "CSP entry could not be added. Try again."
msgstr "CSP entry could not be added. Try again."

#: pro/security/wordpress/traits/trait-rsssl-country.php:86
msgid "Cuba"
msgstr "Cuba"

#: pro/security/wordpress/traits/trait-rsssl-country.php:87
msgid "Curacao"
msgstr "Curacao"

#: settings/config/fields/hardening-extended.php:109
msgid "Custom login URL"
msgstr "Custom login URL"

#: settings/config/menu.php:385
#: settings/config/menu.php:395
msgid "Customize login attempts, intervals, and temporary lockouts according to your preferences to regulate the level of security on your website during authentication. No additional settings required"
msgstr "Customize login attempts, intervals, and temporary lockouts according to your preferences to regulate the level of security on your website during authentication. No additional settings required"

#: pro/security/wordpress/traits/trait-rsssl-country.php:88
msgid "Cyprus"
msgstr "Cyprus"

#: pro/security/wordpress/traits/trait-rsssl-country.php:89
msgid "Czech Republic"
msgstr "Czech Republic"

#: settings/config/menu.php:11
msgid "Dashboard"
msgstr "Dashboard"

#: security/wordpress/vulnerabilities.php:423
msgid "Dashboard - Test Notification"
msgstr "Dashboard - Test Notification"

#: settings/config/fields/firewall.php:42
#: settings/config/fields/firewall.php:293
#: settings/config/fields/limit-login-attempts.php:149
#: settings/config/fields/limit-login-attempts.php:195
#: settings/config/fields/limit-login-attempts.php:292
#: settings/config/fields/vulnerability-detection.php:165
msgid "Date"
msgstr "Date"

#: settings/config/fields/firewall.php:194
msgid "Date Added"
msgstr "Date Added"

#: assets/templates/two_fa/onboarding.php:156
msgid "day"
msgstr "day"

#: assets/templates/two_fa/onboarding.php:156
msgid "days"
msgstr "days"

#: class-wp-cli.php:1275
msgid "Deactivate all recommended features."
msgstr "Deactivate all recommended features."

#: class-wp-cli.php:1285
msgid "Deactivate essential security headers."
msgstr "Deactivate essential security headers."

#: class-wp-cli.php:1325
msgid "Deactivate limit login attempts."
msgstr "Deactivate limit login attempts."

#: class-wp-cli.php:1315
msgid "Deactivate password security features."
msgstr "Deactivate password security features."

#: class-wp-cli.php:1247
msgid "Deactivate SSL on the site."
msgstr "Deactivate SSL on the site."

#: class-wp-cli.php:1295
msgid "Deactivate the firewall."
msgstr "Deactivate the firewall."

#: class-wp-cli.php:1352
msgid "Deactivate the license."
msgstr "Deactivate the license."

#: class-wp-cli.php:1305
msgid "Deactivate Two-Factor Authentication."
msgstr "Deactivate Two-Factor Authentication."

#: class-wp-cli.php:1335
msgid "Deactivate vulnerability scanning."
msgstr "Deactivate vulnerability scanning."

#: security/sync-settings.php:34
msgid "Debug.log"
msgstr "Debug.log"

#: settings/config/menu.php:57
msgid "Debugging with Really Simple Security"
msgstr "Debugging with Really Simple Security"

#: settings/config/fields/general.php:70
msgid "Delete all data on plugin deletion"
msgstr "Delete all data on plugin deletion"

#: settings/config/fields/firewall.php:167
#: settings/config/menu.php:578
msgid "Deleted"
msgstr "Deleted"

#: pro/security/wordpress/traits/trait-rsssl-country.php:90
msgid "Denmark"
msgstr "Denmark"

#. translators: %1$ and %2$s are replaced with the opening and closing tag with link.
#: class-admin.php:2128
msgid "Depending on your hosting provider, %1$smanual installation%2$s may be required."
msgstr "Depending on your hosting provider, %1$smanual installation%2$s may be required."

#: settings/config/fields/encryption.php:101
#: settings/config/fields/vulnerability-detection.php:221
msgid "Description"
msgstr "Description"

#: upgrade/upgrade-to-pro.php:79
msgid "Destination folder already exists"
msgstr "Destination folder already exists"

#: placeholders/class-placeholder.php:331
#: placeholders/class-placeholder.php:348
#: placeholders/class-placeholder.php:372
#: placeholders/class-placeholder.php:389
#: placeholders/class-placeholder.php:415
#: placeholders/class-placeholder.php:440
#: pro/class-scan.php:1663
#: pro/class-scan.php:1696
#: pro/class-scan.php:1734
#: pro/class-scan.php:1771
#: pro/class-scan.php:1809
#: pro/class-scan.php:1856
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Settings/MixedContentScan/MixedContentScan.js:90
#: settings/src/Settings/RiskConfiguration/RiskData.js:115
msgid "Details"
msgstr "Details"

#: lets-encrypt/config/fields.php:47
msgid "Detected status of your setup."
msgstr "Detected status of your setup."

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:15
msgid "Device"
msgstr "Device"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:28
msgid "Device Name"
msgstr "Device Name"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:181
msgid "Device removed successfully"
msgstr "Device removed successfully"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:16
msgid "Devices"
msgstr "Devices"

#: lets-encrypt/config/fields.php:231
msgid "Direct Admin URL"
msgstr "Direct Admin URL"

#: lets-encrypt/config/fields.php:228
msgid "DirectAdmin host"
msgstr "DirectAdmin host"

#: lets-encrypt/config/fields.php:271
msgid "DirectAdmin password"
msgstr "DirectAdmin password"

#: lets-encrypt/class-letsencrypt-handler.php:347
msgid "DirectAdmin recognized. Possibly the certificate can be installed automatically."
msgstr "DirectAdmin recognised. Possibly the certificate can be installed automatically."

#: lets-encrypt/config/fields.php:252
msgid "DirectAdmin username"
msgstr "DirectAdmin username"

#: settings/config/fields/security-headers.php:393
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/LearningMode/ManualCspAdditionModal.js:156
msgid "Directive"
msgstr "Directive"

#: lets-encrypt/config/fields.php:17
#: settings/config/menu.php:735
msgid "Directories"
msgstr "Directories"

#: settings/config/fields/security-headers.php:214
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/LearningMode/LearningMode.js:335
#: settings/src/Settings/LearningMode/LearningMode.js:343
#: settings/src/Settings/PermissionsPolicy.js:200
msgid "Disable"
msgstr "Disable"

#: settings/config/fields/hardening-basic.php:13
msgid "Disable \"anyone can register\""
msgstr "Disable \"anyone can register\""

#: settings/config/fields/hardening-extended.php:29
msgid "Disable application passwords"
msgstr "Disable application passwords"

#: settings/config/fields/hardening-basic.php:70
msgid "Disable directory browsing"
msgstr "Disable directory browsing"

#: settings/config/fields/hardening-extended.php:56
msgid "Disable HTTP methods"
msgstr "Disable HTTP methods"

#: lets-encrypt/config/fields.php:123
#: lets-encrypt/config/fields.php:126
msgid "Disable OCSP stapling"
msgstr "Disable OCSP stapling"

#: settings/config/fields/hardening-basic.php:29
msgid "Disable the built-in file editors"
msgstr "Disable the built-in file editors"

#: settings/config/fields/hardening-basic.php:80
msgid "Disable user enumeration"
msgstr "Disable user enumeration"

#: settings/config/fields/hardening-basic.php:142
msgid "Disable XML-RPC"
msgstr "Disable XML-RPC"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:205
#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:602
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:539
#: settings/config/fields/firewall.php:101
#: settings/config/fields/firewall.php:106
#: settings/config/fields/security-headers.php:14
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/DataTable/DataTableWrapper.js:167
#: settings/src/Settings/DynamicDataTable/DynamicDataTable.js:168
#: settings/src/Settings/EventLog/EventLogDataTable.js:232
#: settings/src/Settings/firewall/UserAgentTable.js:273
#: settings/src/Settings/GeoBlockList/BlockListDatatable.js:362
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:520
#: settings/src/Settings/GeoBlockList/WhiteListDatatable.js:415
#: settings/src/Settings/LearningMode/LearningMode.js:386
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:454
#: settings/src/Settings/LimitLoginAttempts/IpAddressDatatable.js:319
#: settings/src/Settings/LimitLoginAttempts/UserDatatable.js:281
#: settings/src/Settings/PermissionsPolicy.js:205
#: settings/src/Settings/RiskConfiguration/VulnerabilitiesOverview.js:134
#: settings/src/Settings/RolesDropDown.js:94
#: settings/src/Settings/TwoFA/TwoFaDataTable.js:309
#: settings/src/Settings/TwoFA/TwoFaEnabledDropDown.js:135
msgid "Disabled"
msgstr "Disabled"

#: class-admin.php:3322
msgid "Discover:"
msgstr "Discover:"

#: class-admin.php:661
msgid "Dismiss"
msgstr "Dismiss"

#: settings/config/fields/general.php:50
msgid "Dismiss all notifications"
msgstr "Dismiss all notifications"

#: pro/security/wordpress/traits/trait-rsssl-country.php:91
msgid "Djibouti"
msgstr "Djibouti"

#: lets-encrypt/class-letsencrypt-handler.php:659
msgid "DNS records were not verified yet. Please complete the previous step."
msgstr "DNS records were not verified yet. Please complete the previous step."

#: lets-encrypt/class-letsencrypt-handler.php:468
#: lets-encrypt/class-letsencrypt-handler.php:471
#: lets-encrypt/class-letsencrypt-handler.php:489
msgid "DNS token not retrieved."
msgstr "DNS token not retrieved."

#: lets-encrypt/config/fields.php:21
#: settings/config/menu.php:740
msgid "DNS verification"
msgstr "DNS verification"

#: lets-encrypt/config/fields.php:396
msgid "Do you want to store these credentials for renewal purposes?"
msgstr "Do you want to store these credentials for renewal purposes?"

#: lets-encrypt/config/fields.php:134
#: lets-encrypt/config/notices.php:51
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/LetsEncrypt/DnsVerification.js:62
msgid "Domain"
msgstr "Domain"

#: pro/security/wordpress/traits/trait-rsssl-country.php:92
msgid "Dominica"
msgstr "Dominica"

#: pro/security/wordpress/traits/trait-rsssl-country.php:93
msgid "Dominican Republic"
msgstr "Dominican Republic"

#: assets/templates/two_fa/onboarding.php:56
#: assets/templates/two_fa/onboarding.php:137
msgid "Don't ask again"
msgstr "Don't ask again"

#: class-admin.php:1789
msgid "Don't show again"
msgstr "Don't show again"

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:393
msgid "Don't use Two-Factor Authentication"
msgstr "Don't use Two-Factor Authentication"

#: settings/config/fields/general.php:60
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/LetsEncrypt/Installation.js:95
#: settings/src/LetsEncrypt/Installation.js:100
#: settings/src/LetsEncrypt/Installation.js:104
msgid "Download"
msgstr "Download"

#: assets/templates/two_fa/profile-settings.php:52
#: assets/templates/two_fa/totp-config.php:23
#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:668
#: security/wordpress/two-fa/class-rsssl-two-factor.php:1435
msgid "Download Backup Codes"
msgstr "Download Backup Codes"

#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:213
msgid "Download Codes"
msgstr "Download Codes"

#: pro/class-scan.php:1632
msgid "Downloading files from other websites can cause serious copyright issues! It is always illegal to use images, files, or any copyright protected material on your own site without the consent of the copyrightholder. Please ask the copyrightholder for permission. Use this function at your own risk."
msgstr "Downloading files from other websites can cause serious copyright issues! It is always illegal to use images, files, or any copyright protected material on your own site without the consent of the copyrightholder. Please ask the copyrightholder for permission. Use this function at your own risk."

#: lets-encrypt/class-letsencrypt-handler.php:745
msgid "Due to a change in challenge type, the order had to be reset. Please start at the previous step."
msgstr "Due to a change in challenge type, the order had to be reset. Please start at the previous step."

#: settings/config/fields/two-fa.php:72
msgid "During the grace period users can configure their secure authentication method. When the grace period ends, users for which secure authentication is enforced won’t be able to log in unless secure authentication is correctly configured. The grace period is also applied to new users."
msgstr "During the grace period users can configure their secure authentication method. When the grace period ends, users for which secure authentication is enforced won’t be able to log in unless secure authentication is correctly configured. The grace period is also applied to new users."

#: onboarding/class-onboarding.php:405
msgid "E-mail login"
msgstr "E-mail login"

#: upgrade/upgrade-to-pro.php:147
msgid "Easily improve site security with WordPress Hardening, Two-Factor Authentication (2FA), Login Protection, Vulnerability Detection and SSL certificate generation."
msgstr "Easily improve site security with WordPress Hardening, Two-Factor Authentication (2FA), Login Protection, Vulnerability Detection and SSL certificate generation."

#: pro/security/wordpress/traits/trait-rsssl-country.php:94
msgid "Ecuador"
msgstr "Ecuador"

#. translators: Example auth code.
#: assets/templates/two_fa/profile-settings.php:98
#: assets/templates/two_fa/totp-config.php:45
msgid "eg. %s"
msgstr "eg. %s"

#: pro/security/wordpress/traits/trait-rsssl-country.php:95
msgid "Egypt"
msgstr "Egypt"

#: pro/security/wordpress/traits/trait-rsssl-country.php:96
msgid "El Salvador"
msgstr "El Salvador"

#: pro/class-admin.php:602
msgid "Elementor mixed content successfully converted."
msgstr "Elementor mixed content successfully converted."

#: settings/config/menu.php:54
msgid "Elevate your security with our Premium Support! Our expert team ensures simple, hassle-free assistance whenever you need it."
msgstr "Elevate your security with our Premium Support! Our expert team ensures simple, hassle-free assistance whenever you need it."

#: security/wordpress/two-fa/class-rsssl-two-factor-settings.php:640
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:541
#: settings/config/fields/vulnerability-detection.php:108
#: settings/config/menu.php:35
msgid "Email"
msgstr "Email"

#: lets-encrypt/config/fields.php:94
#: lets-encrypt/config/fields.php:97
#: settings/config/fields/general.php:78
msgid "Email address"
msgstr "Email address"

#: mailer/class-mail.php:60
#: mailer/class-mail.php:99
#: mailer/class-mail.php:145
msgid "Email address not valid"
msgstr "Email address not valid"

#: class-admin.php:2371
msgid "Email address successfully verified."
msgstr "Email address successfully verified."

#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:202
msgid "Email authentication is not active for this user"
msgstr "Email authentication is not active for this user"

#: mailer/class-mail.php:210
msgid "Email could not be sent."
msgstr "Email could not be sent."

#: mailer/class-mail.php:141
msgid "Email could not be sent. No message or subject set."
msgstr "Email could not be sent. No message or subject set."

#: settings/config/fields/two-fa.php:104
msgid "Email log in will send an authentication code to the user’s email address. This is considered less secure than other 2FA methods."
msgstr "Email log in will send an authentication code to the user’s email address. This is considered less secure than other 2FA methods."

#: settings/config/disable-fields-filter.php:80
msgid "Email not verified yet. Verify your email address to get the most out of Really Simple Security."
msgstr "Email not verified yet. Verify your email address to get the most out of Really Simple Security."

#: mailer/class-mail.php:217
msgid "Email notification error"
msgstr "Email notification error"

#: mailer/class-mail.php:68
msgid "Email notifications are only sent for important updates, security notices or when certain features are enabled."
msgstr "Email notifications are only sent for important updates, security notices or when certain features are enabled."

#: mailer/class-mail.php:205
msgid "Email sent! Please check your mail"
msgstr "Email sent! Please check your mail"

#: mailer/class-mail.php:204
#: settings/config/disable-fields-filter.php:74
#: settings/config/fields/general.php:90
msgid "Email validation"
msgstr "Email validation"

#: settings/config/disable-fields-filter.php:77
msgid "Email validation completed"
msgstr "Email validation completed"

#: mailer/class-mail.php:81
#: mailer/class-mail.php:98
msgid "Email verification error"
msgstr "Email verification error"

#: onboarding/class-onboarding.php:195
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js:116
msgid "Enable"
msgstr "Enable"

#: settings/config/disable-fields-filter.php:20
msgid "Enable .htaccess only if you know how to regain access in case of issues."
msgstr "Enable .htaccess only if you know how to regain access in case of issues."

#: class-site-health.php:373
msgid "Enable 301 .htaccess redirect"
msgstr "Enable 301 .htaccess redirect"

#: class-site-health.php:361
msgid "Enable 301 redirect"
msgstr "Enable 301 redirect"

#: class-admin.php:2217
msgid "Enable a .htaccess redirect or PHP redirect in the settings to create a 301 redirect."
msgstr "Enable a .htaccess redirect or PHP redirect in the settings to create a 301 redirect."

#: settings/config/fields/hibp-integration.php:12
msgid "Enable compromised password check"
msgstr "Enable compromised password check"

#: settings/config/fields/hardening-extended.php:93
msgid "Enable Custom login URL"
msgstr "Enable Custom login URL"

#: settings/config/fields/two-fa.php:105
msgid "Enable Email Authentication for:"
msgstr "Enable Email Authentication for:"

#: settings/config/fields/firewall.php:12
msgid "Enable Firewall"
msgstr "Enable Firewall"

#: settings/config/fields/limit-login-attempts.php:13
msgid "Enable Limit Login Attempts"
msgstr "Enable Limit Login Attempts"

#: class-site-health.php:119
#: pro/security/notices.php:67
msgid "Enable Limit Login Attempts to protect the login form against brute-force attacks."
msgstr "Enable Limit Login Attempts to protect the login form against brute-force attacks."

#: settings/config/fields/limit-login-attempts.php:171
msgid "Enable open source blocklist API etc."
msgstr "Enable open source blocklist API etc."

#: settings/config/fields/security-headers.php:303
msgid "Enable Permissions Policy"
msgstr "Enable Permissions Policy"

#: security/notices.php:110
msgid "Enable the Vulnerability scan to detect possible vulnerabilities."
msgstr "Enable the Vulnerability scan to detect possible vulnerabilities."

#: settings/config/fields/two-fa.php:129
msgid "Enable TOTP Authentication for:"
msgstr "Enable TOTP Authentication for:"

#: assets/templates/two_fa/profile-settings.php:31
#: settings/config/fields/two-fa.php:12
msgid "Enable Two-Factor Authentication"
msgstr "Enable Two-Factor Authentication"

#: settings/config/fields/vulnerability-detection.php:13
msgid "Enable vulnerability scanning"
msgstr "Enable vulnerability scanning"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:205
#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:599
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:539
msgid "Enabled"
msgstr "Enabled"

#: lets-encrypt/integrations/cloudways/functions.php:49
msgid "Enabling auto renew..."
msgstr "Enabling auto renew..."

#: settings/config/fields/access-control.php:28
msgid "Enforce frequent password change"
msgstr "Enforce frequent password change"

#: settings/config/fields/two-fa.php:27
msgid "Enforce secure authentication for:"
msgstr "Enforce secure authentication for:"

#: settings/config/menu.php:366
msgid "Enforce secure password policies for your users by requiring strong passwords, and expiring passwords after a period of your choosing."
msgstr "Enforce secure password policies for your users by requiring strong passwords, and expiring passwords after a period of your choosing."

#: settings/config/fields/access-control.php:12
#: settings/config/fields/access-control.php:19
msgid "Enforce strong passwords"
msgstr "Enforce strong passwords"

#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:368
msgid "Enter a backup verification code."
msgstr "Enter a backup verification code."

#: settings/config/fields/hardening-extended.php:107
msgid "Enter a custom login URL. This allows you to log in via this custom URL instead of /wp-admin or /wp-login.php"
msgstr "Enter a custom login URL. This allows you to log in via this custom URL instead of /wp-admin or /wp-login.php"

#: pro/security/wordpress/traits/trait-rsssl-country.php:97
msgid "Equatorial Guinea"
msgstr "Equatorial Guinea"

#: pro/security/wordpress/traits/trait-rsssl-country.php:98
msgid "Eritrea"
msgstr "Eritrea"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:88
msgid "Error"
msgstr "Error"

#: lets-encrypt/class-letsencrypt-handler.php:1349
#: lets-encrypt/class-letsencrypt-handler.php:1353
#: lets-encrypt/class-letsencrypt-handler.php:1732
msgid "Error code %s"
msgstr "Error code %s"

#: pro/security/wordpress/class-rsssl-geo-block.php:1144
msgid "Error code: 403"
msgstr "Error code: 403"

#: pro/security/wordpress/firewall/class-rsssl-404-interceptor.php:339
msgid "Error code: 404"
msgstr "Error code: 404"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:76
msgid "Error during assertion verification:"
msgstr "Error during assertion verification:"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:80
msgid "Error during completing the registration:"
msgstr "Error during completing the registration:"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:72
msgid "Error during passkey login:"
msgstr "Error during passkey login:"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:73
msgid "Error during passkey registration:"
msgstr "Error during passkey registration:"

#: lets-encrypt/integrations/cloudways/cloudways.php:196
msgid "Error enabling auto renew for Let's Encrypt"
msgstr "Error enabling auto renew for Let's Encrypt"

#: class-admin.php:2158
msgid "Error occurred when retrieving the webpage."
msgstr "Error occurred when retrieving the webpage."

#: lets-encrypt/integrations/cpanel/cpanel.php:128
#: lets-encrypt/integrations/cpanel/cpanel.php:207
msgid "Errors were reported during installation."
msgstr "Errors were reported during installation."

#: onboarding/class-onboarding.php:192
msgid "Essential security"
msgstr "Essential security"

#: settings/config/menu.php:104
msgid "Essential Security Headers"
msgstr "Essential Security Headers"

#: class-site-health.php:272
msgid "Essential security headers installed"
msgstr "Essential security headers installed"

#: onboarding/class-onboarding.php:399
msgid "Essential WordPress hardening"
msgstr "Essential WordPress hardening"

#: pro/security/wordpress/traits/trait-rsssl-country.php:99
msgid "Estonia"
msgstr "Estonia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:100
msgid "Ethiopia"
msgstr "Ethiopia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:295
msgid "Europe"
msgstr "Europe"

#: settings/config/fields/firewall.php:299
#: settings/config/fields/limit-login-attempts.php:314
msgid "Event"
msgstr "Event"

#: settings/config/menu.php:474
#: settings/config/menu.php:490
msgid "Event Log"
msgstr "Event Log"

#: settings/config/menu.php:678
#: settings/config/menu.php:679
msgid "Event Logs"
msgstr "Event Logs"

#: settings/config/fields/hardening-extended.php:108
msgid "Example: If you want to change your login page from /wp-admin/ to /control/ answer: control"
msgstr "Example: If you want to change your login page from /wp-admin/ to /control/ answer: control"

#: class-admin.php:3298
msgid "Experience all powerful features of Really Simple Security Pro using this %slimited time discount%s: %s"
msgstr "Experience all powerful features of Really Simple Security Pro using this %slimited time discount%s: %s"

#: lets-encrypt/integrations/cloudways/cloudways.php:161
msgid "Failed retrieving access token"
msgstr "Failed retrieving access token"

#: lets-encrypt/class-letsencrypt-handler.php:869
msgid "Failed retrieving account."
msgstr "Failed retrieving account."

#: upgrade/upgrade-to-pro.php:107
msgid "Failed to activate plugin"
msgstr "Failed to activate plugin"

#: pro/security/wordpress/class-rsssl-geo-block.php:645
#: pro/security/wordpress/class-rsssl-geo-block.php:688
msgid "Failed to add country to the list."
msgstr "Failed to add country to the list."

#: security/wordpress/two-fa/class-rsssl-two-factor.php:1455
msgid "Failed to create a login nonce."
msgstr "Failed to create a login nonce."

#: pro/security/wordpress/class-rsssl-geo-block.php:906
msgid "Failed to delete %s from the list."
msgstr "Failed to delete %s from the list."

#: pro/security/wordpress/class-rsssl-geo-block.php:573
msgid "Failed to delete some countries."
msgstr "Failed to delete some countries."

#: upgrade/upgrade-to-pro.php:93
msgid "Failed to gather package information"
msgstr "Failed to gather package information"

#: upgrade/upgrade-to-pro.php:100
msgid "Failed to install plugin"
msgstr "Failed to install plugin"

#: class-wp-cli.php:1186
msgid "Failed to reach %s. The site does not appear to be accessible over HTTPS (Error: %s). Check debug logs for details."
msgstr "Failed to reach %s. The site does not appear to be accessible over HTTPS (Error: %s). Check debug logs for details."

#: class-wp-cli.php:1170
msgid "Failed to reach %s. The site does not appear to be accessible over HTTPS. Please ensure your server is configured for SSL."
msgstr "Failed to reach %s. The site does not appear to be accessible over HTTPS. Please ensure your server is configured for SSL."

#: pro/security/wordpress/traits/trait-rsssl-country.php:101
msgid "Falkland Islands (Malvinas)"
msgstr "Falkland Islands (Malvinas)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:102
msgid "Faroe Islands"
msgstr "Faroe Islands"

#: settings/config/fields/security-headers.php:225
msgid "Feature"
msgstr "Feature"

#: settings/config/fields/vulnerability-detection.php:41
msgid "Feedback in plugin overview"
msgstr "Feedback in plugin overview"

#: pro/security/wordpress/traits/trait-rsssl-country.php:103
msgid "Fiji"
msgstr "Fiji"

#: pro/class-scan.php:1617
msgid "file"
msgstr "file"

#: lets-encrypt/download.php:49
msgid "File missing. Please retry the previous steps."
msgstr "File missing. Please retry the previous steps."

#: settings/config/fields/hardening-extended.php:82
msgid "File permissions check"
msgstr "File permissions check"

#: lets-encrypt/class-letsencrypt-handler.php:704
#: lets-encrypt/class-letsencrypt-handler.php:780
msgid "Files not created yet..."
msgstr "Files not created yet..."

#: pro/security/wordpress/permission-detection/download.php:36
msgid "Files with wrong permissions:"
msgstr "Files with wrong permissions:"

#: pro/class-scan.php:420
msgid "Finished scan"
msgstr "Finished scan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:104
msgid "Finland"
msgstr "Finland"

#: onboarding/class-onboarding.php:421
#: onboarding/class-onboarding.php:462
#: security/firewall-manager.php:626
#: security/firewall-manager.php:632
#: security/firewall-manager.php:648
#: settings/config/menu.php:529
#: settings/config/menu.php:547
#: modal/build/index.d4bca8705bbc6e3e5777.js:1
#: modal/src/components/DeactivationModal/DeactivationModal.js:71
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Progress/ProgressFooter.js:44
#: settings/src/Dashboard/Progress/ProgressFooter.js:48
msgid "Firewall"
msgstr "Firewall"

#: class-site-health.php:73
msgid "Firewall Protection"
msgstr "Firewall Protection"

#: settings/config/menu.php:544
msgid "Firewall Rules"
msgstr "Firewall Rules"

#: pro/security/wordpress/permission-detection/permission-detection.php:82
msgid "Fixing of file permissions completed. Fixed: %d. Failed: %d. Skipped: %d"
msgstr "Fixing of file permissions completed. Fixed: %d. Failed: %d. Skipped: %d"

#: pro/security/wordpress/permission-detection/permission-detection.php:88
msgid "Fixing of file permissions partially completed due to the amount of files. Start again to fix all files. Fixed: %d. Failed: %d. Skipped: %d"
msgstr "Fixing of file permissions partially completed due to the amount of files. Restart to fix all files. Fixed: %d. Failed: %d. Skipped: %d"

#: class-wp-cli.php:1389
#: class-wp-cli.php:1450
#: class-wp-cli.php:1468
#: class-wp-cli.php:1496
msgid "Flag to add a permanent block."
msgstr "Flag to add a permanent block."

#: class-wp-cli.php:1514
msgid "Flag to remove a permanent block."
msgstr "Flag to remove a permanent block."

#: pro/csp-violation-endpoint.php:229
msgid "Follow these steps to complete the setup:"
msgstr "Follow these steps to complete the setup:"

#: lets-encrypt/functions.php:12
msgid "For more information, please read this %sarticle%s"
msgstr "For more information, please read this %sarticle%s"

#: security/wordpress/user-enumeration.php:10
msgid "forbidden - number in author name not allowed = %s"
msgstr "forbidden - number in author name not allowed = %s"

#: security/wordpress/vulnerabilities.php:640
msgid "Force update"
msgstr "Force update"

#: pro/security/wordpress/vulnerabilities-pro.php:606
msgid "Force Update: %s hours"
msgstr "Force Update: %s hours"

#: pro/security/wordpress/vulnerabilities-pro.php:636
msgid "Force Update: failed"
msgstr "Force Update: failed"

#: pro/security/wordpress/traits/trait-rsssl-country.php:105
msgid "France"
msgstr "France"

#: pro/security/wordpress/traits/trait-rsssl-country.php:106
msgid "French Guiana"
msgstr "French Guiana"

#: pro/security/wordpress/traits/trait-rsssl-country.php:107
msgid "French Polynesia"
msgstr "French Polynesia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:108
msgid "French Southern Territories"
msgstr "French Southern Territories"

#: settings/config/fields/hardening-extended.php:17
msgid "From now on, the debug.log won’t be publicly accessible whenever wp-debugging is enabled. The debug log will be stored in a randomly named folder in /wp-content/. This prevents possible leakage of sensitive debugging information."
msgstr "From now on, the debug.log won’t be publicly accessible whenever wp-debugging is enabled. The debug log will be stored in a randomly named folder in /wp-content/. This prevents possible leakage of sensitive debugging information."

#: pro/security/wordpress/traits/trait-rsssl-country.php:109
msgid "Gabon"
msgstr "Gabon"

#: pro/security/wordpress/traits/trait-rsssl-country.php:110
msgid "Gambia"
msgstr "Gambia"

#: upgrade/upgrade-to-pro.php:130
msgid "GDPR/CCPA Privacy Suite"
msgstr "GDPR/CCPA Privacy Suite"

#: settings/config/menu.php:23
#: settings/config/menu.php:29
#: settings/config/menu.php:386
msgid "General"
msgstr "General"

#: lets-encrypt/config/fields.php:12
#: settings/config/menu.php:725
msgid "General Settings"
msgstr "General Settings"

#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:195
msgid "Generate Verification Codes"
msgstr "Generate Verification Codes"

#: pro/class-scan.php:289
msgid "Generating file list"
msgstr "Generating file list"

#: lets-encrypt/config/fields.php:518
msgid "Generating SSL certificate..."
msgstr "Generating SSL certificate..."

#: pro/class-scan.php:247
msgid "Generating web page list"
msgstr "Generating web page list"

#: lets-encrypt/config/fields.php:25
#: settings/config/menu.php:745
msgid "Generation"
msgstr "Generation"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:567
msgid "Geo-IP blocklist changed"
msgstr "Geo-IP blocklist changed"

#: pro/security/wordpress/traits/trait-rsssl-country.php:111
msgid "Georgia"
msgstr "Georgia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:112
msgid "Germany"
msgstr "Germany"

#: settings/config/menu.php:309
msgid "Get Login Protection with %sReally Simple SSL Pro%s"
msgstr "Get Login Protection with %sReally Simple SSL Pro%s"

#: settings/config/menu.php:318
msgid "Get Login Protection with Really Simple Security Pro"
msgstr "Get Login Protection with Really Simple Security Pro"

#: settings/config/fields/general.php:100
msgid "Get notified of important changes, updates and settings. Recommended when using security features."
msgstr "Get notified of important changes, updates and settings. Recommended when using security features."

#: settings/config/menu.php:281
msgid "Get two-factor authentication with Really Simple Security Pro"
msgstr "Get two-factor authentication with Really Simple Security Pro"

#: pro/security/wordpress/traits/trait-rsssl-country.php:113
msgid "Ghana"
msgstr "Ghana"

#: pro/security/wordpress/traits/trait-rsssl-country.php:114
msgid "Gibraltar"
msgstr "Gibraltar"

#: lets-encrypt/functions.php:385
msgid "Go to activation"
msgstr "Go to activation"

#: lets-encrypt/functions.php:386
msgid "Go to installation"
msgstr "Go to installation"

#: pro/security/wordpress/traits/trait-rsssl-country.php:115
msgid "Greece"
msgstr "Greece"

#: pro/security/wordpress/traits/trait-rsssl-country.php:116
msgid "Greenland"
msgstr "Greenland"

#: pro/security/wordpress/traits/trait-rsssl-country.php:117
msgid "Grenada"
msgstr "Grenada"

#: pro/security/wordpress/traits/trait-rsssl-country.php:118
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: pro/security/wordpress/traits/trait-rsssl-country.php:119
msgid "Guam"
msgstr "Guam"

#: pro/security/wordpress/traits/trait-rsssl-country.php:120
msgid "Guatemala"
msgstr "Guatemala"

#: pro/security/wordpress/traits/trait-rsssl-country.php:121
msgid "Guernsey"
msgstr "Guernsey"

#: pro/security/wordpress/traits/trait-rsssl-country.php:122
msgid "Guinea"
msgstr "Guinea"

#: pro/security/wordpress/traits/trait-rsssl-country.php:123
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: pro/security/wordpress/traits/trait-rsssl-country.php:124
msgid "Guyana"
msgstr "Guyana"

#: pro/security/wordpress/traits/trait-rsssl-country.php:125
msgid "Haiti"
msgstr "Haiti"

#: settings/config/menu.php:233
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Vulnerabilities/VulnerabilitiesHeader.js:15
msgid "Hardening"
msgstr "Hardening"

#: settings/config/fields/hardening-basic.php:20
msgid "Hardening features limit the possibility of potential weaknesses and vulnerabilities which can be misused."
msgstr "Hardening features limit the possibility of potential weaknesses and vulnerabilities which can be misused."

#: settings/config/fields/general.php:112
msgid "hCaptcha"
msgstr "hCaptcha"

#: settings/config/fields/general.php:183
msgid "hCaptcha secret key"
msgstr "hCaptcha secret key"

#: settings/config/fields/general.php:166
msgid "hCaptcha site key"
msgstr "hCaptcha site key"

#: pro/security/wordpress/traits/trait-rsssl-country.php:126
msgid "Heard Island and McDonald Islands"
msgstr "Heard Island and McDonald Islands"

#: onboarding/class-onboarding.php:217
msgid "Heavyweight security features, in a lightweight performant plugin from Really Simple Plugins. Get started with below features and get the latest and greatest updates for peace of mind!"
msgstr "Heavyweight security features, in a lightweight performant plugin from Really Simple Plugins. Get started with below features and get the latest and greatest updates for peace of mind!"

#. translators: %1$s: user login, %2$s: site url, %3$s: password best practices link, %4$s: lost password url
#: security/wordpress/two-fa/traits/trait-rsssl-email-trait.php:49
msgid ""
"Hello %1$s, an unusually high number of failed login attempts have been detected on your account at %2$s.\n"
"\n"
"These attempts successfully entered your password, and were only blocked because they failed to enter your second authentication factor. Despite not being able to access your account, this behavior indicates that the attackers have compromised your password. The most common reasons for this are that your password was easy to guess, or was reused on another site which has been compromised.\n"
"\n"
"To protect your account, your password has been reset, and you will need to create a new one. For advice on setting a strong password, please read %3$s\n"
"\n"
"To pick a new password, please visit %4$s\n"
"\n"
"This is an automated notification. If you would like to speak to a site administrator, please contact them directly."
msgstr ""
"Hello %1$s, an unusually high number of failed login attempts have been detected on your account at %2$s.\n"
"\n"
"These attempts successfully entered your password, and were only blocked because they failed to enter your second authentication factor. Despite not being able to access your account, this behaviour indicates that the attackers have compromised your password. The most common reasons for this are that your password was easy to guess, or was reused on another site which has been compromised.\n"
"\n"
"To protect your account, your password has been reset, and you will need to create a new one. For advice on setting a strong password, please read %3$s\n"
"\n"
"To pick a new password, please visit %4$s\n"
"\n"
"This is an automated notification. If you would like to speak to a site administrator, please contact them directly."

#: security/wordpress/two-fa/traits/trait-rsssl-email-trait.php:121
msgid "Hello, this is a notice from your website to inform you that an unusually high number of failed login attempts have been detected on the %1$s account (ID %2$d). Those attempts successfully entered the user's password, and were only blocked because they entered invalid second authentication factors. To protect their account, the password has automatically been reset, and they have been notified that they will need to create a new one. If you do not wish to receive these notifications, you can disable them with the `two_factor_notify_admin_user_password_reset` filter. See %3$s for more information. Thank you"
msgstr "Hello, this is a notice from your website to inform you that an unusually high number of failed login attempts have been detected on the %1$s account (ID %2$d). Those attempts successfully entered the user's password, and were only blocked because they entered invalid second authentication factors. To protect their account, the password has automatically been reset, and they have been notified that they will need to create a new one. If you do not wish to receive these notifications, you can disable them with the `two_factor_notify_admin_user_password_reset` filter. See %3$s for more information. Thank you"

#: settings/config/menu.php:630
msgid "Here you can add IP addresses that should never be blocked by the Firewall. We will automatically add the IP address of the administrator that enabled the Firewall."
msgstr "Here you can add IP addresses that should never be blocked by the Firewall. We will automatically add the IP address of the administrator that enabled the Firewall."

#: settings/config/menu.php:193
msgid "Here you can configure vulnerability detection, notifications and measures. To learn more about the features displayed, please use the instructions linked in the top-right corner."
msgstr "Here you can configure vulnerability detection, notifications and measures. To learn more about the features displayed, please use the instructions linked in the top-right corner."

#: settings/config/menu.php:332
msgid "Here you can see which users have configured Two-Factor Authentication. The reset button will trigger the 2FA onboarding for the selected user(s) again and allow the configured grace period."
msgstr "Here you can see which users have configured Two-Factor Authentication. The reset button will trigger the 2FA onboarding for the selected user(s) again and allow the configured grace period."

#: settings/config/menu.php:328
#: settings/config/menu.php:406
msgid "Here you control the users that are automatically, and temporarily blocked. You can also add or remove users manually. We recommend blocking ‘admin’ as username as a start."
msgstr "Here you control the users that are automatically, and temporarily blocked. You can also add or remove users manually. We recommend blocking ‘admin’ as username as a start."

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:330
msgid "Hi"
msgstr "Hi"

#: pro/security/wordpress/class-rsssl-password-security.php:844
msgid "Hi %1$s, Your password expired on %2$s , please change your password."
msgstr "Hi %1$s, Your password expired on %2$s , please change your password."

#: pro/security/wordpress/class-rsssl-password-security.php:867
msgid "Hi %1$s, Your password on %2$s will expire in 7 days, please change your password."
msgstr "Hi %1$s, Your password on %2$s will expire in 7 days, please change your password."

#. translators: %s: First name. %s: Last name.
#: security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php:141
msgid "Hi %s %s"
msgstr "Hi %s %s"

#. translators: %1$ and %2$ are replaced with opening and closing a tag containing hyperlink
#: class-admin.php:1762
msgid "Hi, Really Simple Security has kept your site secure for a month now, awesome! If you have a moment, please consider leaving a review on WordPress.org to spread the word. We greatly appreciate it! If you have any questions or feedback, leave us a %1$smessage%2$s."
msgstr "Hi, Really Simple Security has kept your site secure for a month now, awesome! If you have a moment, please consider leaving a review on WordPress.org to spread the word. We greatly appreciate it! If you have any questions or feedback, leave us a %1$smessage%2$s."

#. translators: %1$ and %2$ are replaced with opening and closing a tag containing hyperlink
#: class-admin.php:1755
msgid "Hi, Really Simple Security has kept your site secure for some time now, awesome! If you have a moment, please consider leaving a review on WordPress.org to spread the word. We greatly appreciate it! If you have any questions or feedback, leave us a %1$smessage%2$s."
msgstr "Hi, Really Simple Security has kept your site secure for some time now, awesome! If you have a moment, please consider leaving a review on WordPress.org to spread the word. We greatly appreciate it! If you have any questions or feedback, leave us a %1$smessage%2$s."

#: settings/config/fields/access-control.php:94
msgid "Hide the remember me checkbox"
msgstr "Hide the remember me checkbox"

#: settings/config/fields/hardening-basic.php:49
msgid "Hide your WordPress version"
msgstr "Hide your WordPress version"

#: settings/config/fields/vulnerability-detection.php:61
#: settings/config/fields/vulnerability-detection.php:105
#: settings/config/fields/vulnerability-detection.php:196
msgid "High-risk"
msgstr "High-risk"

#: security/hardening.php:20
#: security/wordpress/vulnerabilities.php:86
msgid "high-risk"
msgstr "high-risk"

#: settings/config/fields/vulnerability-detection.php:83
msgid "High-risk (default)"
msgstr "High-risk (default)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:127
msgid "Holy See (Vatican City State)"
msgstr "Holy See (Vatican City State)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:128
msgid "Honduras"
msgstr "Honduras"

#: pro/security/wordpress/traits/trait-rsssl-country.php:129
msgid "Hong Kong"
msgstr "Hong Kong"

#: settings/config/menu.php:729
msgid "Hosting"
msgstr "Hosting"

#: settings/config/fields/general.php:32
msgid "Hosting provider"
msgstr "Hosting provider"

#: settings/config/menu.php:116
msgid "HSTS forces browsers always to load a website via HTTPS. It prevents unnecessary redirects and prevents manipulation of data originating from communication with your website."
msgstr "HSTS forces browsers always to load a website via HTTPS. It prevents unnecessary redirects and prevents manipulation of data originating from communication with your website."

#: pro/class-headers.php:281
msgid "HSTS not enabled"
msgstr "HSTS not enabled"

#: pro/class-headers.php:268
#: pro/class-headers.php:274
msgid "HSTS Preload"
msgstr "HSTS Preload"

#: settings/config/fields/security-headers.php:79
msgid "HTTP Strict Transport Security"
msgstr "HTTP Strict Transport Security"

#. Author URI of the plugin
#: really-simple-ssl-pro.php
msgid "https://really-simple-plugins.com"
msgstr "https://really-simple-plugins.com"

#. Plugin URI of the plugin
#: really-simple-ssl-pro.php
msgid "https://really-simple-ssl.com"
msgstr "https://really-simple-ssl.com"

#: pro/security/wordpress/traits/trait-rsssl-country.php:130
msgid "Hungary"
msgstr "Hungary"

#: lets-encrypt/config/fields.php:110
msgid "I agree to the Terms & Conditions from Let's Encrypt."
msgstr "I agree to the Terms & Conditions from Let's Encrypt."

#: lets-encrypt/config/class-hosts.php:788
msgid "I don't know, or not listed, proceed with installation"
msgstr "I don't know, or not listed, proceed with installation"

#: settings/config/fields/vulnerability-detection.php:233
msgid "I have read and understood the risks to intervene with these measures."
msgstr "I have read and understood the risks to intervene with these measures."

#: pro/security/wordpress/traits/trait-rsssl-country.php:131
msgid "Iceland"
msgstr "Iceland"

#: settings/config/fields/limit-login-attempts.php:50
msgid "If the number of failed login attempts is exceeded within this timeframe, the IP address and user will be blocked."
msgstr "If the number of failed login attempts is exceeded within this timeframe, the IP address and user will be blocked."

#: settings/config/fields/hardening-basic.php:108
msgid "If the username 'admin' currently exists, you can rename it here. Please note that you can no longer use this username, and should use the new username or an email address"
msgstr "If the username 'admin' currently exists, you can rename it here. Please note that you can no longer use this username, and should use the new username or an email address"

#: settings/config/fields/vulnerability-detection.php:38
msgid "If there's a vulnerability, you will also get feedback on the themes and plugin overview."
msgstr "If there's a vulnerability, you will also get feedback on the themes and plugin overview."

#: lets-encrypt/class-letsencrypt-handler.php:1685
msgid "If this is not the case, don't add this alias to your certificate."
msgstr "If this is not the case, don't add this alias to your certificate."

#: settings/config/fields/encryption.php:59
msgid "If this option is set to true, the mixed content fixer will fire on the init hook instead of the template_redirect hook. Only use this option when you experience problems with the mixed content fixer."
msgstr "If this option is set to true, the mixed content fixer will fire on the init hook instead of the template_redirect hook. Only use this option when you experience problems with the mixed content fixer."

#: security/wordpress/two-fa/class-rsssl-two-factor.php:1240
msgid "If you are the owner of this account, please check your email for instructions on regaining access."
msgstr "If you are the owner of this account, please check your email for instructions on regaining access."

#: lets-encrypt/class-letsencrypt-handler.php:478
msgid "If you entered your DNS records before, they need to be changed."
msgstr "If you entered your DNS records before, they need to be changed."

#. translators: %s is replaced with date.
#: class-admin.php:2126
msgid "If your hosting provider auto-renews your certificate, no action is required. Alternatively, you have the option to generate an SSL certificate with Really Simple Security."
msgstr "If your hosting provider auto-renews your certificate, no action is required. Alternatively, you have the option to generate an SSL certificate with Really Simple Security."

#: settings/config/menu.php:519
msgid "If your site is only intended for users to login from specific geographical regions, you can entirely prevent logins from certain continents or countries."
msgstr "If your site is only intended for users to login from specific geographical regions, you can entirely prevent logins from certain continents or countries."

#: pro/class-scan.php:1797
msgid "iFrame in the wp_postmeta database table"
msgstr "iFrame in the wp_postmeta database table"

#: class-admin.php:2284
msgid "Implement Two-Factor Authentication or Passkey login."
msgstr "Implement Two-Factor Authentication or Passkey login."

#: placeholders/class-placeholder.php:356
#: placeholders/class-placeholder.php:397
#: placeholders/class-placeholder.php:423
#: placeholders/class-placeholder.php:448
#: pro/class-scan.php:1704
#: pro/class-scan.php:1779
#: pro/class-scan.php:1817
#: pro/class-scan.php:1864
msgid "Import and insert file"
msgstr "Import and insert file"

#: security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php:74
msgid "Important security notice"
msgstr "Important security notice"

#: class-admin.php:168
msgid "Improve security - Upgrade"
msgstr "Improve security - Upgrade"

#: settings/config/menu.php:370
msgid "Improve security by requiring strong passwords and forced periodic password changes"
msgstr "Improve security by requiring strong passwords and forced periodic password changes"

#: settings/config/fields/access-control.php:20
msgid "Improve the default WordPress password strength check. You can also enforce frequent password changes for user roles."
msgstr "Improve the default WordPress password strength check. You can also enforce frequent password changes for user roles."

#: lets-encrypt/config/fields.php:145
#: lets-encrypt/config/fields.php:148
msgid "Include alias"
msgstr "Include alias"

#: settings/config/fields/security-headers.php:101
msgid "Include preload"
msgstr "Include preload"

#: settings/config/fields/security-headers.php:126
msgid "Include subdomains"
msgstr "Include subdomains"

#: pro/security/wordpress/traits/trait-rsssl-country.php:132
msgid "India"
msgstr "India"

#: pro/security/wordpress/traits/trait-rsssl-country.php:133
msgid "Indonesia"
msgstr "Indonesia"

#: pro/security/wordpress/permission-detection/permission-detection.php:217
msgid "Insecure file permissions"
msgstr "Insecure file permissions"

#: pro/security/wordpress/permission-detection/permission-detection.php:431
msgid "Insecure file permissions detected."
msgstr "Insecure file permissions detected."

#: onboarding/class-onboarding.php:212
#: onboarding/class-onboarding.php:219
#: upgrade/upgrade-to-pro.php:132
#: upgrade/upgrade-to-pro.php:145
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPluginsData.js:73
msgid "Install"
msgstr "Install"

#: upgrade/upgrade-to-pro.php:338
msgid "Install %sManually%s."
msgstr "Install %sManually%s."

#: assets/templates/two_fa/totp-config.php:18
msgid "Install Authentication app:"
msgstr "Install Authentication app:"

#: class-admin.php:2107
#: class-admin.php:2117
#: class-admin.php:2132
msgid "Install SSL certificate"
msgstr "Install SSL certificate"

#: lets-encrypt/config/fields.php:29
#: settings/config/menu.php:750
msgid "Installation"
msgstr "Installation"

#: lets-encrypt/class-letsencrypt-handler.php:1846
msgid "Installation failed."
msgstr "Installation failed."

#: upgrade/upgrade-to-pro.php:199
msgid "Installation finished"
msgstr "Installation finished"

#: upgrade/upgrade-to-pro.php:156
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPlugins.js:24
msgid "Installed"
msgstr "Installed"

#: upgrade/upgrade-to-pro.php:322
msgid "Installing"
msgstr "Installing"

#: upgrade/upgrade-to-pro.php:98
msgid "Installing plugin..."
msgstr "Installing plugin..."

#: lets-encrypt/integrations/plesk/functions.php:33
msgid "Installing SSL certificate using PLESK API..."
msgstr "Installing SSL certificate using PLESK API..."

#: lets-encrypt/integrations/cloudways/functions.php:43
msgid "Installing SSL certificate..."
msgstr "Installing SSL certificate..."

#: onboarding/class-onboarding.php:668
msgid "Instantly configure these essential features."
msgstr "Instantly configure these essential features."

#: lets-encrypt/functions.php:376
#: lets-encrypt/functions.php:377
#: settings/build/995.7a0675fe0519b06656b3.js:1
#: settings/src/Settings/SettingsGroup.js:100
msgid "Instructions"
msgstr "Instructions"

#: settings/config/fields/limit-login-attempts.php:52
msgid "Interval"
msgstr "Interval"

#: pro/security/wordpress/class-rsssl-geo-block.php:779
#: pro/security/wordpress/class-rsssl-geo-block.php:1775
#: pro/security/wordpress/class-rsssl-geo-block.php:1819
#: pro/security/wordpress/class-rsssl-geo-block.php:1928
msgid "Invalid data provided."
msgstr "Invalid data provided."

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:183
msgid "Invalid device ID"
msgstr "Invalid device ID"

#: upgrade/upgrade-to-pro.php:545
msgid "Invalid license."
msgstr "Invalid licence."

#: pro/security/wordpress/traits/trait-rsssl-api-toolbox.php:104
msgid "Invalid list type."
msgstr "Invalid list type."

#: security/wordpress/prevent-login-info-leakage.php:9
msgid "Invalid login details."
msgstr "Invalid login details."

#: pro/security/wordpress/class-rsssl-password-security.php:628
#: pro/security/wordpress/class-rsssl-password-security.php:632
#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:141
msgid "Invalid nonce."
msgstr "Invalid nonce."

#: pro/security/wordpress/two-fa/controllers/class-rsssl-totp-controller.php:66
#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:116
#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:188
msgid "Invalid provider"
msgstr "Invalid provider"

#: pro/security/wordpress/class-rsssl-geo-block.php:322
#: pro/security/wordpress/class-rsssl-geo-block.php:376
#: pro/security/wordpress/class-rsssl-geo-block.php:441
#: pro/security/wordpress/class-rsssl-geo-block.php:496
msgid "Invalid region code."
msgstr "Invalid region code."

#: pro/security/wordpress/two-fa/controllers/class-rsssl-totp-controller.php:72
#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:123
msgid "Invalid Two Factor Authentication code."
msgstr "Invalid Two Factor Authentication code."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:117
msgid "Invalid Two Factor Authentication secret key."
msgstr "Invalid Two Factor Authentication secret key."

#: security/wordpress/two-fa/class-rsssl-two-factor.php:1075
msgid "Invalid verification code."
msgstr "Invalid verification code."

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:560
msgid "IP added to blocklist"
msgstr "IP added to blocklist"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:562
msgid "IP added to trusted list"
msgstr "IP added to trusted list"

#: settings/config/fields/firewall.php:29
#: settings/config/fields/firewall.php:69
#: settings/config/fields/firewall.php:285
#: settings/config/fields/limit-login-attempts.php:182
#: settings/config/fields/limit-login-attempts.php:306
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/GeoBlockList/TrustIpAddressModal.js:89
#: settings/src/Settings/LimitLoginAttempts/AddIpAddressModal.js:81
msgid "IP Address"
msgstr "IP Address"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:311
msgid "IP address %s added to permanent blocklist"
msgstr "IP address %s added to permanent blocklist"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:305
msgid "IP address %s added to temporary blocklist"
msgstr "IP address %s added to temporary blocklist"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:317
msgid "IP address %s added to trusted IP list"
msgstr "IP address %s added to trusted IP list"

#. translators: %s: Name of the country that was removed from the blocked list.
#: pro/security/wordpress/class-rsssl-geo-block.php:1804
msgid "IP address %s is now %s."
msgstr "IP address %s is now %s."

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:314
msgid "IP address %s removed from permanent blocklist"
msgstr "IP address %s removed from permanent blocklist"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:308
msgid "IP address %s removed from temporary blocklist"
msgstr "IP address %s removed from temporary blocklist"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:320
msgid "IP address %s removed from trusted IP list"
msgstr "IP address %s removed from trusted IP list"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:555
msgid "IP address locked-out"
msgstr "IP address locked-out"

#: settings/config/fields/firewall.php:268
#: settings/config/fields/limit-login-attempts.php:275
msgid "IP address overview"
msgstr "IP address overview"

#: settings/config/menu.php:440
#: settings/config/menu.php:460
msgid "IP Addresses"
msgstr "IP Addresses"

#: settings/config/menu.php:441
msgid "IP Addresses can be allowed, blocked or will show up when your settings add them to a temporary blocklist. If you want to add your IP to the allowlist, please read the article provided at the right-hand side for instructions."
msgstr "IP Addresses can be allowed, blocked or will show up when your settings add them to a temporary blocklist. If you want to add your IP to the allowlist, please read the article provided at the right-hand side for instructions."

#: settings/config/menu.php:641
#: settings/config/menu.php:642
msgid "IP Blocklist"
msgstr "IP Blocklist"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:558
msgid "IP removed from blocklist"
msgstr "IP removed from blocklist"

#: pro/security/wordpress/class-rsssl-geo-block.php:1834
msgid "IP removed from list."
msgstr "IP removed from list."

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:564
msgid "IP removed from trusted list"
msgstr "IP removed from trusted list"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:84
msgid "iPhone or another platform authenticator"
msgstr "iPhone or another platform authenticator"

#: pro/security/wordpress/traits/trait-rsssl-country.php:134
msgid "Iran, Islamic Republic of"
msgstr "Iran, Islamic Republic of"

#: pro/security/wordpress/traits/trait-rsssl-country.php:135
msgid "Iraq"
msgstr "Iraq"

#: pro/security/wordpress/traits/trait-rsssl-country.php:136
msgid "Ireland"
msgstr "Ireland"

#: pro/security/wordpress/traits/trait-rsssl-country.php:137
msgid "Isle of Man"
msgstr "Isle of Man"

#: pro/security/wordpress/traits/trait-rsssl-country.php:138
msgid "Israel"
msgstr "Israel"

#: lets-encrypt/class-letsencrypt-handler.php:208
msgid "It is not possible to install Let's Encrypt on a localhost environment."
msgstr "It is not possible to install Let's Encrypt on a localhost environment."

#: lets-encrypt/class-letsencrypt-handler.php:218
msgid "It is not possible to install Let's Encrypt on a subfolder configuration."
msgstr "It is not possible to install Let's Encrypt on a subfolder configuration."

#: lets-encrypt/class-letsencrypt-handler.php:213
msgid "It is not possible to install Let's Encrypt on a subsite. Please go to the main site of your website."
msgstr "It is not possible to install Let's Encrypt on a subsite. Please go to the main site of your website."

#: class-wp-cli.php:83
msgid "It seems that no valid license key is activated for this domain. Activate your license key using the `%s` command, or purchase a valid license key via https://really-simple-ssl.com/pro"
msgstr "It seems that no valid license key is activated for this domain. Activate your license key using the `%s` command, or purchase a valid license key via https://really-simple-ssl.com/pro"

#: pro/security/wordpress/traits/trait-rsssl-country.php:139
msgid "Italy"
msgstr "Italy"

#: pro/security/wordpress/traits/trait-rsssl-country.php:140
msgid "Jamaica"
msgstr "Jamaica"

#: pro/security/wordpress/traits/trait-rsssl-country.php:141
msgid "Japan"
msgstr "Japan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:142
msgid "Jersey"
msgstr "Jersey"

#: pro/security/wordpress/traits/trait-rsssl-country.php:143
msgid "Jordan"
msgstr "Jordan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:144
msgid "Kazakhstan"
msgstr "Kazakhstan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:145
msgid "Kenya"
msgstr "Kenya"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:669
#: security/wordpress/two-fa/class-rsssl-two-factor.php:1436
msgid "Key copied"
msgstr "Key copied"

#: pro/security/wordpress/traits/trait-rsssl-country.php:146
msgid "Kiribati"
msgstr "Kiribati"

#: pro/security/wordpress/traits/trait-rsssl-country.php:147
msgid "Korea, Democratic People's Republic of"
msgstr "Korea, Democratic People's Republic of"

#: pro/security/wordpress/traits/trait-rsssl-country.php:148
msgid "Korea, Republic of"
msgstr "Korea, Republic of"

#: pro/security/wordpress/traits/trait-rsssl-country.php:149
msgid "Kosovo"
msgstr "Kosovo"

#: pro/security/wordpress/traits/trait-rsssl-country.php:150
msgid "Kuwait"
msgstr "Kuwait"

#: pro/security/wordpress/traits/trait-rsssl-country.php:151
msgid "Kyrgyzstan"
msgstr "Kyrgyzstan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:152
msgid "Lao People's Democratic Republic"
msgstr "Lao People's Democratic Republic"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:30
msgid "Last Used"
msgstr "Last Used"

#: pro/security/wordpress/traits/trait-rsssl-country.php:153
msgid "Latvia"
msgstr "Latvia"

#: settings/config/fields/firewall.php:107
msgid "Lax - 10 errors in 2 seconds"
msgstr "Lax - 10 errors in 2 seconds"

#: mailer/class-mail.php:33
#: mailer/class-mail.php:37
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js:192
msgid "Learn more"
msgstr "Learn more"

#: mailer/class-mail.php:35
msgid "Learn more about our features!"
msgstr "Learn more about our features!"

#: pro/csp-violation-endpoint.php:217
msgid "Learning Mode is active for your Content Security Policy and will complete in %s days."
msgstr "Learning Mode is active for your Content Security Policy and will complete in %s days."

#: class-admin.php:1780
msgid "Leave a review"
msgstr "Leave a review"

#: pro/security/wordpress/traits/trait-rsssl-country.php:154
msgid "Lebanon"
msgstr "Lebanon"

#: pro/security/wordpress/traits/trait-rsssl-country.php:155
msgid "Lesotho"
msgstr "Lesotho"

#: settings/config/menu.php:68
#: settings/build/index.a4cc556db77e3384994b.js:1
#: settings/src/Header.js:32
msgid "Let's Encrypt"
msgstr "Let's Encrypt"

#: settings/config/menu.php:69
msgid "Let's Encrypt."
msgstr "Let's Encrypt."

#: settings/config/menu.php:718
msgid "Letʼs Encrypt is a free, automated and open certificate authority brought to you by the nonprofit Internet Security Research Group (ISRG)."
msgstr "Letʼs Encrypt is a free, automated and open certificate authority brought to you by the nonprofit Internet Security Research Group (ISRG)."

#: settings/config/fields/security-headers.php:86
msgid "Leveraging your SSL certificate with HSTS is a staple for every website. Force your website over SSL, mitigating risks of malicious counterfeit websites in your name."
msgstr "Leveraging your SSL certificate with HSTS is a staple for every website. Force your website over SSL, mitigating risks of malicious counterfeit websites in your name."

#: pro/security/wordpress/traits/trait-rsssl-country.php:156
msgid "Liberia"
msgstr "Liberia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:157
msgid "Libyan Arab Jamahiriya"
msgstr "Libyan Arab Jamahiriya"

#: pro/class-licensing.php:184
#: pro/class-licensing.php:206
#: pro/class-licensing.php:722
#: pro/class-licensing.php:729
#: pro/class-licensing.php:736
#: pro/class-licensing.php:743
#: pro/class-licensing.php:750
#: pro/class-licensing.php:765
msgid "License"
msgstr "Licence"

#: upgrade/upgrade-to-pro.php:86
msgid "License invalid"
msgstr "Licence invalid"

#: upgrade/upgrade-to-pro.php:85
msgid "License valid"
msgstr "Licence valid"

#: pro/security/wordpress/traits/trait-rsssl-country.php:158
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: settings/config/menu.php:397
msgid "Limit Attempts"
msgstr "Limit Attempts"

#: settings/config/fields/access-control.php:81
msgid "Limit logged in session duration"
msgstr "Limit logged in session duration"

#: onboarding/class-onboarding.php:435
#: onboarding/class-onboarding.php:477
#: settings/config/menu.php:376
#: settings/config/menu.php:384
#: modal/build/index.d4bca8705bbc6e3e5777.js:1
#: modal/src/components/DeactivationModal/DeactivationModal.js:96
msgid "Limit Login Attempts"
msgstr "Limit Login Attempts"

#: pro/security/notices.php:74
msgid "Limit Login Attempts enabled."
msgstr "Limit Login Attempts enabled."

#: class-site-health.php:67
msgid "Limit Login Attempts Protection"
msgstr "Limit Login Attempts Protection"

#: settings/config/fields/limit-login-attempts.php:18
msgid "Limit Login Attempts protects your site from login attempts by unauthorized users. When you enable Limit Login Attempts, all login attempts are logged and repeated attempts to login with invalid credentials will be blocked automatically."
msgstr "Limit Login Attempts protects your site from login attempts by unauthorised users. When you enable Limit Login Attempts, all login attempts are logged and repeated attempts to login with invalid credentials will be blocked automatically."

#: pro/security/wordpress/traits/trait-rsssl-country.php:159
msgid "Lithuania"
msgstr "Lithuania"

#: settings/config/fields/encryption.php:107
#: settings/config/fields/security-headers.php:386
msgid "Location"
msgstr "Location"

#: settings/config/fields/firewall.php:214
#: settings/config/fields/limit-login-attempts.php:123
#: settings/config/fields/limit-login-attempts.php:168
#: settings/config/fields/limit-login-attempts.php:214
msgid "Locked-out"
msgstr "Locked-out"

#: settings/config/fields/firewall.php:123
#: settings/config/fields/limit-login-attempts.php:76
msgid "Lockout duration"
msgstr "Lockout duration"

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:358
msgid "Log In"
msgstr "Log In"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:67
msgid "Log in with Passkey"
msgstr "Log in with Passkey"

#: settings/config/fields/limit-login-attempts.php:29
msgid "Login attempts"
msgstr "Login attempts"

#: settings/config/menu.php:283
#: settings/config/menu.php:291
msgid "Login Authentication"
msgstr "Login Authentication"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:577
msgid "Login blocked by Geo-IP list"
msgstr "Login blocked by Geo-IP list"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:575
msgid "Login blocked by IP address"
msgstr "Login blocked by IP address"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:572
msgid "Login blocked by username"
msgstr "Login blocked by username"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:258
msgid "Login by %s failed (incorrect credentials)"
msgstr "Login by %s failed (incorrect credentials)"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:255
msgid "Login by %s was successful"
msgstr "Login by %s was successful"

#: lets-encrypt/integrations/cpanel/cpanel.php:298
msgid "Login credentials incorrect"
msgstr "Login credentials incorrect"

#: lets-encrypt/integrations/cpanel/cpanel.php:113
msgid "Login credentials incorrect. Please check your login credentials for cPanel."
msgstr "Login credentials incorrect. Please check your log-in credentials for cPanel."

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:522
msgid "Login failed"
msgstr "Login failed"

#. translators: %s is replaced with the country name.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:357
msgid "Login failed (Country %s blocked by geo-IP blocklist )"
msgstr "Login failed (Country %s blocked by geo-IP blocklist )"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:361
msgid "Login failed (incorrect MFA code)"
msgstr "Login failed (incorrect MFA code)"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:353
msgid "Login failed (IP %s found in permanent blocklist)"
msgstr "Login failed (IP %s found in permanent blocklist)"

#. translators: %s is replaced with the IP address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:350
msgid "Login failed (IP %s found in temporary blocklist)"
msgstr "Login failed (IP %s found in temporary blocklist)"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:345
msgid "Login failed (User %s found in permanent blocklist)"
msgstr "Login failed (User %s found in permanent blocklist)"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:339
msgid "Login failed (User %s found in temporary blocklist)"
msgstr "Login failed (User %s found in temporary blocklist)"

#: settings/config/menu.php:273
#: modal/build/index.d4bca8705bbc6e3e5777.js:1
#: modal/src/components/DeactivationModal/DeactivationModal.js:86
msgid "Login Protection"
msgstr "Login Protection"

#: assets/templates/two_fa/profile-settings.php:20
msgid "Login protection"
msgstr "Login protection"

#: settings/config/fields/hardening-xml.php:55
msgid "Login status"
msgstr "Login status"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:520
msgid "Login successful"
msgstr "Login successful"

#: pro/settings/sync-settings.php:83
#: pro/settings/sync-settings.php:90
msgid "Login URL already changed by something else than Really Simple Security"
msgstr "Login URL already changed by something else than Really Simple Security"

#: pro/security/wordpress/change-login-url.php:37
msgid "Login URL request"
msgstr "Login URL request"

#: settings/config/menu.php:668
msgid "Logs"
msgstr "Logs"

#: pro/class-scan.php:370
msgid "Looking up blocked resources in files, %s of %s"
msgstr "Looking up blocked resources in files, %s of %s"

#: pro/class-scan.php:397
msgid "Looking up blocked resources in postmeta"
msgstr "Looking up blocked resources in postmeta"

#: pro/class-scan.php:384
msgid "Looking up blocked resources in posts"
msgstr "Looking up blocked resources in posts"

#: pro/class-scan.php:412
msgid "Looking up blocked resources in widgets"
msgstr "Looking up blocked resources in widgets"

#: settings/config/fields/vulnerability-detection.php:103
#: settings/config/fields/vulnerability-detection.php:194
msgid "Low-risk"
msgstr "Low-risk"

#: security/hardening.php:18
#: security/wordpress/vulnerabilities.php:84
msgid "low-risk"
msgstr "low-risk"

#: settings/config/fields/vulnerability-detection.php:81
msgid "Low-risk "
msgstr "Low-risk "

#: settings/config/fields/vulnerability-detection.php:59
msgid "Low-risk (default)"
msgstr "Low-risk (default)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:160
msgid "Luxembourg"
msgstr "Luxembourg"

#: pro/security/wordpress/traits/trait-rsssl-country.php:161
msgid "Macao"
msgstr "Macao"

#: pro/security/wordpress/traits/trait-rsssl-country.php:162
msgid "Macedonia, the Former Yugoslav Republic of"
msgstr "Macedonia, the Former Yugoslav Republic of"

#: pro/security/wordpress/traits/trait-rsssl-country.php:163
msgid "Madagascar"
msgstr "Madagascar"

#: settings/config/menu.php:225
msgid "Maintain peace of mind with our simple, but effective automated measures when vulnerabilities are discovered. When needed Really Simple Security will force update or quarantaine vulnerable components, on your terms!"
msgstr "Maintain peace of mind with our simple, but effective automated measures when vulnerabilities are discovered. When needed Really Simple Security will force update or quarantaine vulnerable components, on your terms!"

#: pro/security/wordpress/traits/trait-rsssl-country.php:164
msgid "Malawi"
msgstr "Malawi"

#: pro/security/wordpress/traits/trait-rsssl-country.php:165
msgid "Malaysia"
msgstr "Malaysia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:166
msgid "Maldives"
msgstr "Maldives"

#: pro/security/wordpress/traits/trait-rsssl-country.php:167
msgid "Mali"
msgstr "Mali"

#: pro/security/wordpress/traits/trait-rsssl-country.php:168
msgid "Malta"
msgstr "Malta"

#: pro/class-licensing.php:185
msgid "Manage your license here."
msgstr "Manage your license here."

#: pro/csp-violation-endpoint.php:94
msgid "Manual"
msgstr "Manual"

#: pro/security/wordpress/block-admin-creation.php:318
msgid "Manual approval required"
msgstr "Manual approval required"

#: settings/config/fields/hardening-extended.php:44
msgid "Many vulnerabilities are exploited by injecting a user with administrator capabilities outside of the native WordPress creation process. Under advanced hardening you can prevent this from happening."
msgstr "Many vulnerabilities are exploited by injecting a user with administrator capabilities outside of the native WordPress creation process. Under advanced hardening you can prevent this from happening."

#: pro/security/wordpress/traits/trait-rsssl-country.php:169
msgid "Marshall Islands"
msgstr "Marshall Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:170
msgid "Martinique"
msgstr "Martinique"

#: pro/security/wordpress/traits/trait-rsssl-country.php:171
msgid "Mauritania"
msgstr "Mauritania"

#: pro/security/wordpress/traits/trait-rsssl-country.php:172
msgid "Mauritius"
msgstr "Mauritius"

#: pro/security/wordpress/limitlogin/class-rsssl-geo-location.php:82
msgid "MaxMind GeoIP database not installed"
msgstr "MaxMind GeoIP database not installed"

#: class-admin.php:1784
msgid "Maybe later"
msgstr "Maybe later"

#: pro/security/wordpress/traits/trait-rsssl-country.php:173
msgid "Mayotte"
msgstr "Mayotte"

#: settings/config/menu.php:220
msgid "Measures"
msgstr "Measures"

#: settings/config/fields/vulnerability-detection.php:60
#: settings/config/fields/vulnerability-detection.php:82
#: settings/config/fields/vulnerability-detection.php:104
#: settings/config/fields/vulnerability-detection.php:195
msgid "Medium-risk"
msgstr "Medium-risk"

#: security/hardening.php:19
#: security/wordpress/vulnerabilities.php:85
msgid "medium-risk"
msgstr "medium-risk"

#: settings/config/fields/hardening-xml.php:48
#: settings/config/fields/two-fa.php:171
msgid "Method"
msgstr "Method"

#: pro/security/wordpress/traits/trait-rsssl-country.php:174
msgid "Mexico"
msgstr "Mexico"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:361
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:366
msgid "MFA"
msgstr "MFA"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:366
msgid "MFA setup required"
msgstr "MFA setup required"

#: pro/security/wordpress/traits/trait-rsssl-country.php:175
msgid "Micronesia, Federated States of"
msgstr "Micronesia, Federated States of"

#: pro/security/wordpress/class-rsssl-geo-block.php:1366
#: pro/security/wordpress/class-rsssl-geo-block.php:1402
#: pro/security/wordpress/limitlogin/class-rsssl-admin-config-countries.php:165
msgid "Missing filter value."
msgstr "Missing filter value."

#: upgrade/upgrade-to-pro.php:542
msgid "Missing license."
msgstr "Missing licence."

#: pro/security/wordpress/class-rsssl-geo-block.php:631
#: pro/security/wordpress/class-rsssl-geo-block.php:675
#: pro/security/wordpress/class-rsssl-geo-block.php:836
msgid "Missing or invalid country data."
msgstr "Missing or invalid country data."

#: pro/security/wordpress/class-rsssl-geo-block.php:317
#: pro/security/wordpress/class-rsssl-geo-block.php:371
#: pro/security/wordpress/class-rsssl-geo-block.php:436
#: pro/security/wordpress/class-rsssl-geo-block.php:491
msgid "Missing or invalid region data."
msgstr "Missing or invalid region data."

#: settings/config/menu.php:82
msgid "Mixed content"
msgstr "Mixed content"

#: onboarding/class-onboarding.php:411
msgid "Mixed Content Fixer"
msgstr "Mixed Content Fixer"

#: settings/config/fields/encryption.php:46
msgid "Mixed content fixer"
msgstr "Mixed content fixer"

#: settings/config/fields/encryption.php:73
msgid "Mixed content fixer - back-end"
msgstr "Mixed content fixer - back end"

#: settings/config/fields/encryption.php:55
msgid "Mixed content fixer - init hook"
msgstr "Mixed content fixer - init hook"

#: class-admin.php:2164
msgid "Mixed content fixer not enabled. Enable the option to fix mixed content on your site."
msgstr "Mixed content fixer not enabled. Enable the option to fix mixed content on your site."

#: pro/class-scan.php:1847
msgid "Mixed content found in a widget. Press the edit link to edit the widget manually."
msgstr "Mixed content found in a widget. Press the edit link to edit the widget manually."

#: pro/class-scan.php:1762
msgid "Mixed content found in in a post. Can be fixed automatically by pressing the fix button. Pressing the edit button allows you to update the link in the post manually."
msgstr "Mixed content found in in a post. Can be fixed automatically by pressing the fix button. Pressing the edit button allows you to update the link in the post manually."

#: pro/class-scan.php:1799
msgid "Mixed content from a postmeta table in your database. Usually won't cause any mixed content on the front-end. Check the post if it causes mixed content. If so, the link can be replace directly in the database."
msgstr "Mixed content from a postmeta table in your database. Usually won't cause any mixed content on the front-end. Check the post if it causes mixed content. If so, the link can be replace directly in the database."

#: placeholders/class-placeholder.php:343
#: pro/class-scan.php:1691
msgid "Mixed content in %s"
msgstr "Mixed content in %s"

#: placeholders/class-placeholder.php:368
#: pro/class-scan.php:1730
msgid "Mixed content in CSS/JS file from other domain"
msgstr "Mixed content in CSS/JS file from other domain"

#: placeholders/class-placeholder.php:327
#: pro/class-scan.php:1659
msgid "Mixed content in PHP file in %s"
msgstr "Mixed content in PHP file in %s"

#: placeholders/class-placeholder.php:385
#: pro/class-scan.php:1767
msgid "Mixed content in post: %s"
msgstr "Mixed content in post: %s"

#: placeholders/class-placeholder.php:410
#: pro/class-scan.php:1797
msgid "Mixed content in the postmeta table"
msgstr "Mixed content in the postmeta table"

#: pro/class-scan.php:1725
msgid "Mixed content resources: %s"
msgstr "Mixed content resources: %s"

#: settings/config/menu.php:88
#: settings/config/menu.php:90
msgid "Mixed Content Scan"
msgstr "Mixed Content Scan"

#: settings/config/fields/encryption.php:85
msgid "Mixed content scan"
msgstr "Mixed content scan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:176
msgid "Moldova, Republic of"
msgstr "Moldova, Republic of"

#: pro/security/wordpress/traits/trait-rsssl-country.php:177
msgid "Monaco"
msgstr "Monaco"

#: pro/security/wordpress/traits/trait-rsssl-country.php:178
msgid "Mongolia"
msgstr "Mongolia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:179
msgid "Montenegro"
msgstr "Montenegro"

#: pro/security/wordpress/traits/trait-rsssl-country.php:180
msgid "Montserrat"
msgstr "Montserrat"

#: class-admin.php:665
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/build/995.7a0675fe0519b06656b3.js:1
#: settings/src/Dashboard/TaskElement.js:65
#: settings/src/Settings/Help.js:24
msgid "More info"
msgstr "More info"

#: pro/security/wordpress/traits/trait-rsssl-country.php:181
msgid "Morocco"
msgstr "Morocco"

#: pro/security/wordpress/traits/trait-rsssl-country.php:182
msgid "Mozambique"
msgstr "Mozambique"

#: pro/class-scan.php:1609
msgid "mu plugin file"
msgstr "mu plugin file"

#: security/wordpress/vulnerabilities.php:1568
msgid "Multiple %s vulnerabilities have been found."
msgstr "Multiple %s vulnerabilities have been found."

#: pro/class-licensing.php:586
msgid "Multisite detected. Please upgrade to %smultisite%s."
msgstr "Multisite detected. Please upgrade to %smultisite%s."

#: pro/security/wordpress/traits/trait-rsssl-country.php:183
msgid "Myanmar"
msgstr "Myanmar"

#: class-wp-cli.php:1258
msgid "Name of the option to update."
msgstr "Name of the option to update."

#: pro/security/wordpress/traits/trait-rsssl-country.php:184
msgid "Namibia"
msgstr "Namibia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:185
msgid "Nauru"
msgstr "Nauru"

#: pro/security/wordpress/traits/trait-rsssl-country.php:186
msgid "Nepal"
msgstr "Nepal"

#: pro/security/wordpress/traits/trait-rsssl-country.php:187
msgid "Netherlands"
msgstr "Netherlands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:188
msgid "Netherlands Antilles"
msgstr "Netherlands Antilles"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:79
msgid "Network error, please try again later."
msgstr "Network error, please try again later."

#: pro/class-scan.php:208
#: pro/class-scan.php:1927
msgid "Never"
msgstr "Never"

#: pro/security/wordpress/traits/trait-rsssl-country.php:189
msgid "New Caledonia"
msgstr "New Caledonia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:190
msgid "New Zealand"
msgstr "New Zealand"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:82
msgid "NFC device"
msgstr "NFC device"

#: pro/security/wordpress/traits/trait-rsssl-country.php:191
msgid "Nicaragua"
msgstr "Nicaragua"

#: pro/security/wordpress/traits/trait-rsssl-country.php:192
msgid "Niger"
msgstr "Niger"

#: pro/security/wordpress/traits/trait-rsssl-country.php:193
msgid "Nigeria"
msgstr "Nigeria"

#: pro/security/wordpress/traits/trait-rsssl-country.php:194
msgid "Niue"
msgstr "Niue"

#: pro/security/wordpress/class-rsssl-limit-login-admin.php:336
msgid "No %1s were added to %2s"
msgstr "No %1s were added to %2s"

#: class-admin.php:2188
msgid "No 301 redirect is set. Enable the WordPress 301 redirect in the settings to get a 301 permanent redirect."
msgstr "No 301 redirect is set. Enable the WordPress 301 redirect in the settings to get a 301 permanent redirect."

#: class-site-health.php:353
msgid "No 301 redirect to SSL enabled."
msgstr "No 301 redirect to SSL enabled."

#: pro/security/wordpress/permission-detection/download.php:34
msgid "No files with wrong permissions found"
msgstr "No files with wrong permissions found"

#: security/wordpress/vulnerabilities.php:296
#: security/wordpress/vulnerabilities.php:304
msgid "No known vulnerabilities detected"
msgstr "No known vulnerabilities detected"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:71
msgid "No passkey found for this device, please login with username and password."
msgstr "No passkey found for this device, please login with username and password."

#: class-admin.php:1987
msgid "No recommended redirect rules detected."
msgstr "No recommended redirect rules detected."

#: settings/config/fields/encryption.php:17
msgid "No redirect"
msgstr "No redirect"

#: pro/class-admin.php:616
msgid "No redirect to http detected."
msgstr "No redirect to http detected."

#: lets-encrypt/class-letsencrypt-handler.php:1325
#: lets-encrypt/class-letsencrypt-handler.php:1669
msgid "no response"
msgstr "no response"

#: onboarding/class-onboarding.php:294
msgid "No SSL certificate has been detected."
msgstr "No SSL certificate has been detected."

#: class-admin.php:2102
#: class-site-health.php:344
msgid "No SSL detected"
msgstr "No SSL detected"

#: class-admin.php:2103
msgid "No SSL detected. Use the retry button to check again."
msgstr "No SSL detected. Use the retry button to check again."

#: lets-encrypt/class-letsencrypt-handler.php:1632
msgid "No subdomain setup detected."
msgstr "No subdomain setup detected."

#: lets-encrypt/integrations/cpanel/cpanel.php:66
#: lets-encrypt/integrations/directadmin/directadmin.php:66
msgid "No valid list of domains."
msgstr "No valid list of domains."

#: security/wordpress/two-fa/class-rsssl-two-factor-settings.php:644
#: security/wordpress/two-fa/class-rsssl-two-factor-settings.php:662
#: settings/config/fields/vulnerability-detection.php:58
#: settings/config/fields/vulnerability-detection.php:80
#: settings/config/fields/vulnerability-detection.php:102
#: settings/config/fields/vulnerability-detection.php:193
msgid "None"
msgstr "None"

#: pro/security/wordpress/traits/trait-rsssl-country.php:195
msgid "Norfolk Island"
msgstr "Norfolk Island"

#: settings/config/fields/firewall.php:108
msgid "Normal - 10 errors in 5 seconds"
msgstr "Normal - 10 errors in 5 seconds"

#: pro/security/wordpress/traits/trait-rsssl-country.php:296
msgid "North America"
msgstr "North America"

#: pro/security/wordpress/traits/trait-rsssl-country.php:196
msgid "Northern Mariana Islands"
msgstr "Northern Mariana Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:197
msgid "Norway"
msgstr "Norway"

#: class-site-health.php:292
msgid "Not all essential security headers are installed"
msgstr "Not all essential security headers are installed"

#: lets-encrypt/class-letsencrypt-handler.php:1841
msgid "Not recognized server."
msgstr "Not recognised server."

#. translators: %s: Not required.
#: pro/security/wordpress/class-rsssl-password-security.php:198
msgid "Not required"
msgstr "Not required"

#: security/wordpress/two-fa/class-rsssl-two-factor-settings.php:632
msgid "not set"
msgstr "not set"

#: settings/config/menu.php:262
msgid "Not sure if you're using XML-RPC, or want to restrict unauthorized use of XML-RPC? With learning mode you can see exactly which sources use XML-RPC, and you can revoke where necessary."
msgstr "Not sure if you're using XML-RPC, or want to restrict unauthorised use of XML-RPC? With learning mode you can see exactly which sources use XML-RPC, and you can revoke where necessary."

#: settings/config/fields/firewall.php:36
#: settings/config/fields/firewall.php:76
#: settings/config/fields/firewall.php:187
msgid "Note"
msgstr "Note"

#. translators: %s is replaced with the site url
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:327
#: security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php:117
#: security/wordpress/two-fa/traits/trait-rsssl-email-trait.php:142
msgid "Notification by %s"
msgstr "Notification by %s"

#: mailer/class-mail.php:32
msgid "Notification by Really Simple Security"
msgstr "Notification by Really Simple Security"

#: settings/config/menu.php:213
#: settings/build/995.7a0675fe0519b06656b3.js:1
#: settings/src/Settings/Settings.js:164
msgid "Notifications"
msgstr "Notifications"

#: settings/config/fields/general.php:99
msgid "Notifications by email"
msgstr "Notifications by email"

#: pro/security/wordpress/traits/trait-rsssl-country.php:297
msgid "Oceania"
msgstr "Oceania"

#: lets-encrypt/class-letsencrypt-handler.php:760
msgid "OCSP not supported, the certificate will be generated without OCSP."
msgstr "OCSP not supported, the certificate will be generated without OCSP."

#: lets-encrypt/config/fields.php:124
msgid "OCSP stapling is configured as enabled by default. You can disable this option if this is not supported by your hosting provider."
msgstr "OCSP stapling is configured as enabled by default. You can disable this option if this is not supported by your hosting provider."

#: settings/config/fields/security-headers.php:44
#: settings/config/fields/security-headers.php:59
#: settings/config/fields/security-headers.php:162
#: settings/config/fields/security-headers.php:184
#: settings/config/fields/security-headers.php:199
msgid "Off"
msgstr "Off"

#: pro/security/wordpress/traits/trait-rsssl-country.php:198
msgid "Oman"
msgstr "Oman"

#: settings/config/disable-fields-filter.php:15
msgid "On Apache you can use a .htaccess redirect, which is usually faster, but may cause issues on some configurations. Read the instructions in the sidebar first."
msgstr "On Apache you can use a .htaccess redirect, which is usually faster, but may cause issues on some configurations. Read the instructions in the sidebar first."

#: upgrade/upgrade-to-pro.php:143
msgid "One click SSL optimization"
msgstr "One click SSL optimisation"

#: settings/config/fields/security-headers.php:142
msgid "One day (for testing only)"
msgstr "One day (for testing only)"

#: settings/config/fields/security-headers.php:171
msgid "One of the most powerful features, and therefore the most complex are the Cross-Origin headers that can isolate your website so any data leaks are minimized."
msgstr "One of the most powerful features, and therefore the most complex are the Cross-Origin headers that can isolate your website so any data leaks are minimised."

#: settings/config/fields/security-headers.php:143
msgid "One year"
msgstr "One year"

#: onboarding/class-onboarding.php:342
#: settings/settings.php:595
msgid "Online Booking System"
msgstr "Online Booking System"

#: settings/config/fields/encryption.php:74
msgid "Only enable this if you experience mixed content in the admin environment of your WordPress website."
msgstr "Only enable this if you experience mixed content in the admin environment of your WordPress website."

#: class-admin.php:1945
#: pro/class-licensing.php:654
#: pro/security/wordpress/class-rsssl-geo-block.php:1529
msgid "Open"
msgstr "Open"

#: class-wp-cli.php:1395
msgid "Optional note for the block."
msgstr "Optional note for the block."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:93
msgid "or"
msgstr "or"

#: lets-encrypt/class-letsencrypt-handler.php:475
msgid "Order ID mismatch, regenerate order."
msgstr "Order ID mismatch, regenerate order."

#: lets-encrypt/class-letsencrypt-handler.php:878
msgid "Order successfully created."
msgstr "Order successfully created."

#: lets-encrypt/class-letsencrypt-handler.php:894
msgid "Order successfully retrieved."
msgstr "Order successfully retrieved."

#: upgrade/upgrade-to-pro.php:92
msgid "Package information retrieved"
msgstr "Package information retrieved"

#: pro/security/wordpress/traits/trait-rsssl-country.php:199
msgid "Pakistan"
msgstr "Pakistan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:200
msgid "Palau"
msgstr "Palau"

#: pro/security/wordpress/traits/trait-rsssl-country.php:201
msgid "Palestinian Territory, Occupied"
msgstr "Palestinian Territory, Occupied"

#: pro/security/wordpress/traits/trait-rsssl-country.php:202
msgid "Panama"
msgstr "Panama"

#: pro/security/wordpress/traits/trait-rsssl-country.php:203
msgid "Papua New Guinea"
msgstr "Papua New Guinea"

#: pro/security/wordpress/traits/trait-rsssl-country.php:204
msgid "Paraguay"
msgstr "Paraguay"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:207
#: settings/config/menu.php:311
msgid "Passkey"
msgstr "Passkey"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:75
msgid "Passkey configuration"
msgstr "Passkey configuration"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:74
msgid "Passkey login successful"
msgstr "Passkey login successful"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:110
msgid "Passkeys"
msgstr "Passkeys"

#: settings/config/fields/two-fa.php:51
msgid "Passkeys are a very secure and convenient way to log in. It allows the user to authenticate using their device, browser or password manager."
msgstr "Passkeys are a very secure and convenient way to log in. It allows the user to authenticate using their device, browser or password manager."

#: pro/security/wordpress/class-rsssl-password-security.php:158
msgid "Password Expires On"
msgstr "Password Expires On"

#: security/wordpress/two-fa/class-rsssl-two-factor.php:1247
msgid "Password Reset"
msgstr "Password Reset"

#: settings/config/menu.php:359
msgid "Password Security"
msgstr "Password Security"

#: settings/config/menu.php:365
msgid "Password security"
msgstr "Password security"

#: settings/config/menu.php:369
msgid "Passwords"
msgstr "Passwords"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:87
msgid "Pending..."
msgstr "Pending..."

#: class-admin.php:3325
msgid "Performant Firewall"
msgstr "Performant Firewall"

#: pro/security/notices.php:93
msgid "Performant Firewall enabled."
msgstr "Performant Firewall enabled."

#: pro/security/wordpress/class-rsssl-geo-block.php:998
#: settings/config/menu.php:659
msgid "Permanent"
msgstr "Permanent"

#: settings/config/menu.php:413
#: settings/config/menu.php:448
msgid "Permanent block"
msgstr "Permanent block"

#: lets-encrypt/class-le-restapi.php:166
#: lets-encrypt/class-le-restapi.php:227
msgid "Permission denied."
msgstr "Permission denied."

#: settings/config/fields/security-headers.php:215
msgid "Permissions Policy"
msgstr "Permissions Policy"

#: pro/security/wordpress/traits/trait-rsssl-country.php:205
msgid "Peru"
msgstr "Peru"

#: pro/security/wordpress/traits/trait-rsssl-country.php:206
msgid "Philippines"
msgstr "Philippines"

#: pro/security/wordpress/traits/trait-rsssl-country.php:207
msgid "Pitcairn"
msgstr "Pitcairn"

#: onboarding/class-onboarding.php:672
msgid "Please %sconsider upgrading to Pro%s to enjoy all simple and performant security features."
msgstr "Please %sconsider upgrading to Pro%s to enjoy all simple and performant security features."

#: lets-encrypt/functions.php:381
msgid "Please activate it manually on your hosting dashboard."
msgstr "Please activate it manually on your hosting dashboard."

#: lets-encrypt/functions.php:384
msgid "Please activate it on your dashboard %smanually%s"
msgstr "Please activate it on your dashboard %smanually%s"

#: pro/class-licensing.php:608
msgid "Please activate your license key."
msgstr "Please activate your licence key."

#: security/notices.php:33
#: security/notices.php:41
#: security/notices.php:60
msgid "Please add the following lines to your .htaccess, or set it to writable:"
msgstr "Please add the following lines to your .htaccess, or set it to writable:"

#: lets-encrypt/class-letsencrypt-handler.php:222
msgid "Please adjust the CAA records via your DNS provider to allow Let’s Encrypt SSL certificates"
msgstr "Please adjust the CAA records via your DNS provider to allow Let’s Encrypt SSL certificates"

#: lets-encrypt/class-letsencrypt-handler.php:395
msgid "Please change your email address %shere%s and try again."
msgstr "Please change your email address %shere%s and try again."

#: security/firewall-manager.php:649
msgid "Please check if the advanced-headers.php file is included in the wp-config.php, and exists in the wp-content folder."
msgstr "Please check if the advanced-headers.php file is included in the wp-config.php, and exists in the wp-content folder."

#: lets-encrypt/class-letsencrypt-handler.php:1681
msgid "Please check if the non www version of your site also points to this website."
msgstr "Please check if the non www version of your site also points to this website."

#: lets-encrypt/class-letsencrypt-handler.php:1683
msgid "Please check if the www version of your site also points to this website."
msgstr "Please check if the www version of your site also points to this website."

#: class-admin.php:2349
msgid "Please check if your REST API is loading correctly. Your site currently is using the slower Ajax fallback method to load the settings."
msgstr "Please check if your REST API is loading correctly. Your site currently is using the slower Ajax fallback method to load the settings."

#: security/wordpress/vulnerabilities.php:325
msgid "Please check the vulnerabilities overview for more information and take appropriate action."
msgstr "Please check the vulnerabilities overview for more information and take appropriate action."

#: lets-encrypt/functions.php:383
msgid "Please complete %smanually%s"
msgstr "Please complete %smanually%s"

#: lets-encrypt/functions.php:380
msgid "Please complete manually in your hosting dashboard."
msgstr "Please complete manually in your hosting dashboard."

#: lets-encrypt/class-letsencrypt-handler.php:1154
msgid "Please complete the following step(s) first: %s"
msgstr "Please complete the following step(s) first: %s"

#: settings/config/fields/firewall.php:150
#: settings/config/fields/limit-login-attempts.php:105
msgid "Please configure your %sCaptcha settings%s before enabling this setting"
msgstr "Please configure your %sCaptcha settings%s before enabling this setting"

#: lets-encrypt/class-letsencrypt-handler.php:1715
msgid "Please create a folder 'rsssl' in the uploads directory, with 644 permissions."
msgstr "Please create a folder 'rsssl' in the uploads directory, with 644 permissions."

#: lets-encrypt/class-letsencrypt-handler.php:645
msgid "Please double check your DNS txt record."
msgstr "Please double check your DNS txt record."

#: pro/security/wordpress/vulnerabilities-pro.php:608
#: pro/security/wordpress/vulnerabilities-pro.php:638
#: pro/security/wordpress/vulnerabilities-pro.php:666
#: pro/security/wordpress/vulnerabilities-pro.php:698
#: pro/security/wordpress/vulnerabilities-pro.php:728
#: pro/security/wordpress/vulnerabilities-pro.php:759
msgid "Please double-check if your website is working as expected."
msgstr "Please double-check if your website is working as expected."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:441
msgid "Please enter the code generated by your authenticator app."
msgstr "Please enter the code generated by your authenticator app."

#: pro/class-licensing.php:600
msgid "Please enter your license key. Available in your %saccount%s."
msgstr "Please enter your licence key. Available in your %saccount%s."

#: security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php:86
msgid "Please login"
msgstr "Please login"

#: assets/templates/two_fa/onboarding.php:72
msgid "Please make sure to configure a method, access to your account will be denied if no method is configured today."
msgstr "Please make sure to configure a method, access to your account will be denied if no method is configured today."

#: assets/templates/two_fa/onboarding.php:67
msgid "Please make sure to configure a method, access to your account will be denied if no method is configured within the next %s days."
msgstr "Please make sure to configure a method, access to your account will be denied if no method is configured within the next %s days."

#: pro/class-scan.php:2197
msgid "Please note that any changes you have made since to your current files, will be lost."
msgstr "Please note that any changes you have made since to your current files, will be lost."

#: onboarding/class-onboarding.php:294
msgid "Please refresh the SSL status if a certificate has been installed recently."
msgstr "Please refresh the SSL status if a certificate has been installed recently."

#: security/firewall-manager.php:627
msgid "Please set the wp-config.php to writable until the rule has been written."
msgstr "Please set the wp-config.php to writable until the rule has been written."

#: security/firewall-manager.php:633
msgid "Please set the wp-content folder to writable:"
msgstr "Please set the wp-content folder to writable:"

#: lets-encrypt/class-letsencrypt-handler.php:468
#: lets-encrypt/class-letsencrypt-handler.php:489
msgid "Please start at the previous step."
msgstr "Please start at the previous step."

#: security/wordpress/vulnerabilities.php:372
msgid "Please take appropriate action."
msgstr "Please take appropriate action."

#: pro/security/wordpress/firewall/class-rsssl-404-interceptor.php:335
msgid "Please verify that you are human"
msgstr "Please verify that you are human"

#: mailer/class-mail.php:116
msgid "Please verify your email"
msgstr "Please verify your email"

#: lets-encrypt/class-letsencrypt-handler.php:573
msgid "Please wait %s before trying again, as this is the expiration of the DNS record currently."
msgstr "Please wait %s before trying again, as this is the expiration of the DNS record currently."

#: lets-encrypt/config/fields.php:332
msgid "Plesk admin URL"
msgstr "Plesk admin URL"

#: lets-encrypt/config/fields.php:329
msgid "Plesk host"
msgstr "Plesk host"

#: lets-encrypt/config/fields.php:377
msgid "Plesk password"
msgstr "Plesk password"

#: lets-encrypt/class-letsencrypt-handler.php:344
msgid "Plesk recognized. Possibly the certificate can be installed automatically."
msgstr "Plesk recognised. Possibly the certificate can be installed automatically."

#: lets-encrypt/config/fields.php:353
msgid "Plesk username"
msgstr "Plesk username"

#: lets-encrypt/config/fields.php:356
msgid "Plesk username and password"
msgstr "Plesk username and password"

#: upgrade/upgrade-to-pro.php:106
msgid "Plugin activated"
msgstr "Plugin activated"

#: pro/class-scan.php:1611
msgid "plugin file"
msgstr "plugin file"

#: upgrade/upgrade-to-pro.php:99
msgid "Plugin installed"
msgstr "Plugin installed"

#: pro/security/wordpress/traits/trait-rsssl-country.php:208
msgid "Poland"
msgstr "Poland"

#: pro/security/wordpress/traits/trait-rsssl-country.php:209
msgid "Portugal"
msgstr "Portugal"

#: pro/class-admin.php:615
#: pro/class-admin.php:620
msgid "Potential redirect loop."
msgstr "Potential redirect loop."

#: class-wp-cli.php:1230
msgid "Pre-flight checks passed."
msgstr "Pre-flight checks passed."

#: assets/templates/two_fa/profile-settings.php:66
msgid "Preferred Method"
msgstr "Preferred Method"

#: pro/class-licensing.php:656
msgid "Premium"
msgstr "Premium"

#: pro/class-admin.php:151
#: settings/config/fields/general.php:210
#: settings/config/menu.php:51
msgid "Premium Support"
msgstr "Premium Support"

#: settings/config/menu.php:292
msgid "Prevent account theft by offering more secure authentication methods. You can configure which methods are available per user role, or even enforce usage of secure authentication. Secure authentication can be either Two-Factor Authentication or Passkey login."
msgstr "Prevent account theft by offering more secure authentication methods. You can configure which methods are available per user role, or even enforce usage of secure authentication. Secure authentication can be either Two-Factor Authentication or Passkey login."

#: settings/config/menu.php:152
msgid "Prevent clickjacking and other malicious attacks by restricting sources that are permitted to embed content from your website."
msgstr "Prevent clickjacking and other malicious attacks by restricting sources that are permitted to embed content from your website."

#: settings/config/fields/hardening-basic.php:39
msgid "Prevent code execution in the public 'Uploads' folder"
msgstr "Prevent code execution in the public 'Uploads' folder"

#: settings/config/fields/hardening-basic.php:60
msgid "Prevent login feedback"
msgstr "Prevent login feedback"

#: settings/config/fields/access-control.php:84
msgid "Prevent session hijacking"
msgstr "Prevent session hijacking"

#: settings/config/fields/hibp-integration.php:13
msgid "Prevent usage of passwords that have been included in a databreach. This securely verifies part of the hashed password via the Have I Been Pwned API."
msgstr "Prevent usage of passwords that have been included in a databreach. This securely verifies part of the hashed password via the Have I Been Pwned API."

#: class-site-health.php:128
msgid "Protect your login form with Limit Login Attempts"
msgstr "Protect your login form with Limit Login Attempts"

#: class-admin.php:2296
msgid "Protect your login form with Limit Login Attempts."
msgstr "Protect your login form with Limit Login Attempts."

#: settings/config/menu.php:387
msgid "Protect your site against brute force login attacks by limiting the number of login attempts. Enabling this feature will temporary lock-out a username and the IP address that tries to login, after the set number of false logins."
msgstr "Protect your site against brute force login attacks by limiting the number of login attempts. Enabling this feature will temporary lock-out a username and the IP address that tries to login, after the set number of false logins."

#: class-admin.php:2308
msgid "Protect your site with a performant Firewall."
msgstr "Protect your site with a performant Firewall."

#: class-site-health.php:97
msgid "Protect your user logins with Two-Factor Authentication (at least for Administrator accounts)"
msgstr "Protect your user logins with Two-Factor Authentication (at least for Administrator accounts)"

#: settings/config/menu.php:46
msgid "Protect your website against brute-force attacks with a captcha. Choose between Google reCAPTCHA or hCaptcha."
msgstr "Protect your website against brute-force attacks with a captcha. Choose between Google reCAPTCHA or hCaptcha."

#: settings/config/menu.php:108
msgid "Protecting your website visitors from malicious attacks and data breaches should be your #1 priority, start with the essentials with Really Simple Security"
msgstr "Protecting your website visitors from malicious attacks and data breaches should be your #1 priority, start with the essentials with Really Simple Security"

#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:164
msgctxt "Provider Label"
msgid "Backup Verification Codes (Single Use)"
msgstr "Backup Verification Codes (Single Use)"

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:99
msgctxt "Provider Label"
msgid "Email"
msgstr "Email"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:110
msgctxt "Provider label"
msgid "Passkey"
msgstr "Passkey"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:100
msgctxt "Provider Label"
msgid "Time Based One-Time Password (TOTP)"
msgstr "Time Based One-Time Password (TOTP)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:210
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: pro/security/wordpress/traits/trait-rsssl-country.php:211
msgid "Qatar"
msgstr "Qatar"

#: security/wordpress/vulnerabilities.php:646
msgid "Quarantine"
msgstr "Quarantine"

#: pro/security/wordpress/vulnerabilities-pro.php:715
#: pro/security/wordpress/vulnerabilities-pro.php:748
msgid "Quarantine Alert: %s"
msgstr "Quarantine Alert: %s"

#: pro/security/wordpress/vulnerabilities-pro.php:726
msgid "Quarantine in %s hours"
msgstr "Quarantine in %s hours"

#: security/wordpress/vulnerabilities.php:732
#: security/wordpress/vulnerabilities.php:1204
msgid "Quarantined"
msgstr "Quarantined"

#: class-admin.php:2133
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Dashboard/TaskElement.js:66
msgid "Re-check"
msgstr "Re-check"

#: class-wp-cli.php:1208
msgid "Reached %s, but received an error response code: %d. HTTPS is not properly configured."
msgstr "Reached %s, but received an error response code: %d. HTTPS is not properly configured."

#: class-admin.php:3292
msgid "Read about our journey towards Really Simple Security"
msgstr "Read about our journey towards Really Simple Security"

#: class-site-health.php:107
#: class-site-health.php:138
#: class-site-health.php:169
#: class-site-health.php:255
#: class-site-health.php:297
#: settings/config/fields/vulnerability-detection.php:234
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Dashboard/SslLabs/SslLabs.js:320
#: settings/src/LetsEncrypt/DnsVerification.js:57
msgid "Read more"
msgstr "Read more"

#: class-site-health.php:225
msgid "Read more about security concerns with debug display enabled"
msgstr "Read more about security concerns with debug display enabled"

#. Author of the plugin
#: really-simple-ssl-pro.php
msgid "Really Simple Plugins"
msgstr "Really Simple Plugins"

#: onboarding/class-onboarding.php:210
msgid "Really Simple Plugins is also the author of the below privacy-focused plugins including consent management and legal documents!"
msgstr "Really Simple Plugins is also the author of the below privacy-focused plugins including consent management and legal documents!"

#: mailer/class-mail.php:63
msgid "Really Simple Security - Notification Test"
msgstr "Really Simple Security - Notification Test"

#: mailer/class-mail.php:115
msgid "Really Simple Security - Verify your email address"
msgstr "Really Simple Security - Verify your email address"

#: settings/config/fields/access-control.php:85
msgid "Really Simple Security allows you to limit the default logged in session duration. By default, WordPress will keep users logged in for 48 hours, or even 14 days when clicking the ‘remember me’ checkbox. An attacker could possibly steal the logged in cookie and gain access to a user’s account. Limiting the logged in duration to 8 hours will greatly reduce the risk of session hijacking."
msgstr "Really Simple Security allows you to limit the default logged in session duration. By default, WordPress will keep users logged in for 48 hours, or even 14 days when clicking the ‘remember me’ checkbox. An attacker could possibly steal the logged in cookie and gain access to a user’s account. Limiting the logged in duration to 8 hours will greatly reduce the risk of session hijacking."

#. translators: %s is replaced with the hyperlink
#: class-admin.php:202
msgid "Really Simple Security and Really Simple Security add-ons do not process any personal identifiable information, so the GDPR does not apply to these plugins or usage of these plugins on your website. You can find our privacy policy <a href=\"%s\" rel=\"noopener noreferrer\" target=\"_blank\">here</a>."
msgstr "Really Simple Security and Really Simple Security add-ons do not process any personal identifiable information, so the GDPR does not apply to these plugins or usage of these plugins on your website. You can find our privacy policy <a href=\"%s\" rel=\"noopener noreferrer\" target=\"_blank\">here</a>."

#: settings/config/menu.php:45
msgid "Really Simple Security can trigger a Captcha to limit access to your site or the log in form."
msgstr "Really Simple Security can trigger a Captcha to limit access to your site or the login form."

#: settings/config/fields/vulnerability-detection.php:22
msgid "Really Simple Security collects information about plugins, themes, and core vulnerabilities from our database powered by WPVulnerability. Anonymized data about these vulnerable components will be sent to Really Simple Security for statistical analysis to improve open-source contributions. For more information, please read our privacy statement."
msgstr "Really Simple Security collects information about plugins, themes, and core vulnerabilities from our database powered by WPVulnerability. Anonymised data about these vulnerable components will be sent to Really Simple Security for statistical analysis to improve open-source contributions. For more information, please read our privacy statement."

#: settings/config/fields/vulnerability-detection.php:64
msgid "Really Simple Security dashboard"
msgstr "Really Simple Security dashboard"

#: class-site-health.php:332
msgid "Really Simple Security detected an SSL certificate, but has not been configured to enforce SSL."
msgstr "Really Simple Security detected an SSL certificate, but has not been configured to enforce SSL."

#: class-admin.php:2146
msgid "Really Simple Security has received no response from the webpage."
msgstr "Really Simple Security has received no response from the webpage."

#: class-site-health.php:347
msgid "Really Simple Security is installed, but no valid SSL certificate is detected."
msgstr "Really Simple Security is installed, but no valid SSL certificate is detected."

#. Plugin Name of the plugin
#: really-simple-ssl-pro.php
msgid "Really Simple Security Pro"
msgstr "Really Simple Security Pro"

#: settings/config/fields/hardening-extended.php:81
msgid "Really Simple Security will scan for insecure file and folder permissions on a weekly basis. You will receive an email report and Dashboard notice if insecure permissions are found."
msgstr "Really Simple Security will scan for insecure file and folder permissions on a weekly basis. You will receive an email report and Dashboard notice if insecure permissions are found."

#: onboarding/class-onboarding.php:187
msgid "Really Simple Security will send email notifications and security warnings from your server. We will send a test email to confirm that email is correctly configured on your site. Look for the confirmation button in the email."
msgstr "Really Simple Security will send email notifications and security warnings from your server. We will send a test email to confirm that email is correctly configured on your site. Look for the confirmation button in the email."

#. translators: %1$s: opening bold tag, %2$s: closing bold tag
#: class-admin.php:3285
msgid "Really Simple SSL is now %1$sReally Simple Security!%2$s"
msgstr "Really Simple SSL is now %1$sReally Simple Security!%2$s"

#: settings/config/fields/general.php:149
msgid "reCaptcha secret key"
msgstr "reCaptcha secret key"

#: settings/config/fields/general.php:132
msgid "reCaptcha site key"
msgstr "reCaptcha site key"

#: settings/config/fields/general.php:111
msgid "reCaptcha v2"
msgstr "reCaptcha v2"

#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:542
msgid "Receive a code by email"
msgstr "Receive a code by email"

#: settings/config/fields/security-headers.php:60
msgid "recommended"
msgstr "recommended"

#: upgrade/upgrade-to-pro.php:297
msgid "Recommended by Really Simple Plugins"
msgstr "Recommended by Really Simple Plugins"

#: class-admin.php:2273
msgid "Recommended security headers enabled."
msgstr "Recommended security headers enabled."

#: pro/security/wordpress/class-rsssl-limit-login-admin.php:497
#: pro/security/wordpress/class-rsssl-limit-login-admin.php:581
msgid "Record not found."
msgstr "Record not found."

#: pro/security/wordpress/class-rsssl-limit-login-admin.php:565
#: pro/security/wordpress/class-rsssl-limit-login-admin.php:599
msgid "Records deleted successfully."
msgstr "Records deleted successfully."

#: settings/config/disable-fields-filter.php:19
#: settings/config/fields/encryption.php:14
#: settings/config/fields/encryption.php:23
msgid "Redirect method"
msgstr "Redirect method"

#: settings/config/menu.php:76
msgid "Redirection"
msgstr "Redirection"

#: settings/config/disable-fields-filter.php:20
#: settings/config/fields/encryption.php:24
msgid "Redirects your site to https with a SEO friendly 301 redirect if it is requested over http."
msgstr "Redirects your site to https with a SEO friendly 301 redirect if it is requested over http."

#: settings/config/menu.php:498
#: settings/config/menu.php:518
#: settings/config/menu.php:590
#: settings/config/menu.php:591
msgid "Regions"
msgstr "Regions"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:78
msgid "Register passkey"
msgstr "Register passkey"

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:29
msgid "Registered"
msgstr "Registered"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:90
msgid "Registration failed retry?"
msgstr "Registration failed retry?"

#: pro/security/wordpress/two-fa/controllers/class-rsssl-webauthn-controller.php:335
#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:78
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/LearningMode/LearningMode.js:297
msgid "Remove"
msgstr "Remove"

#: class-wp-cli.php:1456
msgid "Remove a blocked IP from the limit login attempts table."
msgstr "Remove a blocked IP from the limit login attempts table."

#: class-wp-cli.php:1502
msgid "Remove a blocked username from the limit login attempts table."
msgstr "Remove a blocked username from the limit login attempts table."

#: class-wp-cli.php:1423
msgid "Remove a trusted IP from the firewall."
msgstr "Remove a trusted IP from the firewall."

#: class-wp-cli.php:1433
msgid "Remove a trusted IP from the limit login attempts table."
msgstr "Remove a trusted IP from the limit login attempts table."

#: class-wp-cli.php:1479
msgid "Remove a trusted username from the limit login attempts table."
msgstr "Remove a trusted username from the limit login attempts table."

#: class-site-health.php:195
msgid "Remove from public location with Really Simple Security"
msgstr "Remove from public location with Really Simple Security"

#: class-wp-cli.php:1401
msgid "Remove IP block."
msgstr "Remove IP block."

#: class-wp-cli.php:1362
msgid "Remove the lock file for safe mode."
msgstr "Remove the lock file for safe mode."

#: security/notices.php:94
msgid "Rename admin user enabled: Please choose a new username of at least 3 characters, which is not in use yet."
msgstr "Rename admin user enabled: Please choose a new username of at least 3 characters, which is not in use yet."

#: settings/config/fields/hardening-extended.php:72
msgid "Rename and randomize your database prefix"
msgstr "Rename and randomise your database prefix"

#: lets-encrypt/config/notices.php:169
#: lets-encrypt/config/notices.php:172
msgid "Renew certificate"
msgstr "Renew certificate"

#: lets-encrypt/config/notices.php:166
msgid "Renew installation"
msgstr "Renew installation"

#: assets/templates/two_fa/onboarding.php:126
#: assets/templates/two_fa/profile-settings.php:119
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:361
msgid "Resend Code"
msgstr "Resend Code"

#: class-wp-cli.php:1367
msgid "Reset the 2FA status of a user to disabled."
msgstr "Reset the 2FA status of a user to disabled."

#: security/wordpress/two-fa/class-rsssl-passkey-list-table.php:177
msgid "Resource not found"
msgstr "Resource not found"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:265
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:529
msgid "REST API authentication failed"
msgstr "REST API authentication failed"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:260
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:524
msgid "REST API authentication successful"
msgstr "REST API authentication successful"

#: pro/class-scan.php:2202
msgid "Restore files"
msgstr "Restore files"

#: settings/config/menu.php:545
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/firewall/UserAgentTable.js:275
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:520
msgid "Restrict access from specific countries or continents. You can also allow only specific countries."
msgstr "Restrict access from specific countries or continents. You can also allow only specific countries."

#: settings/config/menu.php:592
msgid "Restrict access to your site based on user location. By default, all regions are allowed. You can also block entire continents."
msgstr "Restrict access to your site based on user location. By default, regions are allowed. You can also block all entire continents."

#: settings/config/fields/hardening-extended.php:46
msgid "Restrict creation of administrator roles"
msgstr "Restrict creation of administrator roles"

#: lets-encrypt/integrations/cloudways/functions.php:37
msgid "Retrieving Cloudways server data..."
msgstr "Retrieving Cloudways server data..."

#: lets-encrypt/config/fields.php:480
msgid "Retrieving DNS verification token..."
msgstr "Retrieving DNS verification token..."

#: upgrade/upgrade-to-pro.php:91
msgid "Retrieving package information..."
msgstr "Retrieving package information..."

#: class-admin.php:2108
msgid "Retry"
msgstr "Retry"

#: pro/security/wordpress/traits/trait-rsssl-country.php:212
msgid "Reunion"
msgstr "Reunion"

#: pro/csp-violation-endpoint.php:231
msgid "Review the detected configuration in 'Content Security Policy'."
msgstr "Review the detected configuration in 'Content Security Policy'."

#: settings/config/fields/vulnerability-detection.php:172
#: settings/config/fields/vulnerability-detection.php:215
msgid "Risk"
msgstr "Risk"

#: pro/class-scan.php:2191
msgid "Roll back changes made to your files"
msgstr "Roll back changes made to your files"

#: pro/class-importer.php:61
msgid "Roll back file changes"
msgstr "Roll back file changes"

#: pro/security/wordpress/traits/trait-rsssl-country.php:213
msgid "Romania"
msgstr "Romania"

#: settings/config/menu.php:536
msgid "Rules"
msgstr "Rules"

#: pro/security/wordpress/traits/trait-rsssl-country.php:214
msgid "Russian Federation"
msgstr "Russian Federation"

#: pro/security/wordpress/traits/trait-rsssl-country.php:215
msgid "Rwanda"
msgstr "Rwanda"

#: pro/security/wordpress/traits/trait-rsssl-country.php:216
msgid "Saint Barthelemy"
msgstr "Saint Barthelemy"

#: pro/security/wordpress/traits/trait-rsssl-country.php:217
msgid "Saint Helena"
msgstr "Saint Helena"

#: pro/security/wordpress/traits/trait-rsssl-country.php:218
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts and Nevis"

#: pro/security/wordpress/traits/trait-rsssl-country.php:219
msgid "Saint Lucia"
msgstr "Saint Lucia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:220
msgid "Saint Martin"
msgstr "Saint Martin"

#: pro/security/wordpress/traits/trait-rsssl-country.php:221
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre and Miquelon"

#: pro/security/wordpress/traits/trait-rsssl-country.php:222
msgid "Saint Vincent and the Grenadines"
msgstr "Saint Vincent and the Grenadines"

#: pro/security/wordpress/traits/trait-rsssl-country.php:223
msgid "Samoa"
msgstr "Samoa"

#: pro/security/wordpress/traits/trait-rsssl-country.php:224
msgid "San Marino"
msgstr "San Marino"

#: pro/security/wordpress/traits/trait-rsssl-country.php:225
msgid "Sao Tome and Principe"
msgstr "Sao Tome and Principe"

#: pro/security/wordpress/traits/trait-rsssl-country.php:226
msgid "Saudi Arabia"
msgstr "Saudi Arabia"

#: onboarding/class-onboarding.php:188
#: settings/build/995.7a0675fe0519b06656b3.js:1
#: settings/src/Settings/Settings.js:153
msgid "Save and continue"
msgstr "Save and continue"

#: settings/config/menu.php:199
msgid "Scan results"
msgstr "Scan results"

#: pro/class-scan.php:260
msgid "Searching for js and css files and links to external resources in website, %d of %d"
msgstr "Searching for js and css files and links to external resources in website, %d of %d"

#: lets-encrypt/config/fields.php:532
msgid "Searching for link to SSL installation page on your server..."
msgstr "Searching for link to SSL installation page on your server..."

#: pro/class-scan.php:275
msgid "Searching for mixed content in css and js files, %s of %s"
msgstr "Searching for mixed content in css and js files, %s of %s"

#: class-site-health.php:159
msgid "Secure your site with a Firewall"
msgstr "Secure your site with a Firewall"

#: class-site-health.php:150
#: pro/security/notices.php:86
msgid "Secure your site with the performant Firewall."
msgstr "Secure your site with the performant Firewall."

#: class-multisite.php:239
#: class-multisite.php:240
#: class-site-health.php:100
#: class-site-health.php:131
#: class-site-health.php:162
#: settings/settings.php:192
#: settings/settings.php:193
#: settings/settings.php:225
msgid "Security"
msgstr "Security"

#: progress/class-progress.php:86
msgid "Security configuration completed!"
msgstr "Security configuration completed!"

#: progress/class-progress.php:83
msgid "Security configuration not completed yet. You still have %s task open."
msgid_plural "You still have %s tasks open."
msgstr[0] "Security configuration not completed yet. You still have %s task open."
msgstr[1] "You still have %s tasks open."

#: class-admin.php:3328
#: onboarding/class-onboarding.php:442
#: onboarding/class-onboarding.php:484
#: settings/config/menu.php:98
msgid "Security Headers"
msgstr "Security Headers"

#: class-site-health.php:36
msgid "Security Headers Test"
msgstr "Security Headers Test"

#: settings/config/fields/hardening-extended.php:66
msgid "Security through obscurity. Your site is no longer using the default wp_ prefix for database tables. The process has been designed to only complete and replace the tables after all wp_ tables are successfully renamed. In the unlikely event that this does lead to database issues on your site, please navigate to our troubleshooting article."
msgstr "Security through obscurity. Your site is no longer using the default wp_ prefix for database tables. The process has been designed to only complete and replace the tables after all wp_ tables are successfully renamed. In the unlikely event that this does lead to database issues on your site, please navigate to our troubleshooting article."

#: pro/security/wordpress/permission-detection/permission-detection.php:227
msgid "Security warning"
msgstr "Security warning"

#: pro/security/wordpress/permission-detection/permission-detection.php:226
msgid "Security warning: insecure file permissions"
msgstr "Security warning: insecure file permissions"

#: class-admin.php:2264
msgid "See which recommended security headers are not present on your website."
msgstr "See which recommended security headers are not present on your website."

#: assets/templates/two_fa/profile-settings.php:61
msgid "Selected provider"
msgstr "Selected provider"

#: settings/config/fields/security-headers.php:214
#: settings/config/fields/security-headers.php:336
msgid "Self (Default)"
msgstr "Self (Default)"

#: settings/config/fields/general.php:88
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/Support.js:45
msgid "Send"
msgstr "Send"

#: settings/config/menu.php:299
msgid "Send an email code during login. You can force user roles to use two-factor authentication, or leave the choose with your users, if so desired."
msgstr "Send an email code during login. You can force user roles to use two-factor authentication, or leave the choose with your users, if so desired."

#: pro/security/wordpress/traits/trait-rsssl-country.php:227
msgid "Senegal"
msgstr "Senegal"

#: pro/security/wordpress/traits/trait-rsssl-country.php:228
msgid "Serbia"
msgstr "Serbia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:229
msgid "Serbia and Montenegro"
msgstr "Serbia and Montenegro"

#: settings/config/fields/security-headers.php:312
msgid "Serve encrypted and authenticated responses"
msgstr "Serve encrypted and authenticated responses"

#: lets-encrypt/class-letsencrypt-handler.php:1170
msgid "Set permissions to 644 to enable SSL generation."
msgstr "Set permissions to 644 to enable SSL generation."

#: class-admin.php:2012
#: class-admin.php:2033
msgid "Set your wp-config.php to writable and reload this page."
msgstr "Set your wp-config.php to writable and reload this page."

#: class-admin.php:156
#: class-admin.php:158
#: settings/config/menu.php:17
#: settings/config/menu.php:394
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js:237
#: settings/src/Dashboard/Vulnerabilities/VulnerabilitiesFooter.js:24
msgid "Settings"
msgstr "Settings"

#: settings/config/fields/encryption.php:27
msgid "Settings update: .htaccess redirect"
msgstr "Settings update: .htaccess redirect"

#: settings/config/fields/hardening-extended.php:65
msgid "Settings update: Database prefix changed"
msgstr "Settings update: Database prefix changed"

#: settings/config/fields/hardening-extended.php:16
msgid "Settings update: Debug.log file relocated"
msgstr "Settings update: Debug.log file relocated"

#: settings/config/fields/hardening-basic.php:103
msgid "Settings update: Username 'admin' renamed"
msgstr "Settings update: Username 'admin' renamed"

#: pro/security/wordpress/vulnerabilities-pro.php:601
msgid "Several automatic updates for components have been scheduled due to the discovery of vulnerabilities on %s."
msgstr "Several automatic updates for components have been scheduled due to the discovery of vulnerabilities on %s."

#: pro/security/wordpress/vulnerabilities-pro.php:660
msgid "Several automatic updates for vulnerable components, scheduled on %s, have been successful."
msgstr "Several automatic updates for vulnerable components, scheduled on %s, have been successful."

#: pro/security/wordpress/vulnerabilities-pro.php:630
#: pro/security/wordpress/vulnerabilities-pro.php:690
msgid "Several automatic updates for vulnerable components, scheduled on %s, have failed."
msgstr "Several automatic updates for vulnerable components, scheduled on %s, have failed."

#: pro/security/wordpress/vulnerabilities-pro.php:753
msgid "Several vulnerable components quarantined on %s."
msgstr "Several vulnerable components quarantined on %s."

#: pro/security/wordpress/vulnerabilities-pro.php:720
msgid "Several vulnerable components scheduled for update on %s, have failed."
msgstr "Several vulnerable components scheduled for update on %s, have failed."

#: pro/security/wordpress/traits/trait-rsssl-country.php:230
msgid "Seychelles"
msgstr "Seychelles"

#: class-wp-cli.php:1413
msgid "Show blocked IP's."
msgstr "Show blocked IP's."

#: pro/security/wordpress/traits/trait-rsssl-country.php:231
msgid "Sierra Leone"
msgstr "Sierra Leone"

#. Description of the plugin
#: really-simple-ssl-pro.php
msgid "Simple and performant security"
msgstr "Simple and performant security"

#: pro/security/wordpress/traits/trait-rsssl-country.php:232
msgid "Singapore"
msgstr "Singapore"

#: security/wordpress/vulnerabilities.php:401
msgid "Site wide - Test Notification"
msgstr "Site wide - Test Notification"

#: settings/config/fields/vulnerability-detection.php:86
msgid "Site-wide, admin notification"
msgstr "Site-wide, admin notification"

#: assets/templates/two_fa/onboarding.php:140
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Onboarding/OnboardingControls.js:201
msgid "Skip"
msgstr "Skip"

#: assets/templates/two_fa/onboarding.php:154
msgid "Skip (%1$d %2$s remaining)"
msgstr "Skip (%1$d %2$s remaining)"

#: assets/templates/two_fa/onboarding.php:150
msgid "Skip (Only today remaining)"
msgstr "Skip (Only today remaining)"

#: pro/security/wordpress/traits/trait-rsssl-country.php:234
msgid "Slovakia"
msgstr "Slovakia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:235
msgid "Slovenia"
msgstr "Slovenia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:236
msgid "Solomon Islands"
msgstr "Solomon Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:237
msgid "Somalia"
msgstr "Somalia"

#: pro/security/wordpress/class-rsssl-limit-login-admin.php:346
msgid "Some %1s were added to %2s, missing %1s are %3s"
msgstr "Some %1s were added to %2s, missing %1s are %3s"

#: pro/class-importer.php:537
#: settings/build/index.a4cc556db77e3384994b.js:1
#: settings/src/Settings/FieldsData.js:184
msgid "Something went wrong"
msgstr "Something went wrong"

#: pro/class-importer.php:93
msgid "Something went wrong. If this doesn't work, you can put the original files back by changing files named 'rsssl-bkp-filename' to filename."
msgstr "Something went wrong. If this doesn't work, you can put the original files back by changing files named 'rsssl-bkp-filename' to filename."

#: pro/class-importer.php:121
#: pro/class-importer.php:162
#: pro/class-importer.php:200
#: pro/class-importer.php:242
#: pro/class-importer.php:278
msgid "Something went wrong. Please refresh the page and try again, or fix manually."
msgstr "Something went wrong. Please refresh the page and try again, or fix manually."

#: security/wordpress/user-enumeration.php:57
msgid "Sorry, you are not allowed to access user data without authentication."
msgstr "Sorry, you are not allowed to access user data without authentication."

#: security/wordpress/user-enumeration.php:39
msgid "Sorry, you are not allowed to access users without authentication."
msgstr "Sorry, you are not allowed to access users without authentication."

#: settings/config/fields/security-headers.php:400
msgid "Source"
msgstr "Source"

#: pro/security/wordpress/traits/trait-rsssl-country.php:238
msgid "South Africa"
msgstr "South Africa"

#: pro/security/wordpress/traits/trait-rsssl-country.php:298
msgid "South America"
msgstr "South America"

#: pro/security/wordpress/traits/trait-rsssl-country.php:239
msgid "South Georgia and the South Sandwich Islands"
msgstr "South Georgia and the South Sandwich Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:240
msgid "South Sudan"
msgstr "South Sudan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:241
msgid "Spain"
msgstr "Spain"

#: pro/security/wordpress/traits/trait-rsssl-country.php:242
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: class-multisite.php:110
msgid "SSL activation in progress"
msgstr "SSL activation in progress"

#: lets-encrypt/class-letsencrypt-handler.php:306
msgid "SSL certificate should be generated and installed."
msgstr "SSL certificate should be generated and installed."

#. translators: %s is replaced with date.
#: class-admin.php:2126
msgid "SSL certificate will expire on %s."
msgstr "SSL certificate will expire on %s."

#: class-multisite.php:79
msgid "SSL is enabled networkwide."
msgstr "SSL is enabled networkwide."

#: class-admin.php:2077
msgid "SSL is enabled on your site."
msgstr "SSL is enabled on your site."

#: class-multisite.php:83
msgid "SSL is not enabled on your network"
msgstr "SSL is not enabled on your network"

#: class-admin.php:2081
msgid "SSL is not enabled yet."
msgstr "SSL is not enabled yet."

#: class-site-health.php:329
msgid "SSL is not enabled."
msgstr "SSL is not enabled."

#: progress/class-progress.php:93
msgid "SSL is not yet enabled on this site."
msgstr "SSL is not yet enabled on this site."

#: class-admin.php:2065
msgid "SSL is now activated. Follow the three steps in this article to check if your website is secure."
msgstr "SSL is now activated. Follow the three steps in this article to check if your website is secure."

#: class-site-health.php:31
msgid "SSL Status Test"
msgstr "SSL Status Test"

#: lets-encrypt/integrations/cpanel/cpanel.php:123
#: lets-encrypt/integrations/directadmin/directadmin.php:124
msgid "SSL successfully installed on %s"
msgstr "SSL successfully installed on %s"

#: pro/security/wordpress/traits/trait-rsssl-country.php:233
msgid "St Martin"
msgstr "St Martin"

#: settings/config/menu.php:289
msgid "Start login protection by adding an additional layer during authentication. This will leave authentication less dependent on just a single password. Want to force strong passwords? Check out Password Security."
msgstr "Start login protection by adding an additional layer during authentication. This will leave authentication less dependent on just a single password. Want to force strong passwords? Check out Password Security."

#: settings/config/fields/firewall.php:248
#: settings/config/fields/limit-login-attempts.php:143
#: settings/config/fields/limit-login-attempts.php:189
#: settings/config/fields/limit-login-attempts.php:249
#: settings/config/fields/two-fa.php:179
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/SslLabs/SslLabsHeader.js:5
msgid "Status"
msgstr "Status"

#: settings/config/fields/general.php:220
msgid "Stop editing the .htaccess file"
msgstr "Stop editing the .htaccess file"

#: lets-encrypt/config/fields.php:400
msgid "Store for renewal purposes. If not stored, renewal may need to be done manually."
msgstr "Store for renewal purposes. If not stored, renewal may need to be done manually."

#: settings/config/fields/firewall.php:109
msgid "Strict - 10 errors in 10 seconds"
msgstr "Strict - 10 errors in 10 seconds"

#: onboarding/class-onboarding.php:512
msgid "Strong Password policy"
msgstr "Strong Password policy"

#: assets/templates/two_fa/totp-config.php:54
#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:374
msgid "Submit"
msgstr "Submit"

#: pro/class-licensing.php:655
#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:89
msgid "Success"
msgstr "Success"

#: lets-encrypt/integrations/cpanel/cpanel.php:306
#: lets-encrypt/integrations/cpanel/cpanel.php:325
msgid "Successfully added TXT record."
msgstr "Successfully added TXT record."

#: lets-encrypt/class-letsencrypt-handler.php:774
msgid "Successfully generated certificate."
msgstr "Successfully generated certificate."

#: lets-encrypt/integrations/cloudways/cloudways.php:181
#: lets-encrypt/integrations/cloudways/cloudways.php:187
msgid "Successfully installed Let's Encrypt"
msgstr "Successfully installed Let's Encrypt"

#: lets-encrypt/integrations/plesk/plesk.php:90
msgid "Successfully installed SSL"
msgstr "Successfully installed SSL"

#: lets-encrypt/class-letsencrypt-handler.php:387
msgid "Successfully retrieved account"
msgstr "Successfully retrieved account"

#: lets-encrypt/integrations/cloudways/cloudways.php:212
#: lets-encrypt/integrations/cloudways/cloudways.php:245
msgid "Successfully retrieved server id and app id"
msgstr "Successfully retrieved server ID and app ID"

#: lets-encrypt/class-letsencrypt-handler.php:1360
#: lets-encrypt/class-letsencrypt-handler.php:1692
#: lets-encrypt/class-letsencrypt-handler.php:1743
msgid "Successfully verified alias domain."
msgstr "Successfully verified alias domain."

#: lets-encrypt/class-letsencrypt-handler.php:562
msgid "Successfully verified DNS records"
msgstr "Successfully verified DNS records"

#: pro/security/wordpress/traits/trait-rsssl-country.php:243
msgid "Sudan"
msgstr "Sudan"

#: class-admin.php:163
#: settings/build/index.a4cc556db77e3384994b.js:1
#: settings/src/Header.js:45
msgid "Support"
msgstr "Support"

#: pro/security/wordpress/traits/trait-rsssl-country.php:244
msgid "Suriname"
msgstr "Suriname"

#: pro/security/wordpress/block-admin-creation.php:329
#: pro/security/wordpress/block-admin-creation.php:330
msgid "Suspicious admin account detected"
msgstr "Suspicious admin account detected"

#: pro/security/wordpress/traits/trait-rsssl-country.php:245
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard and Jan Mayen"

#: pro/security/wordpress/traits/trait-rsssl-country.php:246
msgid "Swaziland"
msgstr "Swaziland"

#: pro/security/wordpress/traits/trait-rsssl-country.php:247
msgid "Sweden"
msgstr "Sweden"

#: pro/security/wordpress/traits/trait-rsssl-country.php:248
msgid "Switzerland"
msgstr "Switzerland"

#: pro/security/wordpress/traits/trait-rsssl-country.php:249
msgid "Syrian Arab Republic"
msgstr "Syrian Arab Republic"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:119
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:174
msgid "System"
msgstr "System"

#: lets-encrypt/config/fields.php:8
#: settings/config/fields/general.php:61
#: settings/config/menu.php:717
msgid "System status"
msgstr "System status"

#: pro/security/wordpress/traits/trait-rsssl-country.php:250
msgid "Taiwan, Province of China"
msgstr "Taiwan, Province of China"

#: pro/security/wordpress/traits/trait-rsssl-country.php:251
msgid "Tajikistan"
msgstr "Tajikistan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:252
msgid "Tanzania, United Republic of"
msgstr "Tanzania, United Republic of"

#: settings/config/menu.php:655
msgid "Temporary"
msgstr "Temporary"

#: settings/config/menu.php:421
#: settings/config/menu.php:456
msgid "Temporary block"
msgstr "Temporary block"

#: lets-encrypt/config/fields.php:111
#: onboarding/class-onboarding.php:335
msgid "Terms & Conditions"
msgstr "Terms & Conditions"

#: lets-encrypt/class-letsencrypt-handler.php:1041
msgid "Terms & Conditions are accepted."
msgstr "Terms & Conditions are accepted."

#: settings/settings.php:589
msgid "Terms and Conditions"
msgstr "Terms and Conditions"

#: lets-encrypt/class-le-restapi.php:235
msgid "Test not found."
msgstr "Test not found."

#: mailer/class-mail.php:59
msgid "Test notification email error"
msgstr "Test notification email error"

#: settings/config/fields/vulnerability-detection.php:127
#: settings/config/fields/vulnerability-detection.php:131
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/RiskConfiguration/NotificationTester.js:63
msgid "Test notifications"
msgstr "Test notifications"

#: settings/config/fields/vulnerability-detection.php:128
msgid "Test notifications can be used to test email delivery and shows how vulnerabilities will be reported on your WordPress installation."
msgstr "Test notifications can be used to test email delivery and shows how vulnerabilities will be reported on your WordPress installation."

#: pro/security/wordpress/traits/trait-rsssl-country.php:253
msgid "Thailand"
msgstr "Thailand"

#: class-admin.php:3317
msgid "Thank you for being a long-time user! As a token of our gratitude, we want to offer you %s6 months Really Simple Security Pro, 100%% Free!%s"
msgstr "Thank you for being a long-time user! As a token of our gratitude, we want to offer you %s6 months Really Simple Security Pro, 100%% Free!%s"

#: pro/class-headers.php:248
#: pro/class-headers.php:457
msgid "The %s security header is not set by Really Simple Security, but has a non-recommended value: \"%s\"."
msgstr "The %s security header is not set by Really Simple Security, but has a non-recommended value: \"%s\"."

#: class-admin.php:2048
msgid "The 'force-deactivate.php' file has to be renamed to .txt. Otherwise your ssl can be deactivated by anyone on the internet."
msgstr "The 'force-deactivate.php' file has to be renamed to .txt. Otherwise your SSL can be deactivated by anyone on the internet."

#: settings/config/fields/encryption.php:28
msgid "The .htaccess redirect has been enabled on your site. If the server configuration is non-standard, this might cause issues. Please check if all pages on your site are functioning properly."
msgstr "The .htaccess redirect has been enabled on your site. If the server configuration is non-standard, this might cause issues. Please check if all pages on your site are functioning properly."

#: class-admin.php:2224
msgid "The .htaccess redirect rules selected by this plugin failed in the test. Set manually or dismiss to leave on PHP redirect."
msgstr "The .htaccess redirect rules selected by this plugin failed in the test. Set manually or dismiss to leave on PHP redirect."

#: class-site-health.php:368
msgid "The 301 .htaccess redirect is the fastest and most reliable redirect option."
msgstr "The 301 .htaccess redirect is the fastest and most reliable redirect option."

#: class-admin.php:2204
msgid "The 301 redirect with .htaccess to HTTPS is now enabled."
msgstr "The 301 redirect with .htaccess to HTTPS is now enabled."

#: security/wordpress/two-fa/class-rsssl-parameter-validation.php:114
msgid "The authentication code is not valid."
msgstr "The authentication code is not valid."

#: lets-encrypt/config/notices.php:90
msgid "The automatic installation of your certificate has failed. Please check your credentials, and retry the %sinstallation%s."
msgstr "The automatic installation of your certificate has failed. Please check your credentials, and retry the %sinstallation%s."

#: lets-encrypt/class-letsencrypt-handler.php:643
msgid "The certificate generation was rate limited for 5 minutes because the authorization failed."
msgstr "The certificate generation was rate limited for 5 minutes because the authorisation failed."

#: lets-encrypt/class-letsencrypt-handler.php:1811
msgid "The certificate installation was rate limited. Please try again later."
msgstr "The certificate installation was rate limited. Please try again later."

#: lets-encrypt/class-letsencrypt-handler.php:1263
msgid "The certs directory is not created yet."
msgstr "The certs directory is not created yet."

#: lets-encrypt/class-letsencrypt-handler.php:1267
msgid "The certs directory was successfully created."
msgstr "The certs directory was successfully created."

#: lets-encrypt/class-letsencrypt-handler.php:1220
msgid "The challenge directory is not created yet."
msgstr "The challenge directory is not created yet."

#: lets-encrypt/class-letsencrypt-handler.php:1224
msgid "The challenge directory was successfully created."
msgstr "The challenge directory was successfully created."

#: security/wordpress/block-code-execution-uploads.php:37
msgid "The code to block code execution in the uploads folder cannot be added automatically on nginx. Add the following code to your nginx.conf file:"
msgstr "The code to block code execution in the uploads folder cannot be added automatically on NGINX. Add the following code to your nginx.conf file:"

#: settings/config/fields/security-headers.php:319
msgid "The content security policy has many options, so we always recommend starting in ‘learning mode’ to see what files and scripts are loaded."
msgstr "The content security policy has many options, so we always recommend starting in ‘learning mode’ to see what files and scripts are loaded."

#: security/wordpress/file-editing.php:28
msgid "The DISALLOW_FILE_EDIT constant is defined and set to false. You can remove it from your wp-config.php."
msgstr "The DISALLOW_FILE_EDIT constant is defined and set to false. You can remove it from your wp-config.php."

#: lets-encrypt/class-letsencrypt-handler.php:572
msgid "The DNS response for %s was %s, while it should be %s."
msgstr "The DNS response for %s was %s, while it should be %s."

#: lets-encrypt/class-letsencrypt-handler.php:403
msgid "The email address was not set. Please set the email address"
msgstr "The email address was not set. Please set the email address"

#: class-site-health.php:280
msgid "The essential security headers are detected on your site."
msgstr "The essential security headers are detected on your site."

#: settings/config/menu.php:106
msgid "The Essentials"
msgstr "The Essentials"

#: settings/config/menu.php:475
#: settings/config/menu.php:491
msgid "The Event Log shows all relevant events related to limit login attempts. You can filter the log using the dropdown on the top-right to only show warnings."
msgstr "The Event Log shows all relevant events related to limit login attempts. You can filter the log using the dropdown on the top-right to only show warnings."

#: settings/config/menu.php:680
msgid "The Event Log shows all relevant events related to the Firewall and IP lockouts. You can filter the log using the dropdown on the top-right to only show warnings."
msgstr "The Event Log shows all relevant events related to the Firewall and IP lockouts. You can filter the log using the dropdown on the top-right to only show warnings."

#: settings/config/fields/encryption.php:90
#: settings/config/menu.php:91
msgid "The extensive mixed content scan will list all issues and provide a fix, or instructions to fix manually."
msgstr "The extensive mixed content scan will list all issues and provide a fix, or instructions to fix manually."

#: pro/class-importer.php:179
msgid "The file could not be downloaded. It might not exist, or downloading is blocked. Fix manually."
msgstr "The file could not be downloaded. It might not exist, or downloading is blocked. Fix manually."

#: pro/class-importer.php:221
#: pro/class-importer.php:258
#: pro/class-importer.php:297
msgid "The file could not be downloaded. The file might not exist, or downloading is be blocked by the server. Fix manually."
msgstr "The file could not be downloaded. The file might not exist, or downloading is be blocked by the server. Fix manually."

#: settings/config/menu.php:566
#: settings/config/menu.php:567
msgid "The Firewall can also block traffic from malicious or resource-consuming bots that might crawl your website. A list of well-known bad User-Agents is automatically included. You can manually add or delete user-agents if so desired."
msgstr "The Firewall can also block traffic from malicious or resource-consuming bots that might crawl your website. A list of well-known bad User-Agents is automatically included. You can manually add or delete user-agents if so desired."

#: security/notices.php:153
msgid "The Firewall, LLA and 2FA are currently inactive, as you have activated Safe Mode with the rsssl-safe-mode.lock file. Remove the file from your /wp-content folder after you have finished debugging."
msgstr "The Firewall, LLA and 2FA are currently inactive, as you have activated Safe Mode with the rsssl-safe-mode.lock file. Remove the file from your /wp-content folder after you have finished debugging."

#: lets-encrypt/class-letsencrypt-handler.php:1170
msgid "The following directories do not have the necessary writing permissions."
msgstr "The following directories do not have the necessary writing permissions."

#: pro/class-headers.php:488
msgid "The following essential security headers have not been set: %s"
msgstr "The following essential security headers have not been set: %s"

#: settings/config/menu.php:52
msgid "The following information is attached when you send this form: license key, scan results, your domain, .htaccess file, debug log and a list of active plugins."
msgstr "The following information is attached when you send this form: licence key, scan results, your domain, .htaccess file, debug log and a list of active plugins."

#: lets-encrypt/class-letsencrypt-handler.php:337
msgid "The Hosting Panel software was not recognized. Depending on your hosting provider, the generated certificate may need to be installed manually."
msgstr "The Hosting Panel software was not recognised. Depending on your hosting provider, the generated certificate may need to be installed manually."

#: settings/config/fields/firewall.php:124
msgid "The IP address will see a locked out screen for the selected duration."
msgstr "The IP address will see a locked out screen for the selected duration."

#: class-wp-cli.php:1383
#: class-wp-cli.php:1444
#: class-wp-cli.php:1462
msgid "The IP to block."
msgstr "The IP to block."

#: class-wp-cli.php:1407
msgid "The IP to remove the block for."
msgstr "The IP to unblock."

#: lets-encrypt/class-letsencrypt-handler.php:1237
msgid "The key directory is not created yet."
msgstr "The key directory is not created yet."

#: lets-encrypt/class-letsencrypt-handler.php:1249
msgid "The key directory was successfully created."
msgstr "The key directory was successfully created."

#: security/wordpress/two-fa/class-rsssl-parameter-validation.php:133
msgid "The key is not valid."
msgstr "The key is not valid."

#: pro/class-scan.php:147
msgid "The last scan was completed with errors. Are you sure these issues don't impact your site?"
msgstr "The last scan was completed with errors. Are you sure these issues don't impact your site?"

#: pro/class-scan.php:142
msgid "The last scan was completed with errors. Only migrate if you are sure the found errors are not a problem for your site."
msgstr "The last scan was completed with errors. Only migrate if you are sure the found errors are not a problem for your site."

#: pro/class-licensing.php:594
msgid "The license information could not be retrieved at this moment. Please try again at a later time."
msgstr "The licence information could not be retrieved at this moment. Please try again at a later time."

#: class-wp-cli.php:1346
msgid "The license key to activate."
msgstr "The license key to activate."

#: settings/config/menu.php:548
msgid "The lightweight Firewall can be used to lockout malicious traffic from your site. You can configure generic rules below, or block specific IP addresses by adding them to the Blocklist."
msgstr "The lightweight Firewall can be used to lockout malicious traffic from your site. You can configure generic rules below, or block specific IP addresses by adding them to the Blocklist."

#. translators: %s is replaced with the error description.
#: class-admin.php:2171
msgid "The mixed content fixer could not be detected due to a cURL error: %s. cURL errors are often caused by an outdated version of PHP or cURL and don't affect the front-end of your site. Contact your hosting provider for a fix."
msgstr "The mixed content fixer could not be detected due to a cURL error: %s. cURL errors are often caused by an outdated version of PHP or cURL and don't affect the front-end of your site. Contact your hosting provider for a fix."

#: class-admin.php:2153
msgid "The mixed content fixer is active, but was not detected on the frontpage."
msgstr "The mixed content fixer is active, but was not detected on the frontpage."

#: lets-encrypt/config/notices.php:41
msgid "The non-www version of your site does not point to this website. This is recommended, as it will allow you to add it to the certificate as well."
msgstr "The non-www version of your site does not point to this website. This is recommended, as it will allow you to add it to the certificate as well."

#: onboarding/class-onboarding.php:181
msgid "The onboarding wizard will help to configure essential security features in 1 minute! Select your hosting provider to start."
msgstr "The onboarding wizard will help to configure essential security features in 1 minute! Select your hosting provider to start."

#: lets-encrypt/class-letsencrypt-handler.php:471
#: lets-encrypt/class-letsencrypt-handler.php:749
msgid "The order is invalid, possibly due to too many failed authorization attempts. Please start at the previous step."
msgstr "The order is invalid, possibly due to too many failed authorisation attempts. Please start at the previous step."

#: lets-encrypt/class-letsencrypt-handler.php:367
msgid "The PHP function CURL has successfully been detected."
msgstr "The PHP function cURL has successfully been detected."

#: lets-encrypt/class-letsencrypt-handler.php:363
msgid "The PHP function CURL is not available on your server, which is required. Please contact your hosting provider."
msgstr "The PHP function cURL is not available on your server, which is required. Please contact your hosting provider."

#: security/wordpress/two-fa/class-rsssl-parameter-validation.php:57
msgid "The preferred method is not set."
msgstr "The preferred method is not set."

#: pro/security/wordpress/permission-detection/permission-detection.php:218
msgid "The recurring scan detected insecure file permissions being used for certain files or folders. Navigate to the Really Simple Security dashboard to resolve the issue."
msgstr "The recurring scan detected insecure file permissions being used for certain files or folders. Navigate to the Really Simple Security dashboard to resolve the issue."

#: lets-encrypt/class-letsencrypt-handler.php:1177
msgid "The required directories have the necessary writing permissions."
msgstr "The required directories have the necessary writing permissions."

#: security/wordpress/two-fa/class-rsssl-parameter-validation.php:95
msgid "The selected provider is not valid."
msgstr "The selected provider is not valid."

#: settings/config/menu.php:398
msgid "The settings below determine how strict your site will be protected. You can leave these settings on their default values, unless you experience issues."
msgstr "The settings below determine how strict your site will be protected. You can leave these settings on their default values, unless you experience issues."

#: security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php:106
msgid "The site's security policy requires you to configure Two-Factor Authentication to protect against account theft. %1$s and configure Two-Factor authentication %2$swithin three days%3$s. If you haven't performed the configuration by then, %4$syou will be unable to login%5$s."
msgstr "The site's security policy requires you to configure Two-Factor Authentication to protect against account theft. %1$s and configure Two-Factor authentication %2$swithin three days%3$s. If you haven't performed the configuration by then, %4$syou will be unable to login%5$s."

#: lets-encrypt/config/notices.php:97
msgid "The SSL certificate has been renewed, and requires manual %sinstallation%s in your hosting dashboard."
msgstr "The SSL certificate has been renewed, and requires manual %sinstallation%s in your hosting dashboard."

#: lets-encrypt/integrations/cpanel/functions.php:63
msgid "The system is not ready for the DNS verification yet. Please run the wizard again."
msgstr "The system is not ready for the DNS verification yet. Please run the wizard again."

#: lets-encrypt/class-letsencrypt-handler.php:1851
#: lets-encrypt/integrations/cloudways/functions.php:22
#: lets-encrypt/integrations/cpanel/functions.php:17
#: lets-encrypt/integrations/cpanel/functions.php:35
#: lets-encrypt/integrations/directadmin/functions.php:16
#: lets-encrypt/integrations/plesk/functions.php:16
msgid "The system is not ready for the installation yet. Please run the wizard again."
msgstr "The system is not ready for the installation yet. Please run the wizard again."

#: lets-encrypt/class-letsencrypt-handler.php:1045
msgid "The Terms & Conditions were not accepted. Please accept in the general settings."
msgstr "The Terms & Conditions were not accepted. Please accept in the general settings."

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:307
msgid "The Two-Factor Authentication setup for email failed. Please try again."
msgstr "The Two-Factor Authentication setup for email failed. Please try again."

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:284
msgid "The Two-Factor Authentication setup for TOTP failed. Please try again."
msgstr "The Two-Factor Authentication setup for TOTP failed. Please try again."

#: lets-encrypt/config/fields.php:170
msgid "The URL you use to access your cPanel dashboard. Ends on :2083."
msgstr "The URL you use to access your cPanel dashboard. Ends on :2083."

#: lets-encrypt/config/fields.php:232
msgid "The URL you use to access your DirectAdmin dashboard. Ends on :2222."
msgstr "The URL you use to access your DirectAdmin dashboard. Ends on :2222."

#: lets-encrypt/config/fields.php:333
msgid "The URL you use to access your Plesk dashboard. Ends on :8443."
msgstr "The URL you use to access your Plesk dashboard. Ends on :8443."

#: lets-encrypt/class-letsencrypt-handler.php:394
msgid "The used domain for your email address is not allowed."
msgstr "The used domain for your email address is not allowed."

#: settings/config/fields/limit-login-attempts.php:74
msgid "The user and IP address will be temporarily unable to login for the specified duration. You can block IP addresses indefinitely via the IP addresses block."
msgstr "The user and IP address will be temporarily unable to login for the specified duration. You can block IP addresses indefinitely via the IP addresses block."

#: security/wordpress/two-fa/class-rsssl-parameter-validation.php:38
msgid "The user ID is not valid."
msgstr "The user ID is not valid."

#: security/wordpress/two-fa/class-rsssl-parameter-validation.php:76
msgid "The user object is not valid."
msgstr "The user object is not valid."

#: class-wp-cli.php:1490
msgid "The username to block."
msgstr "The username to block."

#: class-wp-cli.php:1508
msgid "The username to remove the block for."
msgstr "The username to unblock."

#: class-site-health.php:219
msgid "The value, WP_DEBUG_DISPLAY, has either been enabled by WP_DEBUG or added to your configuration file. This will make errors display on the front end of your site."
msgstr "The value, WP_DEBUG_DISPLAY, has either been enabled by WP_DEBUG or added to your configuration file. This will display errors on the front end of your site."

#: class-site-health.php:189
msgid "The value, WP_DEBUG_LOG, has been added to this website’s configuration file. This means any errors on the site will be written to a file which is potentially available to all users."
msgstr "The value, WP_DEBUG_LOG, has been added to this website’s configuration file. This means any errors on the site will be written to a file which is potentially available to all users."

#: class-admin.php:2098
msgid "The wp-config.php file is not writable, and needs to be edited. Please set this file to writable."
msgstr "The wp-config.php file is not writable, and needs to be edited. Please set this file to writable."

#: lets-encrypt/config/notices.php:43
msgid "The www version of your site does not point to this website. This is recommended, as it will allow you to add it to the certificate as well."
msgstr "The www version of your site does not point to this website. This is recommended, as it will allow you to add it to the certificate as well."

#: pro/class-scan.php:1607
msgid "theme file"
msgstr "theme file"

#: lets-encrypt/class-letsencrypt-handler.php:468
#: lets-encrypt/class-letsencrypt-handler.php:489
msgid "There are existing keys, the order had to be cleared first."
msgstr "There are existing keys, the order had to be cleared first."

#: security/wordpress/two-fa/class-rsssl-two-factor.php:1236
msgid "There have been too many failed two-factor authentication attempts, which often indicates that the password has been compromised. The password has been reset in order to protect the account."
msgstr "There have been too many failed two-factor authentication attempts, which often indicates that the password has been compromised. The password has been reset in order to protect the account."

#: pro/class-importer.php:136
msgid "There was a problem editing the file. Please try manually."
msgstr "There was a problem editing the file. Please try manually."

#: settings/config/menu.php:214
msgid "These notifications are set to the minimum risk level that triggers a notification. For example, the default site-wide notification triggers on high-risk and critical vulnerabilities."
msgstr "These notifications are set to the minimum risk level that triggers a notification. For example, the default site-wide notification triggers on high-risk and critical vulnerabilities."

#: settings/config/fields/security-headers.php:25
msgid "These security headers are the fundamental security measures to protect your website visitors while visiting your website."
msgstr "These security headers are the fundamental security measures to protect your website visitors while visiting your website."

#: settings/config/fields/two-fa.php:28
msgid "These user roles will be enforced to either configure Two-factor Authentication or Passkey log in. We recommend to enforce at least administrators."
msgstr "These user roles will be enforced to either configure Two-factor Authentication or Passkey log in. We recommend to enforce at least administrators."

#: settings/config/fields/access-control.php:20
#: settings/config/fields/security-headers.php:221
msgid "They might be misused if you don’t actively tell the browser to disable these features."
msgstr "They might be misused if you don’t actively tell the browser to disable these features."

#: settings/config/fields/access-control.php:15
msgid "This adds extra requirements for strong passwords for new users and updated passwords."
msgstr "This adds extra requirements for strong passwords for new users and updated passwords."

#: upgrade/upgrade-to-pro.php:551
msgid "This appears to be an invalid license key for this plugin."
msgstr "This appears to be an invalid licence key for this plugin."

#: class-wp-cli.php:65
msgid "This command is related to functionality available in Really Simple Security Pro, please consider upgrading to unlock all powerful security features. Read more: https://really-simple-ssl.com/pro"
msgstr "This command is related to functionality available in Really Simple Security Pro, please consider upgrading to unlock all powerful security features. Read more: https://really-simple-ssl.com/pro"

#: pro/class-licensing.php:737
msgid "This domain is not activated for this Really Simple Security Pro license. Please activate the license for this domain."
msgstr "This domain is not activated for this Really Simple Security Pro license. Please activate the license for this domain."

#: pro/class-scan.php:1633
msgid "This downloads the file from the domain without SSL, inserts it into WP media, and changes the URL to the new URL."
msgstr "This downloads the file from the domain without SSL, inserts it into WP media, and changes the URL to the new URL."

#: lets-encrypt/config/fields.php:98
msgid "This email address is used to create a Let's Encrypt account. This is also where you will receive renewal notifications."
msgstr "This email address is used to create a Let's Encrypt account. This is also where you will receive renewal notifications."

#: mailer/class-mail.php:64
msgid "This email is confirmation that any security notices are likely to reach your inbox."
msgstr "This email is confirmation that any security notices are likely to reach your inbox."

#: mailer/class-mail.php:31
msgid "This email is part of the Really Simple Security Notification System"
msgstr "This email is part of the Really Simple Security Notification System"

#: mailer/class-mail.php:36
msgid "This email was sent to"
msgstr "This email was sent to"

#: settings/config/menu.php:588
#: settings/config/menu.php:625
#: settings/config/menu.php:638
#: settings/config/menu.php:675
msgid "This feature allows you to block visitors from your website based on country"
msgstr "This feature allows you to block visitors from your website based on country"

#: settings/config/fields/vulnerability-detection.php:14
msgid "This feature depends on multiple standard background processes. If a process fails or is unavailable on your system, detection might not work. We run frequent tests for this purpose. We will notify you accordingly if there are any issues."
msgstr "This feature depends on multiple standard background processes. If a process fails or is unavailable on your system, detection might not work. We run frequent tests for this purpose. We will notify you accordingly if there are any issues."

#: settings/config/fields/two-fa.php:102
#: settings/config/fields/vulnerability-detection.php:112
#: settings/config/fields/vulnerability-detection.php:130
msgid "This feature is disabled because you have not verified that e-mail is correctly configured on your site."
msgstr "This feature is disabled because you have not verified that e-mail is correctly configured on your site."

#: pro/security/wordpress/change-login-url.php:180
msgid "This feature is not enabled."
msgstr "This feature is not enabled."

#: lets-encrypt/class-letsencrypt-handler.php:1627
msgid "This is a multisite configuration with subdomains. You should generate a wildcard certificate on the root domain."
msgstr "This is a multisite configuration with subdomains. You should generate a wildcard certificate on the root domain."

#: settings/config/menu.php:173
msgid "This is a security feature implemented by web browsers to control how web pages from different origins can interact with each other."
msgstr "This is a security feature implemented by web browsers to control how web pages from different origins can interact with each other."

#: pro/security/wordpress/permission-detection/permission-detection.php:228
msgid "This is a security warning from Really Simple Security for %s."
msgstr "This is a security warning from Really Simple Security for %s."

#: security/wordpress/vulnerabilities.php:402
#: security/wordpress/vulnerabilities.php:424
msgid "This is a test notification from Really Simple Security. You can safely dismiss this message."
msgstr "This is a test notification from Really Simple Security. You can safely dismiss this message."

#: security/wordpress/vulnerabilities.php:1535
msgid "This is a vulnerability alert from Really Simple Security for %s. "
msgstr "This is a vulnerability alert from Really Simple Security for %s. "

#: pro/security/wordpress/vulnerabilities-pro.php:688
msgid "This is the end of the update cycle, we recommend manually removing %s."
msgstr "This is the end of the update cycle, we recommend manually removing %s."

#: pro/security/wordpress/vulnerabilities-pro.php:691
msgid "This is the end of the update cycle, we recommend manually removing vulnerable components."
msgstr "This is the end of the update cycle, we recommend manually removing vulnerable components."

#: settings/config/menu.php:200
msgid "This is the vulnerability overview. Here you will find current known vulnerabilities on your system. You can find more information and helpful, actionable insights for every vulnerability under details."
msgstr "This is the vulnerability overview. Here you will find current known vulnerabilities on your system. You can find more information and helpful, actionable insights for every vulnerability under details."

#: class-multisite.php:127
msgid "This leads to issues when activating SSL networkwide since subdomains will be forced over SSL as well while they don't have a valid certificate."
msgstr "This leads to issues when activating SSL networkwide since subdomains will be forced over SSL as well while they don't have a valid certificate."

#: pro/class-licensing.php:634
msgid "This license is not valid for this product. Find out why on your %saccount%s."
msgstr "This licence is not valid for this product. Find out why on your %saccount%s."

#: pro/class-licensing.php:627
msgid "This license is not valid. Find out why on your %saccount%s."
msgstr "This licence is not valid. Find out why on your %saccount%s."

#: settings/config/menu.php:643
msgid "This list shows all individually blocked IP addresses. On top the top-right you can filter between permanent blocks and temporary blocks. By default, blocks are only temporary, as attackers and bots will frequently alter between IP addresses. However, you can manually configure permanent blocks."
msgstr "This list shows all individually blocked IP addresses. On top the top-right you can filter between permanent blocks and temporary blocks. By default, blocks are only temporary, as attackers and bots will frequently alter between IP addresses. However, you can manually configure permanent blocks."

#: settings/config/fields/security-headers.php:50
msgid "This option is handled by the Content Security Policy/frame-ancestors setting."
msgstr "This option is handled by the Content Security Policy/frame-ancestors setting."

#: settings/config/fields/hardening-extended.php:38
#: settings/config/fields/hardening-extended.php:39
msgid "This setting will block attempts to assign administrator roles outside the native user creation process by WordPress. This might include other plugins that create, edit or assign roles to users. If you need to create an administrator in a third-party plugin, temporarily disable this setting while you make the changes."
msgstr "This setting will block attempts to assign administrator roles outside the native user creation process by WordPress. This might include other plugins that create, edit or assign roles to users. If you need to create an administrator in a third-party plugin, temporarily disable this setting while you make the changes."

#: assets/templates/two_fa/onboarding.php:48
msgid "This site requires you to secure your account with a second authentication method."
msgstr "This site requires you to secure your account with a second authentication method."

#: pro/security/wordpress/class-rsssl-geo-block.php:1141
msgid "This website is unavailable in your region."
msgstr "This website is unavailable in your region."

#: lets-encrypt/config/fields.php:149
msgid "This will include both the www. and non-www. version of your domain."
msgstr "This will include both the www. and non-www. version of your domain."

#: settings/config/fields/hardening-extended.php:55
msgid "This will limit or fully disable HTTP requests that are not needed, but could be used with malicious intent."
msgstr "This will limit or fully disable HTTP requests that are not needed, but could be used with malicious intent."

#: settings/config/fields/hardening-extended.php:69
#: settings/config/fields/hardening-extended.php:70
msgid "This will permanently change your database prefixes and you can NOT rollback this feature. Please make sure you have a back-up."
msgstr "This will permanently change your database prefixes and you can NOT rollback this feature. Please make sure you have a back-up."

#: pro/class-scan.php:2195
msgid "This will put the files back that were changed by the fix option in Really Simple Security Pro."
msgstr "This will put the files back that were changed by the fix option in Really Simple Security Pro."

#: settings/config/fields/vulnerability-detection.php:109
msgid "This will send emails about vulnerabilities directly from your server. Make sure you can receive emails by the testing a preview below. If this feature is disabled, please enable notifications under general settings."
msgstr "This will send emails about vulnerabilities directly from your server. Make sure you can receive emails by the testing a preview below. If this feature is disabled, please enable notifications under general settings."

#: settings/config/fields/firewall.php:100
msgid "Threshold"
msgstr "Threshold"

#: settings/config/fields/firewall.php:82
msgid "Time left"
msgstr "Time left"

#: pro/security/wordpress/traits/trait-rsssl-country.php:254
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: pro/class-admin.php:637
msgid "TLS version"
msgstr "TLS version"

#: class-site-health.php:356
msgid "To ensure all traffic passes through SSL, please enable a 301 redirect."
msgstr "To ensure all traffic passes through SSL, please enable a 301 redirect."

#: class-admin.php:2013
msgid "To safely enable SSL on your server configuration, you should add the following line of code to your wp-config.php."
msgstr "To safely enable SSL on your server configuration, you should add the following line of code to your wp-config.php."

#: mailer/class-mail.php:117
msgid "To use certain features in Really Simple Security we need to confirm emails are delivered without issues."
msgstr "To use certain features in Really Simple Security we need to confirm emails are delivered without issues."

#: pro/security/wordpress/traits/trait-rsssl-country.php:255
msgid "Togo"
msgstr "Togo"

#: pro/security/wordpress/traits/trait-rsssl-country.php:256
msgid "Tokelau"
msgstr "Tokelau"

#: lets-encrypt/class-letsencrypt-handler.php:548
#: lets-encrypt/integrations/cpanel/functions.php:47
msgid "Token not generated. Please complete the previous step."
msgstr "Token not generated. Please complete the previous step."

#: lets-encrypt/class-letsencrypt-handler.php:456
msgid "Token not received yet."
msgstr "Token not received yet."

#: lets-encrypt/class-letsencrypt-handler.php:441
#: lets-encrypt/class-letsencrypt-handler.php:449
msgid "Token successfully retrieved. Click the refresh button if it's not visible yet."
msgstr "Token successfully retrieved. Click the refresh button if it's not visible yet."

#: pro/security/wordpress/traits/trait-rsssl-country.php:257
msgid "Tonga"
msgstr "Tonga"

#: security/wordpress/two-fa/controllers/class-rsssl-email-controller.php:310
msgid "Too many attempts. Please try again later."
msgstr "Too many attempts. Please try again later."

#. translators: %s: time delay between login attempts
#: security/wordpress/two-fa/class-rsssl-two-factor.php:1013
msgid "Too many invalid verification codes, you can try again in %s. This limit protects your account against automated attacks."
msgstr "Too many invalid verification codes, you can try again in %s. This limit protects your account against automated attacks."

#: settings/config/fields/two-fa.php:128
msgid "TOTP means authentication using apps like Google Authenticator."
msgstr "TOTP means authentication using apps like Google Authenticator."

#: settings/config/menu.php:322
msgid "TOTP requires users to authenticate using a third-party app such as Google Authenticator."
msgstr "TOTP requires users to authenticate using a third-party app such as Google Authenticator."

#: settings/config/fields/limit-login-attempts.php:102
msgid "Trigger captcha on failed login attempt"
msgstr "Trigger captcha on failed login attempt"

#: settings/config/fields/firewall.php:146
msgid "Trigger Captcha on lockout"
msgstr "Trigger Captcha on lockout"

#: pro/security/wordpress/traits/trait-rsssl-country.php:258
msgid "Trinidad and Tobago"
msgstr "Trinidad and Tobago"

#: pro/security/wordpress/limitlogin/class-rsssl-admin-config-countries.php:120
#: settings/config/fields/firewall.php:215
#: settings/config/fields/limit-login-attempts.php:124
#: settings/config/fields/limit-login-attempts.php:169
#: settings/config/fields/limit-login-attempts.php:215
#: settings/config/menu.php:417
#: settings/config/menu.php:452
msgid "Trusted"
msgstr "Trusted"

#: settings/config/menu.php:628
#: settings/config/menu.php:629
msgid "Trusted IP addresses"
msgstr "Trusted IP addresses"

#: lets-encrypt/class-letsencrypt-handler.php:1241
msgid "Trying to create directory in root of website."
msgstr "Trying to create directory in root of website."

#: pro/security/wordpress/traits/trait-rsssl-country.php:259
msgid "Tunisia"
msgstr "Tunisia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:260
msgid "Turkey"
msgstr "Turkey"

#: pro/security/wordpress/traits/trait-rsssl-country.php:261
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pro/security/wordpress/traits/trait-rsssl-country.php:262
msgid "Turks and Caicos Islands"
msgstr "Turks and Caicos Islands"

#: pro/security/wordpress/traits/trait-rsssl-country.php:263
msgid "Tuvalu"
msgstr "Tuvalu"

#: settings/config/fields/security-headers.php:144
msgid "Two years (required for preload)"
msgstr "Two years (required for preload)"

#: assets/templates/two_fa/expired.php:3
#: assets/templates/two_fa/onboarding.php:44
#: class-admin.php:3327
#: class-site-health.php:61
#: onboarding/class-onboarding.php:428
#: onboarding/class-onboarding.php:469
#: settings/config/menu.php:302
#: modal/build/index.d4bca8705bbc6e3e5777.js:1
#: modal/src/components/DeactivationModal/DeactivationModal.js:91
msgid "Two-Factor Authentication"
msgstr "Two-Factor Authentication"

#: assets/templates/two_fa/profile-settings.php:21
msgid "Two-Factor Authentication adds an extra layer of security to your account. You can enable it here."
msgstr "Two-Factor Authentication adds an extra layer of security to your account. You can enable it here."

#: pro/security/notices.php:55
msgid "Two-Factor Authentication enabled for administrators."
msgstr "Two-Factor Authentication enabled for administrators."

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:260
msgid "Two-Factor Authentication for TOTP failed. No Authentication code provided, please try again."
msgstr "Two-Factor Authentication for TOTP failed. No Authentication code provided, please try again."

#: assets/templates/two_fa/profile-settings.php:24
msgid "Two-Factor Authentication is mandatory for your account, so you need to make a selection."
msgstr "Two-Factor Authentication is mandatory for your account, so you need to make a selection."

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:198
msgid "Two-Factor Authentication settings have been reset."
msgstr "Two-Factor Authentication settings have been reset."

#: security/wordpress/two-fa/class-rsssl-two-factor.php:1353
msgid "Two-Factor Authentication Setup"
msgstr "Two-Factor Authentication Setup"

#. translators: %s: the site's domain
#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:309
msgid "Two-Factor Backup Codes for %s"
msgstr "Two-Factor Backup Codes for %s"

#. translators: %s: URL for code regeneration
#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:146
msgid "Two-Factor: You are out of backup codes and need to <a href=\"%s\">regenerate!</a>"
msgstr "Two-Factor: You are out of backup codes and need to <a href=\"%s\">regenerate!</a>"

#: settings/config/fields/encryption.php:94
msgid "Type"
msgstr "Type"

#: pro/security/wordpress/traits/trait-rsssl-country.php:264
msgid "Uganda"
msgstr "Uganda"

#: pro/security/wordpress/traits/trait-rsssl-country.php:265
msgid "Ukraine"
msgstr "Ukraine"

#: lets-encrypt/integrations/cpanel/cpanel.php:294
msgid "Unable to connect to cPanel"
msgstr "Unable to connect to cPanel"

#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:329
msgid "Unable to enable Backup Codes provider for this user."
msgstr "Unable to enable Backup Codes provider for this user."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:129
msgid "Unable to save Two Factor Authentication code. Please re-scan the QR code and enter the code provided by your application."
msgstr "Unable to save Two Factor Authentication code. Please re-scan the QR code and enter the code provided by your application."

#: settings/config/fields/hardening-extended.php:43
msgid "Unauthorized administrators"
msgstr "Unauthorised administrators"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:569
msgid "Unblock link sent"
msgstr "Unblock link sent"

#. translators: %s is replaced with the email address.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:335
msgid "Unblock link sent to %s"
msgstr "Unblock link sent to %s"

#: pro/security/wordpress/traits/trait-rsssl-country.php:266
msgid "United Arab Emirates"
msgstr "United Arab Emirates"

#: pro/security/wordpress/traits/trait-rsssl-country.php:267
msgid "United Kingdom"
msgstr "United Kingdom"

#: pro/security/wordpress/traits/trait-rsssl-country.php:268
msgid "United States"
msgstr "United States"

#: pro/security/wordpress/traits/trait-rsssl-country.php:269
msgid "United States Minor Outlying Islands"
msgstr "United States Minor Outlying Islands"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:86
msgid "Unknown device"
msgstr "Unknown device"

#: pro/csp-violation-endpoint.php:72
msgid "Unknown directive given."
msgstr "Unknown directive given."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:69
msgid "Unknown error occurred"
msgstr "Unknown error occurred"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:369
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:580
msgid "Unknown event"
msgstr "Unknown event"

#: upgrade/upgrade-to-pro.php:402
msgid "Unknown plugin encountered."
msgstr "Unknown plugin encountered."

#: pro/class-licensing.php:518
msgid "unlimited"
msgstr "unlimited"

#: pro/class-licensing.php:542
msgid "Unlimited activations available."
msgstr "Unlimited activations available."

#: pro/security/wordpress/class-rsssl-password-security.php:848
msgid "Unlock Account"
msgstr "Unlock Account"

#: settings/config/fields/hardening-basic.php:90
msgid "Unset X-Powered-By header"
msgstr "Unset X-Powered-By header"

#: class-wp-cli.php:1252
msgid "Update a Really Simple Security option. Usage: wp rsssl update_option --name=option_name --value=option_value. Use 0 and 1 for booleans."
msgstr "Update a Really Simple Security option. Usage: wp rsssl update_option --name=option_name --value=option_value. Use 0 and 1 for booleans."

#: pro/security/wordpress/vulnerabilities-pro.php:596
#: pro/security/wordpress/vulnerabilities-pro.php:625
#: pro/security/wordpress/vulnerabilities-pro.php:655
#: pro/security/wordpress/vulnerabilities-pro.php:684
msgid "Update Alert: %s"
msgstr "Update Alert: %s"

#: class-wp-cli.php:1372
msgid "Update the advanced-headers.php with the latest rules."
msgstr "Update the advanced-headers.php with the latest rules."

#: class-admin.php:1946
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPlugins.js:21
msgid "Upgrade"
msgstr "Upgrade"

#: pro/class-licensing.php:531
msgid "Upgrade to a %s5 sites or Agency%s license."
msgstr "Upgrade to a %s5 sites or Agency%s licence."

#: pro/class-licensing.php:534
msgid "Upgrade to an %sAgency%s license."
msgstr "Upgrade to an %sAgency%s licence."

#: pro/security/wordpress/vulnerabilities-pro.php:427
#: pro/security/wordpress/vulnerabilities-pro.php:478
msgid "Upgrading..."
msgstr "Upgrading..."

#: pro/class-scan.php:1613
msgid "uploads file, possibly generated by plugin or theme"
msgstr "uploads file, possibly generated by plugin or theme"

#: security/wordpress/block-code-execution-uploads.php:19
msgid "Uploads folder not writable."
msgstr "Uploads folder not writable."

#: pro/security/wordpress/traits/trait-rsssl-country.php:270
msgid "Uruguay"
msgstr "Uruguay"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-totp.php:615
msgid "Use an authenticator app on your mobile device to generate a code."
msgstr "Use an authenticator app on your mobile device to generate a code."

#. Translators: %s is the hyperlink for "Download"
#: assets/templates/two_fa/totp-config.php:22
msgid "Use your authenticator app like Google Authenticator to scan the QR code below, then paste the provided Authentication code. %s"
msgstr "Use your authenticator app like Google Authenticator to scan the QR code below, then paste the provided Authentication code. %s"

#: settings/config/fields/limit-login-attempts.php:298
msgid "User"
msgstr "User"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:290
msgid "User %s added to permanent blocklist"
msgstr "User %s added to permanent blocklist"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:281
msgid "User %s added to temporary blocklist"
msgstr "User %s added to temporary blocklist"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:296
msgid "User %s added to trusted  IP list"
msgstr "User %s added to trusted  IP list"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:293
msgid "User %s removed from permanent blocklist"
msgstr "User %s removed from permanent blocklist"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:285
msgid "User %s removed from temporary blocklist"
msgstr "User %s removed from temporary blocklist"

#. translators: %s is replaced with the username.
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:300
msgid "User %s removed from trusted IP list"
msgstr "User %s removed from trusted IP list"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:549
msgid "User added to blocklist"
msgstr "User added to blocklist"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:551
msgid "User added to trusted list"
msgstr "User added to trusted list"

#: pro/security/wordpress/class-rsssl-geo-block.php:1922
msgid "User agent added."
msgstr "User agent added."

#: pro/security/wordpress/class-rsssl-geo-block.php:1962
msgid "User agent removed from current list."
msgstr "User agent removed from current list."

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:544
msgid "User locked out"
msgstr "User locked out"

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:146
#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:163
msgid "User not logged in."
msgstr "User not logged in."

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:547
msgid "User removed from blocklist"
msgstr "User removed from blocklist"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:553
msgid "User removed from trusted list"
msgstr "User removed from trusted list"

#: settings/config/fields/access-control.php:44
msgid "User roles for password change"
msgstr "User roles for password change"

#: settings/config/fields/firewall.php:180
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/firewall/UserAgentModal.js:104
msgid "User-Agent"
msgstr "User-Agent"

#: settings/config/menu.php:564
#: settings/config/menu.php:565
msgid "User-Agents"
msgstr "User-Agents"

#: security/notices.php:93
#: settings/config/fields/limit-login-attempts.php:136
#: settings/config/fields/two-fa.php:155
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/LimitLoginAttempts/AddUserModal.js:60
msgid "Username"
msgstr "Username"

#: security/wordpress/rename-admin-user.php:15
msgid "Username 'admin' has been changed to %s"
msgstr "Username 'admin' has been changed to %s"

#: settings/config/fields/two-fa.php:143
#: settings/config/menu.php:331
#: settings/config/menu.php:405
#: settings/config/menu.php:425
msgid "Users"
msgstr "Users"

#: settings/config/fields/hardening-extended.php:126
msgid "Users trying to enter via /wp-admin or /wp-login.php will be redirected to this URL."
msgstr "Users trying to enter via /wp-admin or /wp-login.php will be redirected to this URL."

#: pro/security/wordpress/traits/trait-rsssl-country.php:271
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: pro/class-licensing.php:561
msgid "Valid license for %s."
msgstr "Valid licence for %s."

#: pro/class-licensing.php:525
msgid "Valid until %s."
msgstr "Valid until %s."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:131
msgid "Validate Using Passkey"
msgstr "Validate Using Passkey"

#: upgrade/upgrade-to-pro.php:84
msgid "Validating license..."
msgstr "Validating licence..."

#: class-wp-cli.php:1264
msgid "Value to set for the option."
msgstr "Value to set for the option."

#: pro/security/wordpress/traits/trait-rsssl-country.php:272
msgid "Vanuatu"
msgstr "Vanuatu"

#: pro/security/wordpress/traits/trait-rsssl-country.php:273
msgid "Venezuela"
msgstr "Venezuela"

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:153
msgid "Verification code re-sent"
msgstr "Verification code re-sent"

#: security/wordpress/two-fa/class-rsssl-two-factor-profile-settings.php:169
msgid "Verification code sent"
msgstr "Verification code sent."

#: assets/templates/two_fa/onboarding.php:118
#: assets/templates/two_fa/profile-settings.php:115
#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:370
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:356
msgid "Verification Code:"
msgstr "Verification Code:"

#: mailer/class-mail.php:118
msgid "Verify email"
msgstr "Verify email"

#: onboarding/class-onboarding.php:186
msgid "Verify your email"
msgstr "Verify your email"

#: settings/config/fields/general.php:92
msgid "Verify your email address to get the most out of Really Simple Security."
msgstr "Verify your email address to get the most out of Really Simple Security."

#: pro/security/wordpress/vulnerabilities-pro.php:665
#: pro/security/wordpress/vulnerabilities-pro.php:697
#: pro/security/wordpress/vulnerabilities-pro.php:758
msgid "Verify your website"
msgstr "Verify your website"

#: pro/security/wordpress/traits/trait-rsssl-country.php:274
msgid "Viet Nam"
msgstr "Viet Nam"

#: class-admin.php:665
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/build/829.0d69f68a1345874307b1.js:1
#: settings/src/Dashboard/TaskElement.js:72
#: settings/src/Dashboard/TaskElement.js:76
#: settings/src/Settings/MixedContentScan/MixedContentScan.js:80
msgid "View"
msgstr "View"

#: class-admin.php:652
msgid "View Dashboard"
msgstr "View Dashboard"

#: class-multisite.php:112
msgid "View settings page"
msgstr "View settings page"

#: security/wordpress/vulnerabilities.php:330
msgid "View vulnerabilities"
msgstr "View vulnerabilities"

#: pro/security/wordpress/traits/trait-rsssl-country.php:275
msgid "Virgin Islands, British"
msgstr "Virgin Islands, British"

#: pro/security/wordpress/traits/trait-rsssl-country.php:276
msgid "Virgin Islands, U.s."
msgstr "Virgin Islands, U.s."

#: upgrade/upgrade-to-pro.php:333
msgid "Visit Dashboard"
msgstr "Visit Dashboard"

#: security/wordpress/vulnerabilities.php:625
#: settings/config/menu.php:182
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Vulnerabilities/VulnerabilitiesHeader.js:15
msgid "Vulnerabilities"
msgstr "Vulnerabilities"

#: security/wordpress/vulnerabilities.php:316
msgid "Vulnerabilities detected"
msgstr "Vulnerabilities detected"

#: settings/config/fields/vulnerability-detection.php:145
msgid "Vulnerabilities Overview"
msgstr "Vulnerabilities Overview"

#: security/wordpress/vulnerabilities.php:1533
msgid "Vulnerability Alert: %s"
msgstr "Vulnerability Alert: %s"

#: onboarding/class-onboarding.php:499
msgid "Vulnerability Measures"
msgstr "Vulnerability Measures"

#: onboarding/class-onboarding.php:393
#: settings/config/menu.php:187
#: settings/config/menu.php:192
#: settings/build/366.79830113ad25eba9fb57.js:1
#: settings/src/Dashboard/Progress/ProgressFooter.js:58
#: settings/src/Dashboard/Progress/ProgressFooter.js:62
msgid "Vulnerability scan"
msgstr "Vulnerability scan"

#: security/notices.php:117
msgid "Vulnerability scanning is enabled."
msgstr "Vulnerability scanning is enabled."

#: security/wordpress/vulnerabilities.php:1187
msgid "Vulnerability: %s"
msgstr "Vulnerability: %s"

#: pro/security/wordpress/traits/trait-rsssl-country.php:277
msgid "Wallis and Futuna"
msgstr "Wallis and Futuna"

#: class-admin.php:1944
#: pro/class-licensing.php:653
#: settings/build/485.47f7474dc2a61c04262b.js:1
#: settings/src/Settings/MixedContentScan/MixedContentScan.js:58
msgid "Warning"
msgstr "Warning"

#: security/wordpress/two-fa/class-rsssl-two-factor.php:722
msgid "Warning: There has been %1$s failed login attempt on your account without providing a valid two-factor token. The last failed login occurred %2$s ago. If this wasn't you, you should reset your password."
msgid_plural "Warning: %1$s failed login attempts have been detected on your account without providing a valid two-factor token. The last failed login occurred %2$s ago. If this wasn't you, you should reset your password."
msgstr[0] "Warning: There has been %1$s failed login attempt on your account without providing a valid two-factor token. The last failed login occurred %2$s ago. If this wasn't you, you should reset your password."
msgstr[1] "Warning: %1$s failed login attempts have been detected on your account without providing a valid two-factor token. The last failed login occurred %2$s ago. If this wasn't you, you should reset your password."

#: pro/security/wordpress/hibp-password-check.php:93
#: pro/security/wordpress/hibp-password-check.php:111
#: pro/security/wordpress/hibp-password-check.php:133
msgid "Warning: This password has been found in %d data breaches. Please choose a different password."
msgstr "Warning: This password has been found in %d data breaches. Please choose a different password."

#: settings/config/menu.php:482
#: settings/config/menu.php:692
msgid "Warnings"
msgstr "Warnings"

#: security/includes/check404/class-rsssl-simple-404-interceptor.php:108
msgid "We detected suspected bots triggering large numbers of 404 errors on your site."
msgstr "We detected suspected bots triggering large numbers of 404 errors on your site."

#: security/notices.php:79
msgid "We have detected administrator roles where the login and display names are the same."
msgstr "We have detected administrator roles where the login and display names are the same."

#. translators: %s is replaced with the plugin name.
#: class-admin.php:2320
msgid "We have detected the %s plugin on your website."
msgstr "We have detected the %s plugin on your website."

#: lets-encrypt/class-letsencrypt-handler.php:1190
msgid "We have not detected any known hosting limitations."
msgstr "We have not detected any known hosting limitations."

#: settings/config/menu.php:708
msgid "We have tried to make our Wizard as simple and fast as possible. Although these questions are all necessary, if there’s any way you think we can improve the plugin, please let us %sknow%s!"
msgstr "We have tried to make our Wizard as simple and fast as possible. Although these questions are all necessary, if there’s any way you think we can improve the plugin, please let us %sknow%s!"

#: class-site-health.php:88
#: pro/security/notices.php:48
msgid "We recommend to enable Two-Factor Authentication at least for administrators."
msgstr "We recommend to enable Two-Factor Authentication at least for administrators."

#: onboarding/class-onboarding.php:209
msgid "We think you will like this"
msgstr "We think you will like this"

#: pro/security/wordpress/vulnerabilities-pro.php:607
#: pro/security/wordpress/vulnerabilities-pro.php:637
msgid "We will initiate %s automatic update cycles, in the next %s hours, to mitigate available vulnerabilities."
msgstr "We will initiate %s automatic update cycles, in the next %s hours, to mitigate available vulnerabilities."

#: pro/security/wordpress/vulnerabilities-pro.php:727
msgid "We will initiate a quarantine cycle in the next %s hours to mitigate available vulnerabilities."
msgstr "We will initiate a quarantine cycle in the next %s hours to mitigate available vulnerabilities."

#: pro/security/wordpress/firewall/class-rsssl-404-interceptor.php:334
msgid "We're sorry!"
msgstr "We're sorry!"

#: pro/security/wordpress/class-rsssl-geo-block.php:1140
msgid "We're sorry."
msgstr "We're sorry."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:68
msgid "WebAuthn is not available in this browser."
msgstr "WebAuthn is not available in this browser."

#: onboarding/class-onboarding.php:180
msgid "Welcome to Really Simple Security"
msgstr "Welcome to Really Simple Security"

#: pro/security/wordpress/traits/trait-rsssl-country.php:278
msgid "Western Sahara"
msgstr "Western Sahara"

#: mailer/class-mail.php:38
msgid "Why did I receive this email?"
msgstr "Why did I receive this email?"

#: settings/config/menu.php:55
msgid "Why Premium Support?"
msgstr "Why Premium Support?"

#: placeholders/class-placeholder.php:431
msgid "Widget area"
msgstr "Widget area"

#: pro/class-scan.php:1842
msgid "Widget area \"%s\""
msgstr "Widget area \"%s\""

#: placeholders/class-placeholder.php:436
#: pro/class-scan.php:1852
msgid "Widget with mixed content"
msgstr "Widget with mixed content"

#: security/wordpress/vulnerabilities.php:642
msgid "Will run a frequent update process on vulnerable components."
msgstr "Will run a frequent update process on vulnerable components."

#: class-admin.php:2210
msgid "WordPress 301 redirect enabled. We recommend to enable a 301 .htaccess redirect."
msgstr "WordPress 301 redirect enabled. We recommend to enable a 301 .htaccess redirect."

#: pro/security/wordpress/two-fa/class-rsssl-two-factor-backup-codes.php:211
msgid "Write these down!  Once you navigate away from this page, you will not be able to view these codes again."
msgstr "Write these down!  Once you navigate away from this page, you will not be able to view these codes again."

#: settings/config/menu.php:264
msgid "XML-RPC"
msgstr "XML-RPC"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:275
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:539
msgid "XML-RPC authentication failed"
msgstr "XML-RPC authentication failed"

#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:270
#: pro/security/wordpress/eventlog/class-rsssl-event-type.php:534
msgid "XML-RPC authentication successful"
msgstr "XML-RPC authentication successful"

#: settings/config/fields/hardening-xml.php:38
msgid "XML-RPC is a mechanism originally implemented into WordPress to publish content without the need to actually login to the backend. It is also used to login to WordPress from devices other than desktop, or the regular wp-admin interface."
msgstr "XML-RPC is a mechanism originally implemented into WordPress to publish content without the need to actually login to the backend. It is also used to login to WordPress from devices other than desktop, or the regular wp-admin interface."

#: settings/config/menu.php:261
msgid "XML-RPC with Learning Mode"
msgstr "XML-RPC with Learning Mode"

#: pro/security/wordpress/traits/trait-rsssl-country.php:279
msgid "Yemen"
msgstr "Yemen"

#: settings/config/fields/security-headers.php:334
msgid "Yes (don't set header)"
msgstr "Yes (don't set header)"

#: lets-encrypt/class-letsencrypt-handler.php:300
msgid "You already have a valid SSL certificate."
msgstr "You already have a valid SSL certificate."

#: lets-encrypt/functions.php:395
msgid "You already have free SSL on your hosting environment."
msgstr "You already have free SSL on your hosting environment."

#. translators: 1: Site URL.
#: security/wordpress/two-fa/services/class-rsssl-two-fa-reminder-service.php:93
msgid "You are receiving this email because you have an account registered at %s."
msgstr "You are receiving this email because you have an account registered at %s."

#: pro/class-licensing.php:766
msgid "You are using Really Simple Security Pro single site on a multisite environment. Please install Really Simple Security multisite networkwide for multisite support."
msgstr "You are using Really Simple Security Pro single site on a multisite environment. Please install Really Simple Security multisite networkwide for multisite support."

#: lets-encrypt/config/notices.php:144
msgid "You are using the Really Simple Security Shell Exec add on, but your current version needs to be updated."
msgstr "You are using the Really Simple Security Shell Exec add on, but your current version needs to be updated."

#: pro/class-admin.php:665
msgid "You are using W3 Total Cache with Disk: Enhanced setting. This can prevent Security headers from being properly loaded."
msgstr "You are using W3 Total Cache with Disk: Enhanced setting. This can prevent Security headers from being properly loaded."

#: settings/config/menu.php:432
msgid "You can add any non-existing username to this table, to instantly block IP addresses that try common usernames like \"admin\"."
msgstr "You can add any non-existing username to this table, to instantly block IP addresses that try common usernames like \"admin\"."

#: pro/security/wordpress/vulnerabilities-pro.php:694
msgid "You can also use our ‘Quarantine’ option to automate this process in the future."
msgstr "You can also use our ‘Quarantine’ option to automate this process in the future."

#: settings/config/menu.php:221
msgid "You can choose to automate the most common actions for a vulnerability. Each action is set to a minimum risk level, similar to the notifications. Please read the instructions to learn more about the process."
msgstr "You can choose to automate the most common actions for a vulnerability. Each action is set to a minimum risk level, similar to the notifications. Please read the instructions to learn more about the process."

#: settings/config/menu.php:499
msgid "You can easily block countries, or entire continents. You can act on the event log below and see which countries are suspicious, or exclude all countries but your own."
msgstr "You can easily block countries, or entire continents. You can act on the event log below and see which countries are suspicious, or exclude all countries but your own."

#: pro/class-scan.php:1654
msgid "You can edit the source file manually by pressing the edit button."
msgstr "You can edit the source file manually by pressing the edit button."

#: lets-encrypt/config/fields.php:313
msgid "You can find your api key %shere%s (make sure you're logged in with your main account)."
msgstr "You can find your api key %shere%s (make sure you're logged in with your main account)."

#: lets-encrypt/config/fields.php:357
msgid "You can find your Plesk username and password in %s"
msgstr "You can find your Plesk username and password in %s"

#: lets-encrypt/functions.php:406
msgid "You can follow these %sinstructions%s."
msgstr "You can follow these %sinstructions%s."

#: settings/config/menu.php:466
msgid "You can indefinitely block known abusive IP addresses, to completely prevent them from trying to login."
msgstr "You can indefinitely block known abusive IP addresses, to completely prevent them from trying to login."

#: settings/config/menu.php:464
msgid "You can prevent IP addresses from being temporarily blocked by adding them to this list. This can be convenient if you share an IP address with other site users. Usernames that trigger false logins will still be blocked."
msgstr "You can prevent IP addresses from being temporarily blocked by adding them to this list. This can be convenient if you share an IP address with other site users. Usernames that trigger false logins will still be blocked."

#: settings/config/menu.php:430
msgid "You can prevent usernames from being temporarily blocked by adding them to this list. The IP address that triggers false logins will still be blocked."
msgstr "You can prevent usernames from being temporarily blocked by adding them to this list. The IP address that triggers false logins will still be blocked."

#: assets/templates/two_fa/onboarding.php:53
msgid "You can protect your account with a second authentication layer. Please choose one of the following methods, or click %s if you don't want to use Two-Factor Authentication."
msgstr "You can protect your account with a second authentication layer. Please choose one of the following methods, or click %s if you don't want to use Two-Factor Authentication."

#: pro/class-licensing.php:537
msgid "You can renew your license on your %saccount%s."
msgstr "You can renew your licence on your %saccount%s."

#: pro/class-licensing.php:716
msgid "You can upgrade on your %saccount%s."
msgstr "You can upgrade on your %saccount%s."

#: pro/class-licensing.php:744
msgid "You do not have any activations left on your Really Simple Security Pro license. Please upgrade your plan for additional activations."
msgstr "You do not have any activations left on your Really Simple Security Pro license. Please upgrade your plan for additional activations."

#: security/wordpress/vulnerabilities.php:1588
msgid "You have %s critical vulnerability"
msgid_plural "You have %s critical vulnerabilities"
msgstr[0] "You have %s critical vulnerability"
msgstr[1] "You have %s critical vulnerabilities"

#: security/wordpress/vulnerabilities.php:1591
msgid "You have %s high-risk vulnerability"
msgid_plural "You have %s high-risk vulnerabilities"
msgstr[0] "You have %s high-risk vulnerability"
msgstr[1] "You have %s high-risk vulnerabilities"

#: security/wordpress/vulnerabilities.php:1597
msgid "You have %s low-risk vulnerability"
msgid_plural "You have %s low-risk vulnerabilities"
msgstr[0] "You have %s low-risk vulnerability"
msgstr[1] "You have %s low-risk vulnerabilities"

#: security/wordpress/vulnerabilities.php:1594
msgid "You have %s medium-risk vulnerability"
msgid_plural "You have %s medium-risk vulnerabilities"
msgstr[0] "You have %s medium-risk vulnerability"
msgstr[1] "You have %s medium-risk vulnerabilities"

#: security/notices.php:130
msgid "You have %s open hardening feature."
msgid_plural "You have %s open hardening features."
msgstr[0] "You have %s open hardening feature."
msgstr[1] "You have %s open hardening features."

#: pro/class-licensing.php:525
msgid "You have a lifetime license."
msgstr "You have a lifetime licence."

#: settings/config/fields/hardening-extended.php:95
msgid "You have changed your login URL"
msgstr "You have changed your login URL"

#: mailer/class-mail.php:41
msgid "You have enabled a feature on %s. We think it's important to let you know a little bit more about this feature so you can use it without worries."
msgstr "You have enabled a feature on %s. We think it's important to let you know a little bit more about this feature so you can use it without worries."

#: pro/security/wordpress/limitlogin/class-rsssl-geo-location.php:84
msgid "You have enabled GEO IP, but the GEO IP database hasn't been downloaded automatically. If you continue to see this message, download the file from %1$sReally Simple Security CDN%2$s, unzip it, and put it in the %3$s folder in your WordPress uploads directory"
msgstr "You have enabled GEO IP, but the GEO IP database hasn't been downloaded automatically. If you continue to see this message, download the file from %1$sReally Simple Security CDN%2$s, unzip it, and put it in the %3$s folder in your WordPress uploads directory"

#: pro/security/notices.php:13
msgid "You have enabled the \"Rename and randomize your database prefix\" option, but the attempt to do this has failed. The option has been disabled."
msgstr "You have enabled the \"Rename and randomise your database prefix\" option, but the attempt to do this has failed. The option has been disabled."

#: pro/class-admin.php:138
msgid "You have installed Really Simple Security Pro. We have deactivated and removed the free plugin."
msgstr "You have installed Really Simple Security Pro. We have deactivated and removed the free plugin."

#: pro/security/wordpress/change-login-url.php:38
msgid "You have requested the login URL for your website. You can log in on %s."
msgstr "You have requested the login URL for your website. You can log in on %s."

#: class-site-health.php:320
msgid "You have set a 301 redirect to SSL. This is important for SEO purposes"
msgstr "You have set a 301 redirect to SSL. This is important for SEO purposes"

#: onboarding/class-onboarding.php:274
msgid "You may need to login in again, have your credentials prepared."
msgstr "You may need to login in again, have your credentials prepared."

#: class-multisite.php:126
msgid "You run a Multisite installation with subdomains, but your site doesn't have a wildcard certificate."
msgstr "You run a Multisite installation with subdomains, but your site doesn't have a wildcard certificate."

#: class-multisite.php:96
msgid "You run a Multisite installation with subfolders, which prevents this plugin from fixing your missing server variable in the wp-config.php."
msgstr "You run a multisite installation with subfolders, which prevents this plugin from fixing your missing server variable in the wp-config.php."

#: lets-encrypt/config/fields.php:150
msgid "You should have the www domain pointed to the same website as the non-www domain."
msgstr "You should have the www domain pointed to the same website as the non-www domain."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:77
msgid "You should receive a notice from your desired tool to confirm the Passkey configuration."
msgstr "You should receive a notice from your desired tool to confirm the Passkey configuration."

#: pro/security/wordpress/permission-detection/download.php:40
msgid "You should set the files to 644 permissions, and folders to 755. If you're not sure how to do this, please check with your hosting provider for help."
msgstr "You should set the files to 644 permissions, and folders to 755. If you're not sure how to do this, please check with your hosting provider for help."

#: settings/config/disable-fields-filter.php:79
msgid "You're using a feature where email is an essential part of the functionality. Please validate that you can send emails on your server."
msgstr "You're using a feature where email is an essential part of the functionality. Please validate that you can send emails on your server."

#: security/wordpress/two-fa/class-rsssl-two-factor.php:551
#: security/wordpress/two-fa/class-rsssl-two-factor.php:1299
msgid "Your 2FA grace period expired. Please contact your site administrator to regain access and to configure 2FA."
msgstr "Your 2FA grace period expired. Please contact your site administrator to regain access and to configure 2FA."

#: pro/security/wordpress/class-rsssl-event-listener.php:190
msgid "Your access has been denied due to too many failed login attempts."
msgstr "Your access has been denied due to too many failed login attempts."

#: pro/security/wordpress/class-rsssl-event-listener.php:274
#: pro/security/wordpress/class-rsssl-event-listener.php:300
msgid "Your access has been denied, please contact the webmaster for support"
msgstr "Your access has been denied, please contact the webmaster for support"

#: pro/security/wordpress/class-rsssl-event-listener.php:252
#: pro/security/wordpress/class-rsssl-event-listener.php:307
msgid "Your access has been denied, too many login attempts"
msgstr "Your access has been denied, too many login attempts"

#: pro/security/wordpress/class-rsssl-geo-block.php:1143
msgid "Your access to this site has been denied"
msgstr "Your access to this site has been denied"

#: pro/security/wordpress/class-rsssl-geo-block.php:1142
#: pro/security/wordpress/firewall/class-rsssl-404-interceptor.php:337
msgid "Your access to this site has been temporarily denied"
msgstr "Your access to this site has been temporarily denied"

#: pro/security/wordpress/class-rsssl-password-security.php:841
#: pro/security/wordpress/class-rsssl-password-security.php:842
msgid "Your account is locked"
msgstr "Your account is locked"

#: lets-encrypt/config/notices.php:71
msgid "Your certificate is valid until: %s"
msgstr "Your certificate is valid until: %s"

#: lets-encrypt/config/notices.php:111
msgid "Your certificate will be renewed and installed automatically."
msgstr "Your certificate will be renewed and installed automatically."

#: lets-encrypt/class-letsencrypt-handler.php:296
msgid "Your certificate will expire on %s."
msgstr "Your certificate will expire on %s."

#: lets-encrypt/config/notices.php:75
msgid "Your certificate will expire on %s. You can renew it %shere%s."
msgstr "Your certificate will expire on %s. You can renew it %shere%s."

#: lets-encrypt/class-letsencrypt-handler.php:226
msgid "Your domain meets the requirements for Let's Encrypt."
msgstr "Your domain meets the requirements for Let's Encrypt."

#: pro/class-importer.php:107
msgid "Your files already were restored."
msgstr "Your files already were restored."

#: pro/class-importer.php:105
msgid "Your files were restored."
msgstr "Your files were restored."

#: lets-encrypt/functions.php:404
msgid "Your hosting environment does not allow automatic SSL installation."
msgstr "Your hosting environment does not allow automatic SSL installation."

#: lets-encrypt/config/notices.php:28
msgid "Your Key and Certificate directories are not properly protected."
msgstr "Your Key and Certificate directories are not properly protected."

#: pro/class-licensing.php:620
msgid "Your license could not be found in our system. Please contact %ssupport%s."
msgstr "Your licence could not be found in our system. Please contact %ssupport%s."

#: pro/class-licensing.php:614
msgid "Your license has been revoked. Please contact %ssupport%s."
msgstr "Your licence has been revoked. Please contact %ssupport%s."

#: upgrade/upgrade-to-pro.php:548
msgid "Your license is not active for this URL."
msgstr "Your licence is not active for this URL."

#: upgrade/upgrade-to-pro.php:533
msgid "Your license key expired on %s."
msgstr "Your licence key expired on %s."

#: upgrade/upgrade-to-pro.php:539
msgid "Your license key has been disabled."
msgstr "Your licence key has been disabled."

#: pro/class-licensing.php:647
msgid "Your license key has expired. Please renew your license key on your %saccount%s."
msgstr "Your licence key has expired. Please renew your licence key on your %saccount%s."

#: upgrade/upgrade-to-pro.php:554
msgid "Your license key has reached its activation limit."
msgstr "Your licence key has reached its activation limit."

#. translators: %s: site name
#: security/wordpress/two-fa/providers/class-rsssl-two-factor-email.php:266
msgid "Your login confirmation code for %s"
msgstr "Your login confirmation code for %s"

#: settings/config/fields/hardening-extended.php:96
msgid "Your login URL has changed to {login_url} to prevent common bot attacks on standard login URLs. Learn more about this feature, common questions and measures to prevent any issues."
msgstr "Your login URL has changed to {login_url} to prevent common bot attacks on standard login URLs. Learn more about this feature, common questions and measures to prevent any issues."

#: pro/security/wordpress/class-rsssl-password-security.php:682
msgid "Your password contains (part of) your (user)name or email address. Choose a different password"
msgstr "Your password contains (part of) your (user)name or email address. Choose a different password"

#: pro/security/wordpress/class-rsssl-password-security.php:411
msgid "Your password has expired. Please change your password."
msgstr "Your password has expired. Please change your password."

#: security/wordpress/two-fa/traits/trait-rsssl-email-trait.php:28
msgid "Your password was compromised and has been reset"
msgstr "Your password was compromised and has been reset"

#. translators: %s: URL to reset password
#: security/wordpress/two-fa/class-rsssl-two-factor.php:777
msgid "Your password was reset because of too many failed Two Factor attempts. You will need to <a href=\"%s\">create a new password</a> to regain access. Please check your email for more information."
msgstr "Your password was reset because of too many failed Two Factor attempts. You will need to <a href=\"%s\">create a new password</a> to regain access. Please check your email for more information."

#: pro/security/wordpress/class-rsssl-password-security.php:864
#: pro/security/wordpress/class-rsssl-password-security.php:865
msgid "Your password will expire in 7 days"
msgstr "Your password will expire in 7 days"

#: pro/class-licensing.php:699
msgid "Your Really Simple Security Pro license hasn't been activated."
msgstr "Your Really Simple Security Pro license hasn't been activated."

#: pro/class-licensing.php:700
msgid "Your Really Simple Security Pro license is not valid."
msgstr "Your Really Simple Security Pro license is not valid."

#: pro/class-licensing.php:723
msgid "Your Really Simple Security Pro license key has expired. Please renew your license to continue receiving updates and premium support."
msgstr "Your Really Simple Security Pro license key has expired. Please renew your license to continue receiving updates and premium support."

#: pro/class-licensing.php:751
msgid "Your Really Simple Security Pro license key hasn't been activated yet. You can activate your license key on the license tab."
msgstr "Your Really Simple Security Pro license key hasn't been activated yet. You can activate your license key on the license tab."

#: pro/class-licensing.php:730
msgid "Your Really Simple Security Pro license key is not activated. Please activate your license to continue receiving updates and premium support."
msgstr "Your Really Simple Security Pro license key is not activated. Please activate your license to continue receiving updates and premium support."

#: lets-encrypt/config/notices.php:128
msgid "Your server provides shell functionality, which offers additional methods to install SSL. If installing SSL using the default methods is not possible, you can install the shell add on."
msgstr "Your server provides shell functionality, which offers additional methods to install SSL. If installing SSL using the default methods is not possible, you can install the shell add-on."

#: pro/class-headers.php:269
msgid "Your site has been configured for the HSTS preload list. If you have submitted your site, it will be preloaded. Click %shere%s to submit."
msgstr "Your site has been configured for the HSTS preload list. If you have submitted your site, it will be preloaded. Click %shere%s to submit."

#: pro/csp-violation-endpoint.php:257
msgid "Your site has exceeded the maximum size for HTTP headers. To prevent issues, the Content Security Policy won't be added to your HTTP headers."
msgstr "Your site has exceeded the maximum size for HTTP headers. To prevent issues, the Content Security Policy won't be added to your HTTP headers."

#: pro/class-headers.php:282
msgid "Your site is not configured for HSTS yet."
msgstr "Your site is not configured for HSTS yet."

#: pro/class-headers.php:275
msgid "Your site is not yet configured for the HSTS preload list."
msgstr "Your site is not yet configured for the HSTS preload list."

#: class-site-health.php:155
msgid "Your site is protected by a firewall."
msgstr "Your site is protected by a firewall."

#: class-site-health.php:124
msgid "Your site is protected by Limit Login Attempts."
msgstr "Your site is protected by Limit Login Attempts."

#: class-site-health.php:93
msgid "Your site is protected by Two-Factor Authentication (2FA)."
msgstr "Your site is protected by Two-Factor Authentication (2FA)."

#: class-site-health.php:211
msgid "Your site is set to display errors on your website"
msgstr "Your site is set to display errors on your website"

#: pro/class-admin.php:652
msgid "Your site redirects 404 pages to the http:// version of your homepage. This can cause mixed content issues with images."
msgstr "Your site redirects 404 pages to the http:// version of your homepage. This can cause mixed content issues with images."

#: pro/class-admin.php:638
msgid "Your site uses an outdated version of TLS. Upgrade to TLS 1.2 or TLS 1.3 to keep your site secure."
msgstr "Your site uses an outdated version of TLS. Upgrade to TLS 1.2 or TLS 1.3 to keep your site secure."

#: class-admin.php:2253
msgid "Your site uses Divi. This can require some additional steps before getting the secure lock."
msgstr "Your site uses Divi. This can require some additional steps before getting the secure lock."

#: class-admin.php:2239
msgid "Your site uses Elementor. This can require some additional steps before getting the secure lock."
msgstr "Your site uses Elementor. This can require some additional steps before getting the secure lock."

#: class-admin.php:2387
msgid "Your site uses plain permalinks, which causes issues with the REST API. Please use a different permalinks configuration."
msgstr "Your site uses plain permalinks, which causes issues with the REST API. Please use a different permalinks configuration."

#: class-admin.php:2123
msgid "Your SSL certificate will expire soon."
msgstr "Your SSL certificate will expire soon."

#: class-site-health.php:293
msgid "Your website does not send all essential security headers."
msgstr "Your website does not send all essential security headers."

#: lets-encrypt/integrations/cpanel/cpanel.php:108
#: lets-encrypt/integrations/cpanel/cpanel.php:192
msgid "Your website's ip address is blocked. Please add your domain's ip address to the security policy in CPanel"
msgstr "Your website's IP address is blocked. Please add your domain's IP address to the security policy in cPanel"

#: class-admin.php:2011
msgid "Your wp-config.php has to be edited, but is not writable."
msgstr "Your wp-config.php has to be edited, but is not writable."

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:85
msgid "YubiKey or other external authenticator"
msgstr "YubiKey or other external authenticator"

#: pro/security/wordpress/two-fa/providers/class-rsssl-two-factor-passkey.php:81
msgid "YubiKey or other USB device"
msgstr "YubiKey or other USB device"

#: pro/security/wordpress/traits/trait-rsssl-country.php:280
msgid "Zambia"
msgstr "Zambia"

#: pro/security/wordpress/traits/trait-rsssl-country.php:281
msgid "Zimbabwe"
msgstr "Zimbabwe"
