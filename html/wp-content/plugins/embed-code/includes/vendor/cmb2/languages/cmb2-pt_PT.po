# Copyright (C) 2016 WebDevStudios
# This file is distributed under the same license as the CMB2 package.
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <alvaro<PERSON><PERSON>@ovni.org>, 2016
msgid ""
msgstr ""
"Project-Id-Version: CMB2\n"
"Report-Msgid-Bugs-To: http://wordpress.org/support/plugin/cmb2\n"
"POT-Creation-Date: 2016-09-05 21:59+0100\n"
"PO-Revision-Date: 2016-09-05 22:07+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: pt_PT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.8.8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;__ngettext:1,2;_n:1,2;__ngettext_noop:1,2;"
"_n_noop:1,2;_c,_nc:4c,1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;_nx_noop:4c,1,2;"
"esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"esc_html_x: 1,2c\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-WPHeader: init.php\n"
"X-Poedit-SearchPath-0: .\n"

#: example-functions.php:117 tests/test-cmb-field.php:260
msgid "Test Metabox"
msgstr "Teste de metabox"

#: example-functions.php:130 example-functions.php:452
msgid "Test Text"
msgstr "Teste de texto"

#: example-functions.php:131 example-functions.php:144
#: example-functions.php:157 example-functions.php:164
#: example-functions.php:177 example-functions.php:185
#: example-functions.php:194 example-functions.php:202
#: example-functions.php:217 example-functions.php:225
#: example-functions.php:233 example-functions.php:250
#: example-functions.php:259 example-functions.php:272
#: example-functions.php:279 example-functions.php:286
#: example-functions.php:300 example-functions.php:313
#: example-functions.php:326 example-functions.php:338
#: example-functions.php:347 example-functions.php:355
#: example-functions.php:364 example-functions.php:371
#: example-functions.php:385 example-functions.php:453
#: example-functions.php:544 example-functions.php:552
#: example-functions.php:559 example-functions.php:566
#: example-functions.php:573 example-functions.php:580
#: example-functions.php:587 example-functions.php:614
#: example-functions.php:622 example-functions.php:629
#: example-functions.php:666 tests/test-cmb-field.php:272
msgid "field description (optional)"
msgstr "descrição do campo (opcional)"

#: example-functions.php:143
msgid "Test Text Small"
msgstr "Teste de texto pequeno"

#: example-functions.php:156
msgid "Test Text Medium"
msgstr "Teste de texto médio"

#: example-functions.php:163
msgid "Read-only Disabled Field"
msgstr "Campo apenas de leitura"

#: example-functions.php:167
msgid "Hey there, I'm a read-only field"
msgstr "Olá, eu sou um campo apenas de leitura"

#: example-functions.php:176
msgid "Custom Rendered Field"
msgstr "Campo com apresentação personalizada"

#: example-functions.php:184
msgid "Website URL"
msgstr "URL do website"

#: example-functions.php:193
msgid "Test Text Email"
msgstr "Teste de texto de email"

#: example-functions.php:201
msgid "Test Time"
msgstr "Teste de hora"

#: example-functions.php:209 example-functions.php:210
msgid "Time zone"
msgstr "Fuso horário"

#: example-functions.php:216
msgid "Test Date Picker"
msgstr "Teste de selecção de data"

#: example-functions.php:224
msgid "Test Date Picker (UNIX timestamp)"
msgstr "Teste de selecção de data (UNIX timestamp)"

#: example-functions.php:232
msgid "Test Date/Time Picker Combo (UNIX timestamp)"
msgstr "Teste de selecção de data e hora combinados (UNIX timestamp)"

#: example-functions.php:249
msgid "Test Money"
msgstr "Teste de moeda"

#: example-functions.php:258
msgid "Test Color Picker"
msgstr "Teste de selecção de cor"

#: example-functions.php:271
msgid "Test Text Area"
msgstr "Teste de área de texto"

#: example-functions.php:278
msgid "Test Text Area Small"
msgstr "Teste de área de texto pequena"

#: example-functions.php:285
msgid "Test Text Area for Code"
msgstr "Teste de área de texto para código"

#: example-functions.php:292
msgid "Test Title Weeeee"
msgstr "Teste de título"

#: example-functions.php:293
msgid "This is a title description"
msgstr "Isto é uma descrição de título"

#: example-functions.php:299
msgid "Test Select"
msgstr "Teste de selecção"

#: example-functions.php:305 example-functions.php:318
#: example-functions.php:330
msgid "Option One"
msgstr "Opção um"

#: example-functions.php:306 example-functions.php:319
#: example-functions.php:331
msgid "Option Two"
msgstr "Opção dois"

#: example-functions.php:307 example-functions.php:320
#: example-functions.php:332
msgid "Option Three"
msgstr "Opção três"

#: example-functions.php:312
msgid "Test Radio inline"
msgstr "Teste de botões de opções em linha"

#: example-functions.php:325
msgid "Test Radio"
msgstr "Teste de botões de opções"

#: example-functions.php:337
msgid "Test Taxonomy Radio"
msgstr "Teste de botões de opção de taxonomia"

#: example-functions.php:346
msgid "Test Taxonomy Select"
msgstr "Teste de selecção de taxonomia"

#: example-functions.php:354
msgid "Test Taxonomy Multi Checkbox"
msgstr "Teste de caixas de selecção múltiplas de taxonomia"

#: example-functions.php:363
msgid "Test Checkbox"
msgstr "Teste de caixa de selecção"

#: example-functions.php:370 tests/test-cmb-field.php:271
msgid "Test Multi Checkbox"
msgstr "Teste de caixas de selecção múltiplas"

#: example-functions.php:376 tests/test-cmb-field.php:277
msgid "Check One"
msgstr "Caixa de selecção um"

#: example-functions.php:377 tests/test-cmb-field.php:278
msgid "Check Two"
msgstr "Caixa de selecção dois"

#: example-functions.php:378 tests/test-cmb-field.php:279
msgid "Check Three"
msgstr "Caixa de selecção três"

#: example-functions.php:384
msgid "Test wysiwyg"
msgstr "Teste de wysiwyg"

#: example-functions.php:392
msgid "Test Image"
msgstr "Teste de imagem"

#: example-functions.php:393
msgid "Upload an image or enter a URL."
msgstr "Carregar uma imagem ou introduzir URL."

#: example-functions.php:399
msgid "Multiple Files"
msgstr "Múltiplos ficheiros"

#: example-functions.php:400
msgid "Upload or add multiple images/attachments."
msgstr "Carregar ou adicionar múltiplas imagens/anexos."

#: example-functions.php:407
msgid "oEmbed"
msgstr "oEmbed"

#: example-functions.php:410
#, php-format
msgid ""
"Enter a youtube, twitter, or instagram URL. Supports services listed at %s."
msgstr ""
"Insira um URL do YouTube, Twitter ou Instagram. Suporta os serviços listados "
"em %s."

#: example-functions.php:443
msgid "About Page Metabox"
msgstr "Metabox da página Sobre"

#: example-functions.php:472
msgid "Repeating Field Group"
msgstr "Grupo de campos repetível"

#: example-functions.php:480
msgid "Generates reusable form entries"
msgstr "Gera entradas de formulário reutilizáveis"

#: example-functions.php:482
msgid "Entry {#}"
msgstr "Entrada {#}"

#: example-functions.php:483
msgid "Add Another Entry"
msgstr "Adicionar outra entrada"

#: example-functions.php:484
msgid "Remove Entry"
msgstr "Remover entrada"

#: example-functions.php:497
msgid "Entry Title"
msgstr "Título da entrada"

#: example-functions.php:504
msgid "Description"
msgstr "Descrição"

#: example-functions.php:505
msgid "Write a short description for this entry"
msgstr "Escreva uma breve descrição sobre esta entrada"

#: example-functions.php:511
msgid "Entry Image"
msgstr "Imagem da entrada"

#: example-functions.php:517
msgid "Image Caption"
msgstr "Legenda da imagem"

#: example-functions.php:536
msgid "User Profile Metabox"
msgstr "Metabox do perfil de utilizador"

#: example-functions.php:543 example-functions.php:613
msgid "Extra Info"
msgstr "Informação extra"

#: example-functions.php:551
msgid "Avatar"
msgstr "Avatar"

#: example-functions.php:558
msgid "Facebook URL"
msgstr "URL do Facebook"

#: example-functions.php:565
msgid "Twitter URL"
msgstr "URL do Twitter"

#: example-functions.php:572
msgid "Google+ URL"
msgstr "URL do Google+"

#: example-functions.php:579
msgid "Linkedin URL"
msgstr "URL do LinkedIn"

#: example-functions.php:586
msgid "User Field"
msgstr "Campo de utilizador"

#: example-functions.php:606
msgid "Category Metabox"
msgstr "Metabox de categoria"

#: example-functions.php:621
msgid "Term Image"
msgstr "Imagem do termo"

#: example-functions.php:628
msgid "Arbitrary Term Field"
msgstr "Campo de termo arbitrário"

#: example-functions.php:650
msgid "Theme Options Metabox"
msgstr "Metabox de opções do tema"

#: example-functions.php:665
msgid "Site Background Color"
msgstr "Cor de fundo do site"

#: includes/CMB2.php:117
msgid "Metabox configuration is required to have an ID parameter."
msgstr "É necessário configurar a metabox para ter o parâmetro ID."

#: includes/CMB2.php:432
msgid "Click to toggle"
msgstr "Clique para alternar"

#: includes/CMB2_Ajax.php:71
msgid "Please Try Again"
msgstr "Por favor tente de novo"

#: includes/CMB2_Ajax.php:173 tests/cmb-tests-base.php:59
msgid "Remove Embed"
msgstr "Remover incorporação"

#: includes/CMB2_Ajax.php:181 includes/helper-functions.php:95
#: tests/cmb-tests-base.php:68
#, php-format
msgid "No oEmbed Results Found for %1$s. View more info at %2$s."
msgstr ""
"Nenhum resultado de oEmbed encontrado para %1$s. Ver mais informações em "
"%2$s."

#: includes/CMB2_Base.php:338
#, php-format
msgid ""
"The \"%s\" field parameter has been deprecated in favor of the \"%s\" "
"parameter."
msgstr ""
"O parâmetro de campo \"%s\" está obsoleto, por favor use o parâmetro \"%s\"."

#: includes/CMB2_Base.php:342
#, php-format
msgid ""
"Using the \"%s\" field parameter as a callback has been deprecated in favor "
"of the \"%s\" parameter."
msgstr ""
"A utilização do parâmetro de campo \"%s\" está obsoleta, por favor use o "
"parâmetro \"%s\"."

#: includes/CMB2_Base.php:372
#, php-format
msgid ""
"%1$s was called with a parameter that is <strong>deprecated</strong> since "
"version %2$s! %3$s"
msgstr ""
"A função %1$s foi chamada com um parâmetro que está <strong>obsoleto</"
"strong> desde a versão %2$s! %3$s"

#: includes/CMB2_Base.php:375
#, php-format
msgid ""
"%1$s was called with a parameter that is <strong>deprecated</strong> since "
"version %2$s with no alternative available."
msgstr ""
"A função %1$s foi chamada com um parâmetro que está <strong>obsoleto</"
"strong> desde a versão %2$s sem nenhuma alternativa disponível."

#: includes/CMB2_Base.php:408 includes/types/CMB2_Type_Base.php:138
#, php-format
msgid "Invalid %1$s property: %2$s"
msgstr "A propriedade de %1$s é inválida: %2$s"

#: includes/CMB2_Base.php:422 includes/types/CMB2_Type_Base.php:123
#, php-format
msgid "Invalid %1$s method: %2$s"
msgstr "O método de %1$s é inválido: %2$s"

#: includes/CMB2_Field.php:1116
msgid "Add Group"
msgstr "Adicionar grupo"

#: includes/CMB2_Field.php:1117
msgid "Remove Group"
msgstr "Remover grupo"

#: includes/CMB2_Field.php:1139 includes/CMB2_Field.php:1143
#: tests/test-cmb-field.php:234
msgid "None"
msgstr "Nenhum"

#: includes/CMB2_Field.php:1204
msgid "Sorry, this field does not have a cmb_id specified."
msgstr "Desculpe, este campo não tem um cmb_id especificado."

#: includes/CMB2_Field_Display.php:408 includes/CMB2_JS.php:155
#: includes/types/CMB2_Type_File_Base.php:75 tests/test-cmb-types-base.php:143
#: tests/test-cmb-types.php:701
msgid "File:"
msgstr "Ficheiro:"

#: includes/CMB2_JS.php:96 includes/CMB2_JS.php:135
msgid "Clear"
msgstr "Limpar"

#: includes/CMB2_JS.php:97
msgid "Default"
msgstr "Por omissão"

#: includes/CMB2_JS.php:98
msgid "Select Color"
msgstr "Seleccionar cor"

#: includes/CMB2_JS.php:99
msgid "Current Color"
msgstr "Cor actual"

#: includes/CMB2_JS.php:125
msgctxt "Valid formatDate string for jquery-ui datepicker"
msgid "mm/dd/yy"
msgstr "dd/mm/yy"

#: includes/CMB2_JS.php:126
msgid "Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday"
msgstr ""
"Domingo, Segunda-feira, Terça-feira, Quarta-feira, Quinta-feira, Sexta-"
"feira, Sábado"

#: includes/CMB2_JS.php:127
msgid "Su, Mo, Tu, We, Th, Fr, Sa"
msgstr "Dom, Seg, Ter, Qua, Qui, Sex, Sáb"

#: includes/CMB2_JS.php:128
msgid "Sun, Mon, Tue, Wed, Thu, Fri, Sat"
msgstr "Dom, Seg, Ter, Qua, Qui, Sex, Sáb"

#: includes/CMB2_JS.php:129
msgid ""
"January, February, March, April, May, June, July, August, September, "
"October, November, December"
msgstr ""
"Janeiro, Fevereiro, Março, Abril, Maio, Junho, Julho, Agosto, Setembro, "
"Outubro, Novembro, Dezembro"

#: includes/CMB2_JS.php:130
msgid "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
msgstr "Jan, Fev, Mar, Abr, Mai, Jun, Jul, Ago, Set, Out, Nov, Dez"

#: includes/CMB2_JS.php:131
msgid "Next"
msgstr "Seguinte"

#: includes/CMB2_JS.php:132
msgid "Prev"
msgstr "Anterior"

#: includes/CMB2_JS.php:133
msgid "Today"
msgstr "Hoje"

#: includes/CMB2_JS.php:134 includes/CMB2_JS.php:144
msgid "Done"
msgstr "Concluído"

#: includes/CMB2_JS.php:138
msgid "Choose Time"
msgstr "Escolher hora"

#: includes/CMB2_JS.php:139
msgid "Time"
msgstr "Hora"

#: includes/CMB2_JS.php:140
msgid "Hour"
msgstr "Hora"

#: includes/CMB2_JS.php:141
msgid "Minute"
msgstr "Minuto"

#: includes/CMB2_JS.php:142
msgid "Second"
msgstr "Segundo"

#: includes/CMB2_JS.php:143
msgid "Now"
msgstr "Agora"

#: includes/CMB2_JS.php:145
msgctxt ""
"Valid formatting string, as per http://trentrichardson.com/examples/"
"timepicker/"
msgid "hh:mm TT"
msgstr "hh:mm TT"

#: includes/CMB2_JS.php:151
msgid "Use this file"
msgstr "Usar este ficheiro"

#: includes/CMB2_JS.php:152
msgid "Use these files"
msgstr "Usar estes ficheiros"

#: includes/CMB2_JS.php:153 includes/types/CMB2_Type_File_Base.php:61
msgid "Remove Image"
msgstr "Remover imagem"

#: includes/CMB2_JS.php:154 includes/CMB2_Types.php:303
#: includes/types/CMB2_Type_File_Base.php:80 tests/test-cmb-types-base.php:143
#: tests/test-cmb-types.php:47 tests/test-cmb-types.php:55
#: tests/test-cmb-types.php:701
msgid "Remove"
msgstr "Remover"

#: includes/CMB2_JS.php:156 includes/types/CMB2_Type_File_Base.php:78
#: tests/test-cmb-types-base.php:143 tests/test-cmb-types.php:701
msgid "Download"
msgstr "Descarregar"

#: includes/CMB2_JS.php:157
msgid "Select / Deselect All"
msgstr "Seleccionar / desseleccionar tudo"

#: includes/CMB2_Types.php:171
#, php-format
msgid ""
"Custom field types require a Type object instantiation to use this method. "
"This method was called by the '%s' field type."
msgstr ""
"Os tipos de campo personalizados requerem uma instância do Tipo de objecto "
"para usar este método. Este método foi chamado pelo tipo de campo '%s'."

#: includes/CMB2_Types.php:174
msgid "That field type may not work as expected."
msgstr "Este tipo de campo poderá não funcionar como esperado."

#: includes/CMB2_Types.php:175
msgid "That field type will not work as expected."
msgstr "Este tipo de campo não vai funcionar como esperado."

#: includes/CMB2_Types.php:177
msgid ""
"For more information about this change see: https://github.com/mustardBees/"
"cmb-field-select2/pull/34w"
msgstr ""
"Para mais informações sobre esta alteração, consulte: https://github.com/"
"mustardBees/cmb-field-select2/pull/34w"

#: includes/CMB2_Types.php:240
msgid "Add Row"
msgstr "Adicionar linha"

#: includes/CMB2_hookup.php:128
msgid ""
"Term Metadata is a WordPress 4.4+ feature. Please upgrade your WordPress "
"install."
msgstr ""
"Metadados de termos é uma funcionalidade do WordPress 4.4+. Por favor "
"actualize a sua instalação do WordPress."

#: includes/CMB2_hookup.php:132
msgid "Term metaboxes configuration requires a \"taxonomies\" parameter."
msgstr ""
"A configuração de metaboxes de termos requer um parâmetro \"taxonomies\"."

#: includes/helper-functions.php:284
msgid "Save"
msgstr "Guardar"

#: includes/types/CMB2_Type_File.php:36 tests/test-cmb-types.php:683
#: tests/test-cmb-types.php:701
msgid "Add or Upload File"
msgstr "Adicionar ou carregar ficheiro"

#: includes/types/CMB2_Type_File_List.php:36 tests/test-cmb-types.php:639
#: tests/test-cmb-types.php:663
msgid "Add or Upload Files"
msgstr "Adicionar ou carregar ficheiros"

#: includes/types/CMB2_Type_Taxonomy_Multicheck.php:27
#: includes/types/CMB2_Type_Taxonomy_Radio.php:25
msgid "No terms"
msgstr "Nenhum termo"

#. Plugin Name of the plugin/theme
msgid "CMB2"
msgstr "CMB2"

#. Plugin URI of the plugin/theme
msgid "https://github.com/CMB2/CMB2"
msgstr "https://github.com/CMB2/CMB2"

#. Description of the plugin/theme
msgid ""
"CMB2 will create metaboxes and forms with custom fields that will blow your "
"mind."
msgstr ""
"O CMB2 permite criar metaboxes e formulários com campos personalizados que "
"são surpreendentes."

#. Author of the plugin/theme
msgid "WebDevStudios"
msgstr "WebDevStudios"

#. Author URI of the plugin/theme
msgid "http://webdevstudios.com"
msgstr "http://webdevstudios.com"
