.weather-atlas-wrapper {
	font-family : 'Open Sans', sans-serif;
	font-size   : 1em;
	line-height : 1.5;
	text-shadow : 1px 1px 0 rgba(0, 0, 0, 1);
	text-align  : center
	}
.weather-atlas-wrapper a {
	text-decoration : none !important;
	box-shadow      : none !important;
	border          : none !important;
	outline         : none !important
	}
.weather-atlas-wrapper .weather-atlas-header {
	padding        : .3em;
	text-align     : center;
	background     : rgba(0, 0, 0, .075);
	text-transform : uppercase
	}
.weather-atlas-wrapper .weather-atlas-body {
	margin  : 0 auto;
	padding : .9em 1.4em
	}
@media (min-width : 480px) {
	.weather-atlas-wrapper .current_horizontal {
		overflow : hidden;
		clear    : both
		}
	
	.weather-atlas-wrapper .current_horizontal .current_temp {
		float : left;
		width : 50%
		}
	
	.weather-atlas-wrapper .current_horizontal .current_text_2 {
		font-size    : 1em;
		float        : left;
		text-align   : left;
		padding-left : 10%;
		width        : 40%
		}
	}
.weather-atlas-wrapper .current_temp {
	text-align    : center;
	margin-bottom : .5em
	}
.weather-atlas-wrapper .current_temp .wi {
	font-size : 3.5em
	}
.weather-atlas-wrapper .current_temp .sunrise_sunset, .weather-atlas-wrapper .current_temp .sunrise_sunset .wi {
	margin-top : .5em;
	font-size  : .85em
	}
.weather-atlas-wrapper .current_temp .temp {
	font-size      : 3.75em;
	line-height    : 1.2;
	margin-left    : .1em;
	letter-spacing : -.05em
	}
.weather-atlas-wrapper .current_temp .current_text {
	font-size      : 1.5em;
	line-height    : 1.2;
	text-transform : lowercase
	}
.weather-atlas-wrapper .current_text_2 {
	display : none
	}
@media (min-width : 480px) {
	.weather-atlas-wrapper .current_text_2 {
		margin-top     : .2em;
		display        : inline-block;
		text-align     : left;
		font-size      : .9em;
		text-transform : lowercase
		}
	}
.weather-atlas-wrapper .daily, .weather-atlas-wrapper .hourly {
	margin-top   : .25em;
	display      : table;
	table-layout : fixed;
	width        : 100%;
	text-align   : center
	}
.weather-atlas-wrapper .daily.days, .weather-atlas-wrapper .hourly.hours {
	text-transform : lowercase;
	margin-top     : .9em
	}
.weather-atlas-wrapper .extended_day, .weather-atlas-wrapper .extended_hour {
	display : table-cell
	}
.weather-atlas-wrapper .extended_day_4, .weather-atlas-wrapper .extended_day_5, .weather-atlas-wrapper .extended_hour_4, .weather-atlas-wrapper .extended_hour_5 {
	display : none
	}
@media (min-width : 480px) {
	.weather-atlas-wrapper .extended_day_4, .weather-atlas-wrapper .extended_day_5, .weather-atlas-wrapper .extended_hour_4, .weather-atlas-wrapper .extended_hour_5 {
		display : table-cell
		}
	}
.weather-atlas-wrapper .extended_day .wi, .weather-atlas-wrapper .extended_hour .wi {
	font-size   : 2em;
	line-height : 1.3
	}
.weather-atlas-wrapper .weather-atlas-text {
	text-align : left;
	margin-top : 1.8em;
	padding    : 1.2em 1.5em 0 1.5em;
	background : rgba(0, 0, 0, .05)
	}
.weather-atlas-wrapper .weather-atlas-footer {
	padding    : .25em 1em;
	font-size  : .9em;
	text-align : center;
	background : rgba(0, 0, 0, .05)
	}
.weather-atlas-wrapper .weather-atlas-footer .weather-atlas-footer-block {
	display : inline-block
	}
.weather-atlas-wrapper .city_name {
	font-family    : 'Open Sans', sans-serif;
	font-size      : 0.85em;
	line-height    : 1;
	text-shadow    : none;
	text-align     : left;
	color          : #000;
	text-transform : uppercase
	}
.weather-atlas-wrapper .city_name {
	padding : 0.2em 5px;
	margin  : -0.3em
	}
.weather-atlas-wrapper .city_name {
	text-decoration : none !important;
	box-shadow      : none !important;
	border          : none !important;
	outline         : none !important
	}
.weather-atlas-wrapper input.city_name {
	-webkit-border-radius : 0;
	-moz-border-radius    : 0;
	border-radius         : 0
	}
.weather-atlas-wrapper .city_name::-webkit-input-placeholder {
	font-family    : 'Open Sans', sans-serif;
	color          : #333;
	text-transform : lowercase
	}
.weather-atlas-wrapper .city_name::-moz-placeholder {
	font-family    : 'Open Sans', sans-serif;
	color          : #333;
	text-transform : lowercase
	}
.weather-atlas-wrapper .city_name:-ms-input-placeholder {
	font-family    : 'Open Sans', sans-serif;
	color          : #333;
	text-transform : lowercase
	}
.weather-atlas-wrapper .city_name:-moz-placeholder {
	font-family    : 'Open Sans', sans-serif;
	color          : #333;
	text-transform : lowercase
	}
.weather-atlas-header-title-wrapper {
	display : inline-block;
	margin  : 0 0 0 10%;
	width   : 80%
	}
.dashicons-admin-site-alt3 {
	color : #b60002;
	}
.components-placeholder__label .dashicons {
	margin : 0 5px 0 10px;
	}
.wp-block-weather-atlas-weather-widget {
	padding : 10px 5px;
	}
.wp-block-weather-atlas-weather-widget .components-placeholder__label {
	color     : #b60002;
	font-size : 18px
	}