/* https://codebeautify.org/minify-css */
.weather_demo_wrapper {
	font-size : 1rem;
	padding   : 30px 10px
	}
@media (min-width : 768px) {
	.weather_demo_wrapper {
		padding : 30px 100px
		}
	}
@media (min-width : 1025px) {
	.weather_demo_wrapper {
		padding : 30px 200px
		}
	}
.autocomplete-container {
	position : relative;
	display  : inline-block;
	width    : 25em;
	}
.autocomplete-dropdown {
	position   : absolute;
	top        : 100%; /* Aligns right below the input */
	left       : 0;
	border     : 1px solid #ddd;
	border-top : none;
	z-index    : 1000;
	background : white;
	width      : 100%;
	display    : none;
	}
.autocomplete-dropdown div {
	padding : 8px;
	cursor  : pointer;
	}
.autocomplete-dropdown div:hover {
	background-color : #f0f0f0;
	}
.clear-input {
	position  : absolute;
	right     : 10px;
	top       : 50%;
	transform : translateY(-50%);
	cursor    : pointer;
	color     : #8c8f94;
	}
.clear-input:hover {
	color : #000;
	}
/* Adjust the right padding of the input field to avoid overlapping with the "X" */
#city_name {
	padding-right : 30px;
	}
/* New styles for the loader inside input */
#city_name.loading {
	background-image    : url('/wp-admin/images/loading.gif');
	background-position : right 5px center;
	background-repeat   : no-repeat;
	}
/* Adjust padding to ensure text doesn't overlap the loader */
#city_name {
	padding-right : 30px;
	}