# Translation of Plugins - WP User Avatar | User Profile Picture - Stable (latest release) in Spanish (Spain)
# This file is distributed under the same license as the Plugins - WP User Avatar | User Profile Picture - Stable (latest release) package.
msgid ""
msgstr ""
"Project-Id-Version: Plugins - WP User Avatar | User Profile Picture - Stable "
"(latest release)\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/one-user-avatar\n"
"POT-Creation-Date: 2024-10-20T16:00:21+00:00\n"
"PO-Revision-Date: 2024-10-20 18:31+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Poedit 3.5\n"

#. Plugin Name of the plugin
#: one-user-avatar.php includes/class-wp-user-avatar-admin.php:209
#: includes/class-wp-user-avatar-admin.php:436
#: includes/class-wp-user-avatar-widget.php:32
#: includes/wpua-options-page.php:67 includes/wpua-tinymce-window.php:31
msgid "One User Avatar"
msgstr "One User Avatar"

#. Plugin URI of the plugin
#: one-user-avatar.php
msgid "https://onedesigns.com/plugins/one-user-avatar/"
msgstr "https://onedesigns.com/plugins/one-user-avatar/"

#. Description of the plugin
#: one-user-avatar.php
msgid ""
"Use any image from your WordPress Media Library as a custom user avatar. Add "
"your own Default Avatar. Fork of WP User Avatar v2.2.16."
msgstr ""
"Utiliza cualquier imagen de tu biblioteca de medios de WordPress como un "
"avatar personalizado. Añade tu propio Avatar por defecto. Bifurcación de WP "
"User Avatar v2.2.16."

#. Author of the plugin
#: one-user-avatar.php
msgid "One Designs"
msgstr "One Designs"

#. Author URI of the plugin
#: one-user-avatar.php
msgid "https://onedesigns.com/"
msgstr "https://onedesigns.com/"

#: includes/class-wp-user-avatar-admin.php:131
msgid "Sorry, you are not allowed to move this avatar to the Trash."
msgstr "No tienes permisos para mover este avatar a la papelera."

#: includes/class-wp-user-avatar-admin.php:135
msgid "Error in moving the avatar to Trash."
msgstr "Error al mover el avatar a la papelera."

#: includes/class-wp-user-avatar-admin.php:156
msgid "Sorry, you are not allowed to restore this avatar from the Trash."
msgstr "No tienes permisos para restaurar este avatar desde la papelera."

#: includes/class-wp-user-avatar-admin.php:160
msgid "Error in restoring the avatar from Trash."
msgstr "Error al restaurar el avatar desde la papelera."

#: includes/class-wp-user-avatar-admin.php:175
msgid "Sorry, you are not allowed to delete this avatar."
msgstr "No tienes permisos para eliminar este avatar."

#: includes/class-wp-user-avatar-admin.php:179
msgid "Error in deleting the avatar."
msgstr "Error al eliminar el avatar."

#: includes/class-wp-user-avatar-admin.php:210
#: includes/class-wp-user-avatar-admin.php:268 includes/wpua-media-page.php:139
msgid "Avatars"
msgstr "Avatares"

#: includes/class-wp-user-avatar-admin.php:219
#: includes/class-wp-user-avatar-admin.php:220
#: includes/class-wp-user-avatar-admin.php:473
#: includes/wpua-options-page.php:128
msgid "Settings"
msgstr "Ajustes"

#: includes/class-wp-user-avatar-admin.php:228
#: includes/class-wp-user-avatar-admin.php:229
msgid "Library"
msgstr "Biblioteca"

#: includes/class-wp-user-avatar-admin.php:362
msgid "Mystery Man"
msgstr "Hombre misterioso"

#: includes/class-wp-user-avatar-admin.php:363
msgid "Blank"
msgstr "En blanco"

#: includes/class-wp-user-avatar-admin.php:364
msgid "Gravatar Logo"
msgstr "Logo Gravatar"

#: includes/class-wp-user-avatar-admin.php:365
msgid "Identicon (Generated)"
msgstr "Identicon (generado)"

#: includes/class-wp-user-avatar-admin.php:366
msgid "Wavatar (Generated)"
msgstr "Wavatar (generado)"

#: includes/class-wp-user-avatar-admin.php:367
msgid "MonsterID (Generated)"
msgstr "MonsterID (generado)"

#: includes/class-wp-user-avatar-admin.php:368
msgid "Retro (Generated)"
msgstr "Retro (generado)"

#: includes/class-wp-user-avatar-admin.php:437
#: includes/class-wp-user-avatar.php:256
msgid "Choose Image"
msgstr "Elegir imagen"

#: includes/class-wp-user-avatar-admin.php:437
#: includes/class-wp-user-avatar-admin.php:571
#: includes/wpua-options-page.php:359 includes/wpua-options-page.php:365
msgid "Default Avatar"
msgstr "Avatar por defecto"

#: includes/class-wp-user-avatar-admin.php:438
msgid "Remove"
msgstr "Eliminar"

#: includes/class-wp-user-avatar-admin.php:438
#: includes/class-wp-user-avatar.php:317 includes/wpua-media-page.php:108
msgid "Undo"
msgstr "Deshacer"

#: includes/class-wp-user-avatar-admin.php:491
msgid "https://onedesigns.com/support/forum/plugins/one-user-avatar/"
msgstr "https://onedesigns.com/support/forum/plugins/one-user-avatar/"

#: includes/class-wp-user-avatar-admin.php:492
msgid "Support Forums"
msgstr "Foro de soporte"

#: includes/class-wp-user-avatar-admin.php:506
#: includes/class-wp-user-avatar-admin.php:567 includes/wpua-functions.php:93
#: includes/wpua-functions.php:106 includes/wpua-functions.php:114
#: includes/wpua-functions.php:188 includes/wpua-tinymce-window.php:94
msgid "Profile Picture"
msgstr "Imagen del perfil"

#: includes/class-wp-user-avatar-functions.php:581
#: includes/class-wp-user-avatar-functions.php:810
msgid "Avatar"
msgstr "Avatar"

#. translators: uploaded files
#: includes/class-wp-user-avatar-list-table.php:258
msgctxt "uploaded files"
msgid "All %s"
msgstr "Todos %s"

#. translators: uploaded files
#: includes/class-wp-user-avatar-list-table.php:272
msgctxt "trashed files"
msgid "Trash %s"
msgstr "Papelera %s"

#: includes/class-wp-user-avatar-list-table.php:291
#: includes/class-wp-user-avatar-list-table.php:643
msgid "Restore"
msgstr "Restaurar"

#: includes/class-wp-user-avatar-list-table.php:292
#: includes/class-wp-user-avatar-list-table.php:294
#: includes/class-wp-user-avatar-list-table.php:664
msgid "Delete Permanently"
msgstr "Borrar permanentemente"

#: includes/class-wp-user-avatar-list-table.php:296
msgid "Move to Trash"
msgstr "Mover a la papelera"

#: includes/class-wp-user-avatar-list-table.php:327
msgid "No avatars found."
msgstr "No se encontraron avatares."

#: includes/class-wp-user-avatar-list-table.php:339
msgctxt "column name"
msgid "File"
msgstr "Archivo"

#: includes/class-wp-user-avatar-list-table.php:340
msgid "Author"
msgstr "Autor"

#: includes/class-wp-user-avatar-list-table.php:341
msgctxt "column name"
msgid "Attached to"
msgstr "Adjunto a"

#: includes/class-wp-user-avatar-list-table.php:342
msgctxt "column name"
msgid "Date"
msgstr "Fecha"

#. translators: post title
#: includes/class-wp-user-avatar-list-table.php:419
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/class-wp-user-avatar-list-table.php:525
msgid "Unpublished"
msgstr "Despublicado"

#. translators: time from now
#: includes/class-wp-user-avatar-list-table.php:533
msgctxt "time from now"
msgid "%s from now"
msgstr "%s desde ahora"

#. translators: time ago
#: includes/class-wp-user-avatar-list-table.php:539
msgctxt "time ago"
msgid "%s ago"
msgstr "hace %s"

#: includes/class-wp-user-avatar-list-table.php:544
msgid "Y/m/d"
msgstr "d/m/Y"

#. translators: %d: number of users.
#: includes/class-wp-user-avatar-list-table.php:581
msgctxt "avatar user count"
msgid "+%d more"
msgstr "+%d más"

#: includes/class-wp-user-avatar-list-table.php:595
msgid "(Unattached)"
msgstr "(Sin adjuntar)"

#. translators: %s: Avatar title.
#: includes/class-wp-user-avatar-list-table.php:631
msgid "Edit &#8220;%s&#8221;"
msgstr "Editar &#171;%s&#187;"

#: includes/class-wp-user-avatar-list-table.php:632
msgid "Edit"
msgstr "Editar"

#. translators: %s: Avatar title.
#: includes/class-wp-user-avatar-list-table.php:642
msgid "Restore &#8220;%s&#8221; from the Trash"
msgstr "Restaurar &#171;%s&#187; de la papelera"

#. translators: %s: Avatar title.
#: includes/class-wp-user-avatar-list-table.php:650
msgid "Move &#8220;%s&#8221; to the Trash"
msgstr "Mover &#171;%s&#187; a la papelera"

#: includes/class-wp-user-avatar-list-table.php:651
msgid "Trash"
msgstr "Papelera"

#. translators: %s: Avatar title.
#: includes/class-wp-user-avatar-list-table.php:663
msgid "Delete &#8220;%s&#8221; permanently"
msgstr "Borrar &#171;%s&#187; permanentemente"

#. translators: %s: Avatar title.
#: includes/class-wp-user-avatar-list-table.php:677
msgid "View &#8220;%s&#8221;"
msgstr "Ver &#171;%s&#187;"

#: includes/class-wp-user-avatar-list-table.php:678
msgid "View"
msgstr "Ver"

#. translators: either wp_footer() or wp_print_footer_scripts() hook.
#: includes/class-wp-user-avatar-resource-manager.php:190
msgid ""
"We're sorry, but your theme's page template didn't make a call to %s, which "
"is required by One User Avatar. Please add this call to your page templates."
msgstr ""
"Lo sentimos, pero la plantilla de la página de su tema no hizo una llamada a "
"%s, lo cual es requerido por One User Avatar. Agregue esta llamada a las "
"plantillas de su página."

#: includes/class-wp-user-avatar-shortcode.php:258
msgid "Profile updated."
msgstr "Perfil actualizado."

#: includes/class-wp-user-avatar-shortcode.php:308
msgid "Update Profile"
msgstr "Actualizar el perfil"

#. translators: [avatar_upload] shortcode
#: includes/class-wp-user-avatar-widget.php:27
msgid "Insert %s"
msgstr "Insertar %s"

#: includes/class-wp-user-avatar-widget.php:99
msgid "Title:"
msgstr "Título:"

#: includes/class-wp-user-avatar-widget.php:105
msgid "Description:"
msgstr "Descripción:"

#: includes/class-wp-user-avatar-widget.php:113
msgid "Automatically add paragraphs"
msgstr "Añadir párrafos automáticamente"

#. translators: user display name
#: includes/class-wp-user-avatar.php:252
msgid "Choose Image: %s"
msgstr "Elegir imagen: %s"

#: includes/class-wp-user-avatar.php:268 includes/class-wp-user-avatar.php:269
#: includes/wpua-tinymce-window.php:95 includes/wpua-tinymce-window.php:186
msgid "Upload"
msgstr "Subir"

#. translators: file size in KB
#: includes/class-wp-user-avatar.php:278 includes/wpua-options-page.php:247
msgid "Maximum upload file size: %s."
msgstr "Tamaño máximo de subida: %s."

#. translators: allowed file extensions
#: includes/class-wp-user-avatar.php:288
msgid "Allowed Files: %s"
msgstr "Archivos permitidos: %s"

#: includes/class-wp-user-avatar.php:301 includes/class-wp-user-avatar.php:303
#: includes/wpua-tinymce-window.php:125
msgid "Original Size"
msgstr "Tamaño original"

#: includes/class-wp-user-avatar.php:307 includes/class-wp-user-avatar.php:309
#: includes/wpua-tinymce-window.php:128
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/class-wp-user-avatar.php:313
msgid "Remove Image"
msgstr "Eliminar imagen"

#: includes/class-wp-user-avatar.php:407
msgid "This file is not an image. Please try another."
msgstr "Este archivo no es una imagen. Por favor prueba con otro."

#: includes/class-wp-user-avatar.php:412 includes/class-wp-user-avatar.php:444
msgid "Memory exceeded. Please try another smaller file."
msgstr "Memoria superada. Por favor prueba con un archivo más pequeño."

#. translators: directory path
#: includes/class-wp-user-avatar.php:419
msgid ""
"Unable to create directory %s. Is its parent directory writable by the "
"server?"
msgstr ""
"No fue posible crear el directorio %s. ¿Es el directorio superior editable "
"por el servidor?"

#: includes/wpua-functions.php:109 includes/wpua-functions.php:118
msgid "Image"
msgstr "Imagen"

#: includes/wpua-media-page.php:46
msgid "You do not have permission to upload files."
msgstr "No tienes permisos para subir archivos."

#. translators: %s: Number of media files.
#: includes/wpua-media-page.php:61
msgid "%s avatar permanently deleted."
msgid_plural "%s avatars permanently deleted."
msgstr[0] "Avatar eliminado permanentemente."
msgstr[1] "%d avatares eliminados permanentemente."

#. translators: %s: Number of media files.
#: includes/wpua-media-page.php:82
msgid "%s avatar moved to the Trash."
msgid_plural "%s avatars moved to the Trash."
msgstr[0] "Avatar movido a la papelera."
msgstr[1] "%s avatares movido a la papelera."

#. translators: search query
#: includes/wpua-media-page.php:146
msgid "Search results for %s"
msgstr "Resultados de búsqueda para %s"

#: includes/wpua-media-page.php:170
msgid "Search"
msgstr "Buscar"

#: includes/wpua-options-page.php:143
msgid "Add avatar button to Visual Editor"
msgstr "Añadir botón de avatar al editor visual"

#: includes/wpua-options-page.php:154
msgid "Allow Contributors & Subscribers to upload avatars"
msgstr "Permitir los colaboradores y los suscriptores subir avatares"

#: includes/wpua-options-page.php:165
msgid "Disable Gravatar and use only local avatars"
msgstr "Desactivar Gravatar y utilizar sólo los avatares locales"

#: includes/wpua-options-page.php:177
msgid "Replace the custom avatars functionality in the Ultimate Member plugin"
msgstr ""
"Reemplace la funcionalidad de avatares personalizados en el plugin Ultimate "
"Member"

#: includes/wpua-options-page.php:190
msgid "Always use the browser file uploader to upload avatars"
msgstr ""
"Utilice siempre el cargador de archivos del navegador para cargar avatares"

#: includes/wpua-options-page.php:191
msgid ""
"Check this if another plugin is conflicting with the WordPress Media "
"Uploader."
msgstr ""
"Marque esto si otro plugin está en conflicto con el cargador de medios de "
"WordPress."

#: includes/wpua-options-page.php:219 includes/wpua-options-page.php:228
msgid "Upload Size Limit"
msgstr "Tamaño máximo de subida"

#: includes/wpua-options-page.php:220 includes/wpua-options-page.php:229
msgid "(only for Contributors & Subscribers)"
msgstr "(para colaboradores y suscriptores)"

#. translators: file name
#: includes/wpua-options-page.php:239
msgid "%s exceeds the maximum upload size for this site."
msgstr "%s excede el limite de tamaño máximo para el archivo."

#: includes/wpua-options-page.php:258
msgid "Allow users to edit avatars"
msgstr "Permitir a los usuarios editar su avatar"

#: includes/wpua-options-page.php:266
msgid "Resize avatars on upload"
msgstr "Escalar los avatares al subirlos"

#: includes/wpua-options-page.php:271
msgid "Width"
msgstr "Ancho"

#: includes/wpua-options-page.php:275
msgid "Height"
msgstr "Altura"

#: includes/wpua-options-page.php:283
msgid "Crop avatars to exact dimensions"
msgstr "Recortar los avatares a sus dimensiones exactas"

#: includes/wpua-options-page.php:305 includes/wpua-options-page.php:311
msgid "Avatar Display"
msgstr "Mostrar avatar"

#: includes/wpua-options-page.php:318
msgid "Show Avatars"
msgstr "Mostrar avatares"

#: includes/wpua-options-page.php:325 includes/wpua-options-page.php:331
msgid "Maximum Rating"
msgstr "Clasificación máxima"

#: includes/wpua-options-page.php:337
msgid "G &#8212; Suitable for all audiences"
msgstr "G &#8212; Apto para todo público"

#: includes/wpua-options-page.php:338
msgid "PG &#8212; Possibly offensive, usually for audiences 13 and above"
msgstr "PG &#8212; Posiblemente ofensivo, para audiencias menores a 13 años"

#: includes/wpua-options-page.php:339
msgid "R &#8212; Intended for adult audiences above 17"
msgstr "R &#8212; Para audiencias adultas mayores a 17"

#: includes/wpua-options-page.php:340
msgid "X &#8212; Even more mature than above"
msgstr "X &#8212; Todavía más adulto que el anterior"

#: includes/wpua-options-page.php:369
msgid ""
"For users without a custom avatar of their own, you can either display a "
"generic logo or a generated one based on their e-mail address."
msgstr ""
"Para usuarios sin un avatar personalizado, puedes mostrar un logo genérico o "
"uno generado en base a su dirección de correo electrónico."

#: includes/wpua-tinymce-window.php:101
msgid "User Name"
msgstr "Nombre de usuario"

#: includes/wpua-tinymce-window.php:121 includes/wpua-tinymce-window.php:134
msgid "Size:"
msgstr "Tamaño:"

#: includes/wpua-tinymce-window.php:126
msgid "Large"
msgstr "Grande"

#: includes/wpua-tinymce-window.php:127
msgid "Medium"
msgstr "Medio"

#: includes/wpua-tinymce-window.php:129
msgid "Custom"
msgstr "Costumbre"

#: includes/wpua-tinymce-window.php:140
msgid "Alignment:"
msgstr "Alineación:"

#: includes/wpua-tinymce-window.php:144
msgid "Center"
msgstr "Centro"

#: includes/wpua-tinymce-window.php:145
msgid "Left"
msgstr "Izquierda"

#: includes/wpua-tinymce-window.php:146
msgid "Right"
msgstr "Derecha"

#: includes/wpua-tinymce-window.php:151
msgid "Link To:"
msgstr "Enlace a:"

#: includes/wpua-tinymce-window.php:155
msgid "Image File"
msgstr "Archivo de imagen"

#: includes/wpua-tinymce-window.php:156
msgid "Attachment Page"
msgstr "Página de adjuntos"

#: includes/wpua-tinymce-window.php:157
msgid "Custom URL"
msgstr "URL personalizada"

#: includes/wpua-tinymce-window.php:162
msgid "URL:"
msgstr "URL:"

#: includes/wpua-tinymce-window.php:170
msgid "Open link in a new window"
msgstr "Abrir enlace en ventana nueva"

#: includes/wpua-tinymce-window.php:174
msgid "Caption"
msgstr "Leyenda"

#: includes/wpua-tinymce-window.php:180 includes/wpua-tinymce-window.php:192
msgid "Insert into Post"
msgstr "Insertar en la entrada"

#: includes/wpua-tinymce.php:70
msgid "Insert Avatar"
msgstr "Insertar avatar"

#. translators: placeholder for <a> and </a> tags.
#: one-user-avatar.php:198
msgid ""
"The plugin One User Avatar is a replacement for the old WP User Avatar "
"plugin. Please %1$sdeactivate WP User Avatar%2$s to start using it."
msgstr ""
"El plugin One User Avatar es un reemplazo del antiguo plugin WP User Avatar. "
"%1$sDesactive WP User Avatar%2$s para comenzar a usarlo."

#~ msgctxt "column name"
#~ msgid "Uploaded to"
#~ msgstr "Subido a"
