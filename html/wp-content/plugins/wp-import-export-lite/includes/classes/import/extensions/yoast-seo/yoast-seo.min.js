"use strict";window.wpiePluginSettings=window.wpiePluginSettings||{},window.wpieYoastSettings=window.wpieYoastSettings||{},jQuery(document).on("wpieChangeConfig",function(e,i){wpieYoastSettings.excludeCF=!0,wpieYoastSettings.changeSettings=!0}),jQuery(document).on("wpieAfterChangeTemplate",function(e,i){if("undefined"!=typeof wpieYoastSettings.changeSettings&&wpieYoastSettings.changeSettings===!0){wpieYoastSettings.changeSettings=!1;var t="wpie_item__yoast_wpseo_opengraph-image_url_data";if("undefined"!=typeof i[t]&&null!==i[t])return!0;var p,r,a,n,o="undefined"!=typeof i.wpie_item_cf&&null!==i.wpie_item_cf?i.wpie_item_cf:[];jQuery.each(o,function(e,i){if(p=i.name,r=i.value,-1===jQuery.inArray(p,wpieYoastSettings.cf))return!0;if(a="wpie_item_"+p,n=r,jQuery("."+a).length)if(jQuery("."+a).first().is(".wpie_radio,.wpie_checkbox")){var t=!0;jQuery.each(jQuery("."+a),function(e){"as_specified"!==n&&jQuery(this).val()===n?(jQuery(this).prop("checked",!0),t=!1):jQuery(this).prop("checked",!1)}),t===!0?(jQuery("."+a+"_as_specified").prop("checked",!0).trigger("change"),jQuery("."+a+"_as_specified_data").val(n)):(jQuery("."+a+"_as_specified").prop("checked",!1).trigger("change"),jQuery("."+a+"_as_specified_data").val(""))}else if(jQuery("."+a).first().is(".wpie_content_data_select")){var t=!0;jQuery.each(jQuery("."+a+" option"),function(e){return jQuery(this).attr("value")===n?(t=!1,!1):void 0}),t?(jQuery("."+a).val("as_specified").trigger("chosen:updated").trigger("change"),jQuery("."+a+"_as_specified_wrapper").show(),jQuery("."+a+"_as_specified_data").val(n)):(jQuery("."+a).val(n).trigger("chosen:updated").trigger("change"),jQuery("."+a+"_as_specified_wrapper").hide(),jQuery("."+a+"_as_specified_data").val(""))}else jQuery("."+a).val(n)}),jQuery(".wpie_item__yoast_wpseo_opengraph-image_url").length&&(jQuery(".wpie_item__yoast_wpseo_opengraph-image_url").prop("checked",!0).trigger("change"),jQuery(".wpie_item__yoast_wpseo_opengraph-image_url_data").val("{_yoast_wpseo_opengraphimage[1]}"),jQuery(".wpie_item__yoast_wpseo_twitter-image_url").prop("checked",!0).trigger("change"),jQuery(".wpie_item__yoast_wpseo_twitter-image_url_data").val("{_yoast_wpseo_twitterimage[1]}"))}}),jQuery(document).on("add_exclude_cf_list",".wpie_exclude_cf_list",function(){if("undefined"!=typeof wpieYoastSettings.excludeCF&&wpieYoastSettings.excludeCF===!0){var e=jQuery(this).val(),i=wpieYoastSettings.cf,t=["_yoast_wpseo_opengraph-image-id","_yoast_wpseo_twitter-image-id"];e=e+"||"+i.join("||")+"||"+t.join("||"),jQuery(this).val(e),wpieYoastSettings.excludeCF=!1}});