# Copyright (C) 2016 Leaky Paywall
# This file is distributed under the same license as the Leaky Paywall package.
msgid ""
msgstr ""
"Project-Id-Version: Leaky Paywall 4.1.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/leaky-paywall\n"
"POT-Creation-Date: 2016-11-15 15:35:17+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2016-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#. #-#-#-#-#  issuem-leaky-paywall.pot (Leaky Paywall 4.1.3)  #-#-#-#-#
#. Plugin Name of the plugin/theme
#: class.php:87
msgid "Leaky Paywall"
msgstr ""

#: class.php:89
msgid "Settings"
msgstr ""

#: class.php:91
msgid "Subscribers"
msgstr ""

#: class.php:93
msgid "Update"
msgstr ""

#: class.php:95
msgid "Add-Ons"
msgstr ""

#: class.php:116
msgid "Unauthorize PDF Download"
msgstr ""

#: class.php:118
msgid ""
"You must be <a href=\"%s\">logged in</a> with a valid subscription to "
"download Issue PDFs."
msgstr ""

#: class.php:119 class.php:334
msgid "back to %s"
msgstr ""

#: class.php:332
msgid "Invalid or Expired Login Link"
msgstr ""

#: class.php:333
msgid ""
"Sorry, this login link is invalid or has expired. <a href=\"%s\">Try again?</"
"a>"
msgstr ""

#: class.php:527
msgid ""
"<a href=\"{{SUBSCRIBE_URL}}\">Subscribe</a> or <a href=\"{{LOGIN_URL}}\">log "
"in</a> to read the rest of this content."
msgstr ""

#: class.php:528
msgid ""
"You must <a href=\"{{SUBSCRIBE_URL}}\">upgrade your account</a> to read the "
"rest of this content."
msgstr ""

#: class.php:559
msgid "Magazine Subscription"
msgstr ""

#: class.php:774
msgid "zeen101's Leaky Paywall Settings Updated."
msgstr ""

#: class.php:788
msgid "zeen101's Leaky Paywall Settings"
msgstr ""

#: class.php:796
msgid "Site Wide Options"
msgstr ""

#: class.php:802
msgid "Enable Settings Site Wide?"
msgstr ""

#: class.php:810 class.php:904 class.php:957 class.php:1139 class.php:1179
#: class.php:1258 class.php:1321
msgid "Save Settings"
msgstr ""

#: class.php:825
msgid "General Settings"
msgstr ""

#: class.php:832
msgid "Page for Log In"
msgstr ""

#: class.php:834 class.php:842 class.php:850 class.php:858 class.php:896
msgid "&mdash; Select &mdash;"
msgstr ""

#: class.php:835
msgid "Add this shortcode to your Log In page: %s"
msgstr ""

#: class.php:840
msgid "Page for Subscribe Cards"
msgstr ""

#: class.php:843
msgid "Add this shortcode to your Subscription page: %s"
msgstr ""

#: class.php:848
msgid "Page for Register Form"
msgstr ""

#: class.php:851
msgid "Add this shortcode to your register page: %s"
msgstr ""

#: class.php:856
msgid "Page for Profile"
msgstr ""

#: class.php:859
msgid ""
"Add this shortcode to your Profile page: %s. This page displays the account "
"information for subscribers."
msgstr ""

#: class.php:864
msgid "Subscribe or Login Message"
msgstr ""

#: class.php:868
msgid ""
"Available replacement variables: {{SUBSCRIBE_LOGIN_URL}} {{SUBSCRIBE_URL}}  "
"{{LOGIN_URL}}"
msgstr ""

#: class.php:874
msgid "Upgrade Message"
msgstr ""

#: class.php:878
msgid ""
"Available replacement variables: {{SUBSCRIBE_LOGIN_URL}} {{SUBSCRIBE_URL}}"
msgstr ""

#: class.php:884
msgid "CSS Style"
msgstr ""

#: class.php:887
msgid "Default"
msgstr ""

#: class.php:888
msgid "None"
msgstr ""

#: class.php:894
msgid "After Subscribe Page"
msgstr ""

#: class.php:897
msgid "Page to redirect to after a user subscribes"
msgstr ""

#: class.php:915
msgid "Email Settings"
msgstr ""

#: class.php:922
msgid "Site Name"
msgstr ""

#: class.php:927
msgid "From Name"
msgstr ""

#: class.php:932
msgid "From Email"
msgstr ""

#: class.php:936
msgid "New Subscriber Email"
msgstr ""

#: class.php:939
msgid "Subject"
msgstr ""

#: class.php:941
msgid "The subject line for the email sent to new subscribers."
msgstr ""

#: class.php:946
msgid "Body"
msgstr ""

#: class.php:948
msgid "The email message that is sent to new subscribers."
msgstr ""

#: class.php:949
msgid "Available template tags:"
msgstr ""

#: class.php:968
msgid "Payment Gateway Settings"
msgstr ""

#: class.php:975
msgid "Test Mode?"
msgstr ""

#: class.php:986
msgid "Enabled Gateways"
msgstr ""

#: class.php:1013
msgid "Stripe Settings"
msgstr ""

#: class.php:1016
msgid "Live Secret Key"
msgstr ""

#: class.php:1021
msgid "Live Publishable Key"
msgstr ""

#: class.php:1026
msgid "Live Webhooks"
msgstr ""

#: class.php:1031
msgid "Test Secret Key"
msgstr ""

#: class.php:1036
msgid "Test Publishable Key"
msgstr ""

#: class.php:1041
msgid "Test Webhooks"
msgstr ""

#: class.php:1055
msgid "PayPal Standard Settings"
msgstr ""

#: class.php:1058
msgid "Merchant ID"
msgstr ""

#: class.php:1061
msgid "Use PayPal Email Address in lieu of Merchant ID"
msgstr ""

#: class.php:1066
msgid "API Username"
msgstr ""

#: class.php:1069 class.php:1104
msgid ""
"At PayPal, see: Profile &rarr; My Selling Tools &rarr; API Access &rarr; "
"Update &rarr; View API Signature (or Request API Credentials)."
msgstr ""

#: class.php:1074
msgid "API Password"
msgstr ""

#: class.php:1081
msgid "API Signature"
msgstr ""

#: class.php:1088
msgid "Live IPN"
msgstr ""

#: class.php:1093
msgid "Sandbox Merchant ID"
msgstr ""

#: class.php:1096
msgid "Use PayPal Sandbox Email Address in lieu of Merchant ID"
msgstr ""

#: class.php:1101
msgid "Sandbox API Username"
msgstr ""

#: class.php:1109
msgid "Sandbox API Password"
msgstr ""

#: class.php:1116
msgid "Sandbox API Signature"
msgstr ""

#: class.php:1123
msgid "Sandbox IPN"
msgstr ""

#: class.php:1152
msgid "Currency Options"
msgstr ""

#: class.php:1159
msgid "Currency"
msgstr ""

#: class.php:1169
msgid "This controls which currency payment gateways will take payments in."
msgstr ""

#: class.php:1192
msgid "Content Restriction"
msgstr ""

#: class.php:1199
msgid "Limited Article Cookie Expiration"
msgstr ""

#: class.php:1203
msgid "Hour(s)"
msgstr ""

#: class.php:1204 functions.php:1426
msgid "Day(s)"
msgstr ""

#: class.php:1205 functions.php:1427
msgid "Week(s)"
msgstr ""

#: class.php:1206 functions.php:1428
msgid "Month(s)"
msgstr ""

#: class.php:1207 functions.php:1429
msgid "Year(s)"
msgstr ""

#: class.php:1209
msgid ""
"Choose length of time when a visitor can once again read your articles/posts "
"(up to the # of articles allowed)."
msgstr ""

#: class.php:1214
msgid "Restrict PDF Downloads?"
msgstr ""

#: class.php:1220
msgid "Restrictions"
msgstr ""

#: class.php:1248
msgid "By default all content is allowed."
msgstr ""

#: class.php:1250
msgid "Add New Restricted Content"
msgstr ""

#: class.php:1269
msgid "Subscription Levels"
msgstr ""

#: class.php:1370 subscriber-table.php:133
msgid "Leaky Paywall Subscribers"
msgstr ""

#: class.php:1375 class.php:1417
msgid ""
"Unable to verify security token. Subscriber not added. Please try again."
msgstr ""

#: class.php:1395
msgid "Manual Addition"
msgstr ""

#: class.php:1410 class.php:1478
msgid "You must include a valid email address."
msgstr ""

#: class.php:1522 class.php:1572
msgid "Username (required)"
msgstr ""

#: class.php:1523 class.php:1573
msgid "Email Address (required)"
msgstr ""

#: class.php:1524 class.php:1574
msgid "Price Paid"
msgstr ""

#: class.php:1526 class.php:1576 subscriber-table.php:102
msgid "Expires"
msgstr ""

#: class.php:1530 class.php:1580
msgid "Subscription Level"
msgstr ""

#: class.php:1540 class.php:1590 shortcodes.php:233 subscriber-table.php:104
msgid "Status"
msgstr ""

#: class.php:1542 class.php:1592
msgid "Active"
msgstr ""

#: class.php:1543 class.php:1593
msgid "Canceled"
msgstr ""

#: class.php:1544 class.php:1594
msgid "Deactivated"
msgstr ""

#: class.php:1548 class.php:1598 shortcodes.php:235
msgid "Payment Method"
msgstr ""

#: class.php:1559 class.php:1606 subscriber-table.php:98
msgid "Subscriber ID"
msgstr ""

#: class.php:1565
msgid "Cancel"
msgstr ""

#: class.php:1600 functions.php:1915
msgid "Manual"
msgstr ""

#: class.php:1601 functions.php:1916
msgid "Stripe"
msgstr ""

#: class.php:1602 include/gateways/class-leaky-paywall-payment-gateways.php:58
msgid "PayPal"
msgstr ""

#: class.php:1627
msgid "Search Subscribers"
msgstr ""

#: class.php:1650
msgid "Leaky Paywall Add-Ons"
msgstr ""

#: class.php:1651
msgid ""
"The following are available add-ons to extend Leaky Paywall functionality."
msgstr ""

#: class.php:1752
msgid "Leaky Paywall Updater"
msgstr ""

#: class.php:1810
msgid "Version 2.0.0 Update Process"
msgstr ""

#: class.php:1811
msgid ""
"We have decided to use the WordPress Users table to instead of maintaining "
"our own subscribers table. This process will copy all existing leaky paywall "
"subscriber data to individual WordPress users."
msgstr ""

#: class.php:1821
msgid "Copying user data for %s (%s mode user)..."
msgstr ""

#: class.php:1861
msgid "completed."
msgstr ""

#: class.php:1863
msgid "skipping."
msgstr ""

#: class.php:1873
msgid "Finished Migrating Subscribers!"
msgstr ""

#: class.php:1874
msgid "Updating Settings..."
msgstr ""

#: class.php:1922
msgid "All Done!"
msgstr ""

#: class.php:1928
msgid ""
"If your browser doesn&#8217;t start loading the next page automatically, "
"click this link:"
msgstr ""

#: class.php:1928
msgid "Next Subscribers"
msgstr ""

#: class.php:1971
msgid ""
"You must update the Leaky Paywall Database to version 2 to continue using "
"this plugin... %s"
msgstr ""

#: class.php:1971
msgid "Update Now"
msgstr ""

#: class.php:1987
msgid ""
"You must complete your PayPal setup to continue using the Leaky Paywall "
"Plugin. %s."
msgstr ""

#: class.php:1987
msgid "Complete Your Setup Now"
msgstr ""

#: deprecated.php:87 deprecated.php:589
msgid "Requested subscription level is not free"
msgstr ""

#: deprecated.php:90 deprecated.php:592
msgid "Not a valid subscription level"
msgstr ""

#: deprecated.php:95 include/registration-functions.php:263
msgid "Username already taken"
msgstr ""

#: deprecated.php:99 include/registration-functions.php:241
msgid "Invalid username"
msgstr ""

#: deprecated.php:103 include/registration-functions.php:268
msgid "Please enter a username"
msgstr ""

#: deprecated.php:107 include/registration-functions.php:236
msgid "Invalid email"
msgstr ""

#: deprecated.php:111 include/registration-functions.php:258
msgid "Email already registered"
msgstr ""

#: deprecated.php:115 include/registration-functions.php:246
msgid "Please enter a password"
msgstr ""

#: deprecated.php:119 include/registration-functions.php:251
msgid "Passwords do not match"
msgstr ""

#: deprecated.php:274
#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:96
msgid "Unable to find valid Stripe customer ID."
msgstr ""

#: deprecated.php:371 deprecated.php:519 deprecated.php:1010 functions.php:439
#: functions.php:908
#: include/gateways/class-leaky-paywall-payment-gateway-paypal.php:192
#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:173
#: include/gateways/stripe/functions.php:83
msgid "Error processing request: %s"
msgstr ""

#: deprecated.php:427
#: include/gateways/class-leaky-paywall-payment-gateway-paypal.php:97
msgid "Error: PayPal denied this payment."
msgstr ""

#: deprecated.php:430
#: include/gateways/class-leaky-paywall-payment-gateway-paypal.php:100
msgid "Error: Payment failed."
msgstr ""

#: deprecated.php:470
#: include/gateways/class-leaky-paywall-payment-gateway-paypal.php:141
msgid "Error: Transaction IDs do not match! %s, %s"
msgstr ""

#: deprecated.php:473
#: include/gateways/class-leaky-paywall-payment-gateway-paypal.php:144
msgid ""
"Error: Amount charged is not the same as the subscription total! %s | %s"
msgstr ""

#: deprecated.php:548
#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:57
#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:221
msgid "Error"
msgstr ""

#: deprecated.php:553
msgid "Register for %s"
msgstr ""

#: deprecated.php:557 shortcodes.php:365 shortcodes.php:640
msgid "Username"
msgstr ""

#: deprecated.php:561 shortcodes.php:375
msgid "Email"
msgstr ""

#: deprecated.php:565 shortcodes.php:619
msgid "First Name"
msgstr ""

#: deprecated.php:569 shortcodes.php:624
msgid "Last Name"
msgstr ""

#: deprecated.php:573 shortcodes.php:645
msgid "Password"
msgstr ""

#: deprecated.php:577
msgid "Password Again"
msgstr ""

#: deprecated.php:584
msgid "Register Now"
msgstr ""

#: deprecated.php:846
#: include/gateways/class-leaky-paywall-payment-gateway-paypal.php:452
msgid "Invalid IPN sent from PayPal: %s"
msgstr ""

#: functions.php:814
msgid "Manually Added"
msgstr ""

#: functions.php:853
msgid "No payment gateway defined."
msgstr ""

#: functions.php:859
msgid "No subscriber ID defined."
msgstr ""

#: functions.php:864
msgid "Cancel Subscription"
msgstr ""

#: functions.php:866
msgid ""
"Cancellations take effect at the end of your billing cycle, and we can’t "
"give partial refunds for unused time in the billing cycle. If you still wish "
"to cancel now, you may proceed, or you can come back later."
msgstr ""

#: functions.php:867
msgid ""
" Thank you for the time you’ve spent subscribed to %s. We hope you’ll return "
"someday. "
msgstr ""

#: functions.php:868
msgid "Yes, cancel my subscription!"
msgstr ""

#: functions.php:868
msgid "No, get me outta here!"
msgstr ""

#: functions.php:884 shortcodes.php:417
msgid ""
"Unable to find valid Stripe customer ID to unsubscribe. Please contact "
"support"
msgstr ""

#: functions.php:895
msgid ""
"Your subscription has been successfully canceled. You will continue to have "
"access to %s until the end of your billing cycle. Thank you for the time you "
"have spent subscribed to our site and we hope you will return soon!"
msgstr ""

#: functions.php:900
msgid ""
"ERROR: An error occured when trying to unsubscribe you from your account, "
"please try again. If you continue to have trouble, please contact us. Thank "
"you."
msgstr ""

#: functions.php:904
msgid "Return to %s..."
msgstr ""

#: functions.php:916
msgid ""
"You must cancel your account through PayPal. Please click this unsubscribe "
"button to complete the cancellation process."
msgstr ""

#: functions.php:921
msgid ""
"Unable to determine your payment method. Please contact support for help "
"canceling your account."
msgstr ""

#: functions.php:929
msgid "You must be logged in to cancel your account."
msgstr ""

#: functions.php:1318 functions.php:1326
msgid "Error Downloading PDF"
msgstr ""

#: functions.php:1320
msgid "Download Error: Invalid response: %s"
msgstr ""

#: functions.php:1321 functions.php:1329
msgid "Home"
msgstr ""

#: functions.php:1328
msgid "Download Error: %s"
msgstr ""

#: functions.php:1383
msgid "Subscription Name"
msgstr ""

#: functions.php:1384
msgid "Subscription ID: %s"
msgstr ""

#: functions.php:1394
msgid "Recurring?"
msgstr ""

#: functions.php:1401
msgid "Subscription Price"
msgstr ""

#: functions.php:1404
msgid "0 for Free Subscriptions"
msgstr ""

#: functions.php:1409
msgid "Subscription Length"
msgstr ""

#: functions.php:1413
msgid "Forever"
msgstr ""

#: functions.php:1414
msgid "Limited for..."
msgstr ""

#: functions.php:1436
msgid "Access Options"
msgstr ""

#: functions.php:1454
msgid "Add New Post Type"
msgstr ""

#: functions.php:1456
msgid ""
"Post Types that are not native the to the site currently being viewed are "
"marked with an asterisk."
msgstr ""

#: functions.php:1464
msgid "Site"
msgstr ""

#: functions.php:1469
msgid "All Sites"
msgstr ""

#: functions.php:1535
msgid "Unlimited"
msgstr ""

#: functions.php:1536
msgid "Limit to..."
msgstr ""

#: functions.php:1546
msgid "#"
msgstr ""

#: functions.php:1619
msgid "Number of"
msgstr ""

#: functions.php:1631
msgid "allowed:"
msgstr ""

#: functions.php:1713
msgid "Subscription Options"
msgstr ""

#: functions.php:1754
msgid "Access %s %s*"
msgstr ""

#: functions.php:1756
msgid "Unlimited %s"
msgstr ""

#: functions.php:1771
msgid "%s%s %s (recurring)"
msgstr ""

#: functions.php:1774
msgid "%s%s %s"
msgstr ""

#: functions.php:1779
msgid "Free for the first %s day(s)"
msgstr ""

#: functions.php:1782
msgid "Free"
msgstr ""

#: functions.php:1795
msgid "Current Subscription"
msgstr ""

#: functions.php:1905
msgid "Lost Password?"
msgstr ""

#: functions.php:1917
#: include/gateways/class-leaky-paywall-payment-gateways.php:59
#: include/gateways/class-leaky-paywall-payment-gateways.php:105
msgid "PayPal Standard"
msgstr ""

#: functions.php:1918
msgid "Free Registration"
msgstr ""

#: functions.php:1929
msgid "for life"
msgstr ""

#: functions.php:1936
msgid "day"
msgstr ""

#: functions.php:1939
msgid "days"
msgstr ""

#: functions.php:1942
msgid "week"
msgstr ""

#: functions.php:1945
msgid "weeks"
msgstr ""

#: functions.php:1948
msgid "month"
msgstr ""

#: functions.php:1951
msgid "months"
msgstr ""

#: functions.php:1954
msgid "year"
msgstr ""

#: functions.php:1957
msgid "years"
msgstr ""

#: functions.php:1965 functions.php:1967
msgid "every"
msgstr ""

#: functions.php:2031
msgid "New subscription on %s"
msgstr ""

#: functions.php:2079
msgid "UAE dirham"
msgstr ""

#: functions.php:2080
msgid "Afghan afghani"
msgstr ""

#: functions.php:2081
msgid "Albanian lek"
msgstr ""

#: functions.php:2082
msgid "Armenian dram"
msgstr ""

#: functions.php:2083
msgid "Netherlands Antillean gulden"
msgstr ""

#: functions.php:2084
msgid "Angolan kwanza"
msgstr ""

#: functions.php:2085
msgid "Argentine peso"
msgstr ""

#: functions.php:2086
msgid "Australian dollar"
msgstr ""

#: functions.php:2087
msgid "Aruban florin"
msgstr ""

#: functions.php:2088
msgid "Azerbaijani manat"
msgstr ""

#: functions.php:2089
msgid "Bosnia and Herzegovina konvertibilna marka"
msgstr ""

#: functions.php:2090
msgid "Barbadian dollar"
msgstr ""

#: functions.php:2091
msgid "Bangladeshi taka"
msgstr ""

#: functions.php:2092
msgid "Bulgarian lev"
msgstr ""

#: functions.php:2093
msgid "Burundi franc"
msgstr ""

#: functions.php:2094
msgid "Bermudian dollar"
msgstr ""

#: functions.php:2095
msgid "Brunei dollar"
msgstr ""

#: functions.php:2096
msgid "Bolivian boliviano"
msgstr ""

#: functions.php:2097
msgid "Brazilian real"
msgstr ""

#: functions.php:2098
msgid "Bahamian dollar"
msgstr ""

#: functions.php:2099
msgid "Botswana pula"
msgstr ""

#: functions.php:2100
msgid "Belize dollar"
msgstr ""

#: functions.php:2101
msgid "Canadian dollar"
msgstr ""

#: functions.php:2102
msgid "Congolese franc"
msgstr ""

#: functions.php:2103
msgid "Swiss franc"
msgstr ""

#: functions.php:2104
msgid "Chilean peso"
msgstr ""

#: functions.php:2105
msgid "Chinese Yuan Renminbi"
msgstr ""

#: functions.php:2106
msgid "Colombian peso"
msgstr ""

#: functions.php:2107
msgid "Costa Rican colon"
msgstr ""

#: functions.php:2108
msgid "Cape Verdean escudo"
msgstr ""

#: functions.php:2109
msgid "Czech koruna"
msgstr ""

#: functions.php:2110
msgid "Djiboutian franc"
msgstr ""

#: functions.php:2111
msgid "Danish krone"
msgstr ""

#: functions.php:2112
msgid "Dominican peso"
msgstr ""

#: functions.php:2113
msgid "Algerian dinar"
msgstr ""

#: functions.php:2114
msgid "Estonian kroon"
msgstr ""

#: functions.php:2115
msgid "Egyptian pound"
msgstr ""

#: functions.php:2116
msgid "Ethiopian birr"
msgstr ""

#: functions.php:2117
msgid "European Euro"
msgstr ""

#: functions.php:2118
msgid "Fijian dollar"
msgstr ""

#: functions.php:2119
msgid "Falkland Islands pound"
msgstr ""

#: functions.php:2120
msgid "British pound"
msgstr ""

#: functions.php:2121
msgid "Georgian lari"
msgstr ""

#: functions.php:2122
msgid "Gibraltar pound"
msgstr ""

#: functions.php:2123
msgid "Gambian dalasi"
msgstr ""

#: functions.php:2124
msgid "Guinean franc"
msgstr ""

#: functions.php:2125
msgid "Guatemalan quetzal"
msgstr ""

#: functions.php:2126
msgid "Guyanese dollar"
msgstr ""

#: functions.php:2127
msgid "Hong Kong dollar"
msgstr ""

#: functions.php:2128
msgid "Honduran lempira"
msgstr ""

#: functions.php:2129
msgid "Croatian kuna"
msgstr ""

#: functions.php:2130
msgid "Haitian gourde"
msgstr ""

#: functions.php:2131
msgid "Hungarian forint"
msgstr ""

#: functions.php:2132
msgid "Indonesian rupiah"
msgstr ""

#: functions.php:2133
msgid "Israeli new sheqel"
msgstr ""

#: functions.php:2134
msgid "Indian rupee"
msgstr ""

#: functions.php:2135
msgid "Icelandic króna"
msgstr ""

#: functions.php:2136
msgid "Jamaican dollar"
msgstr ""

#: functions.php:2137
msgid "Japanese yen"
msgstr ""

#: functions.php:2138
msgid "Kenyan shilling"
msgstr ""

#: functions.php:2139
msgid "Kyrgyzstani som"
msgstr ""

#: functions.php:2140
msgid "Cambodian riel"
msgstr ""

#: functions.php:2141
msgid "Comorian franc"
msgstr ""

#: functions.php:2142
msgid "South Korean won"
msgstr ""

#: functions.php:2143
msgid "Cayman Islands dollar"
msgstr ""

#: functions.php:2144
msgid "Kazakhstani tenge"
msgstr ""

#: functions.php:2145
msgid "Lao kip"
msgstr ""

#: functions.php:2146
msgid "Lebanese lira"
msgstr ""

#: functions.php:2147
msgid "Sri Lankan rupee"
msgstr ""

#: functions.php:2148
msgid "Liberian dollar"
msgstr ""

#: functions.php:2149
msgid "Lesotho loti"
msgstr ""

#: functions.php:2150
msgid "Lithuanian litas"
msgstr ""

#: functions.php:2151
msgid "Latvian lats"
msgstr ""

#: functions.php:2152
msgid "Moroccan dirham"
msgstr ""

#: functions.php:2153
msgid "Moldovan leu"
msgstr ""

#: functions.php:2154
msgid "Malagasy ariary"
msgstr ""

#: functions.php:2155
msgid "Macedonian denar"
msgstr ""

#: functions.php:2156
msgid "Mongolian tugrik"
msgstr ""

#: functions.php:2157
msgid "Macanese pataca"
msgstr ""

#: functions.php:2158
msgid "Mauritanian ouguiya"
msgstr ""

#: functions.php:2159
msgid "Mauritian rupee"
msgstr ""

#: functions.php:2160
msgid "Maldivian rufiyaa"
msgstr ""

#: functions.php:2161
msgid "Malawian kwacha"
msgstr ""

#: functions.php:2162
msgid "Mexican peso"
msgstr ""

#: functions.php:2163
msgid "Malaysian ringgit"
msgstr ""

#: functions.php:2164
msgid "Mozambique Metical"
msgstr ""

#: functions.php:2165
msgid "Namibian dollar"
msgstr ""

#: functions.php:2166
msgid "Nigerian naira"
msgstr ""

#: functions.php:2167
msgid "Nicaraguan Córdoba"
msgstr ""

#: functions.php:2168
msgid "Norwegian krone"
msgstr ""

#: functions.php:2169
msgid "Nepalese rupee"
msgstr ""

#: functions.php:2170
msgid "New Zealand dollar"
msgstr ""

#: functions.php:2171
msgid "Panamanian balboa"
msgstr ""

#: functions.php:2172
msgid "Peruvian nuevo sol"
msgstr ""

#: functions.php:2173
msgid "Papua New Guinean kina"
msgstr ""

#: functions.php:2174
msgid "Philippine peso"
msgstr ""

#: functions.php:2175
msgid "Pakistani rupee"
msgstr ""

#: functions.php:2176
msgid "Polish zloty"
msgstr ""

#: functions.php:2177
msgid "Paraguayan guarani"
msgstr ""

#: functions.php:2178
msgid "Qatari riyal"
msgstr ""

#: functions.php:2179
msgid "Romanian leu"
msgstr ""

#: functions.php:2180
msgid "Serbian dinar"
msgstr ""

#: functions.php:2181
msgid "Russian ruble"
msgstr ""

#: functions.php:2183
msgid "Saudi riyal"
msgstr ""

#: functions.php:2184
msgid "Solomon Islands dollar"
msgstr ""

#: functions.php:2185
msgid "Seychellois rupee"
msgstr ""

#: functions.php:2186
msgid "Swedish krona"
msgstr ""

#: functions.php:2187
msgid "Singapore dollar"
msgstr ""

#: functions.php:2188
msgid "Saint Helena pound"
msgstr ""

#: functions.php:2189
msgid "Sierra Leonean leone"
msgstr ""

#: functions.php:2190
msgid "Somali shilling"
msgstr ""

#: functions.php:2191
msgid "Surinamese dollar"
msgstr ""

#: functions.php:2192
msgid "São Tomé and Príncipe Dobra"
msgstr ""

#: functions.php:2193
msgid "El Salvador Colon"
msgstr ""

#: functions.php:2194
msgid "Swazi lilangeni"
msgstr ""

#: functions.php:2195
msgid "Thai baht"
msgstr ""

#: functions.php:2196
msgid "Tajikistani somoni"
msgstr ""

#: functions.php:2197
msgid "Tonga Pa'anga"
msgstr ""

#: functions.php:2198
msgid "Turkish new lira"
msgstr ""

#: functions.php:2199
msgid "Trinidad and Tobago dollar"
msgstr ""

#: functions.php:2200
msgid "New Taiwan dollar"
msgstr ""

#: functions.php:2201
msgid "Tanzanian shilling"
msgstr ""

#: functions.php:2202
msgid "Ukrainian hryvnia"
msgstr ""

#: functions.php:2203
msgid "Ugandan shilling"
msgstr ""

#: functions.php:2204
msgid "United States dollar"
msgstr ""

#: functions.php:2205
msgid "Uruguayan peso"
msgstr ""

#: functions.php:2206
msgid "Uzbekistani som"
msgstr ""

#: functions.php:2207
msgid "Vietnamese dong"
msgstr ""

#: functions.php:2208
msgid "Vanuatu vatu"
msgstr ""

#: functions.php:2209
msgid "Samoan tala"
msgstr ""

#: functions.php:2210
msgid "Central African CFA franc"
msgstr ""

#: functions.php:2211
msgid "East Caribbean dollar"
msgstr ""

#: functions.php:2212
msgid "West African CFA franc"
msgstr ""

#: functions.php:2213
msgid "CFP franc"
msgstr ""

#: functions.php:2214
msgid "Yemeni rial"
msgstr ""

#: functions.php:2215
msgid "South African rand"
msgstr ""

#: functions.php:2216
msgid "Zambian kwacha"
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:57
msgid ""
"Missing Stripe token, please try again or contact support if the issue "
"persists."
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:221
msgid "An error occurred, please contact the site administrator: "
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateway-stripe.php:427
msgid "The card number you entered is invalid"
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateways.php:60
msgid "Leaky_Paywall_Payment_Gateway_PayPal"
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateways.php:63
msgid "Credit / Debit Card"
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateways.php:64
msgid "Stripe Credit / Debit Card Form"
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateways.php:68
msgid "Stripe Checkout"
msgstr ""

#: include/gateways/class-leaky-paywall-payment-gateways.php:69
msgid "Stripe Checkout Popup"
msgstr ""

#: include/registration-functions.php:226
msgid "Please enter your first name"
msgstr ""

#: include/registration-functions.php:231
msgid "Please enter your last name"
msgstr ""

#: include/registration-functions.php:302
msgid "Payment Information"
msgstr ""

#: include/registration-functions.php:305
#: include/registration-functions.php:616
msgid "Name on Card"
msgstr ""

#: include/registration-functions.php:310
#: include/registration-functions.php:341 shortcodes.php:479
msgid "Card Number"
msgstr ""

#: include/registration-functions.php:315
msgid "CVC"
msgstr ""

#: include/registration-functions.php:320
msgid "Card Zip or Postal Code"
msgstr ""

#: include/registration-functions.php:325
#: include/registration-functions.php:620
msgid "Expiration (MM/YYYY)"
msgstr ""

#: include/registration-functions.php:345
msgid "Card CVC"
msgstr ""

#: include/registration-functions.php:349
msgid "Address"
msgstr ""

#: include/registration-functions.php:353
msgid "City"
msgstr ""

#: include/registration-functions.php:357
msgid "State or Providence"
msgstr ""

#: include/registration-functions.php:361
msgid "Country"
msgstr ""

#: include/registration-functions.php:612
msgid "Card ZIP or Postal Code"
msgstr ""

#: metaboxes.php:21
msgid "Leaky Paywall Visibility"
msgstr ""

#: metaboxes.php:46
msgid "This %s should..."
msgstr ""

#: metaboxes.php:49
msgid "obey Leaky Paywall's defaults."
msgstr ""

#: metaboxes.php:50
msgid "only be visible to..."
msgstr ""

#: metaboxes.php:51
msgid "always be visible to..."
msgstr ""

#: metaboxes.php:52
msgid "only and always be visible to..."
msgstr ""

#: metaboxes.php:75
msgid "Everyone"
msgstr ""

#: metaboxes.php:95
msgid "Hint:"
msgstr ""

#: metaboxes.php:96
msgid ""
"\"Only\" means that only the selected subscription levels can see this %s, "
"if they have not reached their %s limit."
msgstr ""

#: metaboxes.php:97
msgid ""
"\"Always\" means that the selected subscription levels can see this %s, even "
"if they have reached their %s limit."
msgstr ""

#: metaboxes.php:98
msgid ""
"\"Only and Always\" means that only the selected subscription levels can see "
"this %s, even if they have reached their %s limit."
msgstr ""

#: shortcodes.php:20
msgid "Email address:"
msgstr ""

#: shortcodes.php:21
msgid "Check your email for a link to log in."
msgstr ""

#: shortcodes.php:22
msgid "Email sent. Please check your email for the login link."
msgstr ""

#: shortcodes.php:23
msgid "Error sending login email, please try again later."
msgstr ""

#: shortcodes.php:24
msgid "Please supply a valid email address."
msgstr ""

#: shortcodes.php:56
msgid "Send Login Email"
msgstr ""

#: shortcodes.php:102
msgid "Enter your email address to start your subscription:"
msgstr ""

#: shortcodes.php:103
msgid "Check your email for a link to start your subscription."
msgstr ""

#: shortcodes.php:146
msgid ""
"Your subscription will automatically renew until you <a href=\"%s\">cancel</"
"a>"
msgstr ""

#: shortcodes.php:150
msgid "You are a lifetime subscriber!"
msgstr ""

#: shortcodes.php:154
msgid ""
"Your subscription has been canceled. You will continue to have access to %s "
"until the end of your billing cycle. Thank you for the time you have spent "
"subscribed to our site and we hope you will return soon!"
msgstr ""

#: shortcodes.php:158
msgid "You are subscribed via %s until %s."
msgstr ""

#: shortcodes.php:164
msgid "Log Out"
msgstr ""

#: shortcodes.php:223
msgid ""
"<p>Welcome %s, you are currently logged in. <a href=\"%s\">Click here to log "
"out.</a></p>"
msgstr ""

#: shortcodes.php:226 shortcodes.php:587
msgid "Your Subscription"
msgstr ""

#: shortcodes.php:234
msgid "Type"
msgstr ""

#: shortcodes.php:236
msgid "Expiration"
msgstr ""

#: shortcodes.php:237
msgid "Cancel?"
msgstr ""

#: shortcodes.php:247 subscriber-table.php:218
msgid "Undefined"
msgstr ""

#: shortcodes.php:257 subscriber-table.php:285
msgid "Never"
msgstr ""

#: shortcodes.php:265
msgid "Recurs on %s"
msgstr ""

#: shortcodes.php:273
msgid "<a href=\"%s\">cancel</a>"
msgstr ""

#: shortcodes.php:297
msgid "Your Mobile Devices"
msgstr ""

#: shortcodes.php:298
msgid ""
"To generate a token for the mobile app, click the \"Add New Mobile Device\" "
"button below."
msgstr ""

#: shortcodes.php:306
msgid "Your Profile"
msgstr ""

#: shortcodes.php:332
msgid "Invalid email address."
msgstr ""

#: shortcodes.php:340
msgid "Passwords do not match."
msgstr ""

#: shortcodes.php:350
msgid "Profile Changes Saved."
msgstr ""

#: shortcodes.php:370
msgid "Display Name"
msgstr ""

#: shortcodes.php:381
msgid "New Password"
msgstr ""

#: shortcodes.php:386
msgid "New Password (again)"
msgstr ""

#: shortcodes.php:392
msgid "Update Profile Information"
msgstr ""

#: shortcodes.php:420
msgid "Credit Card Number Required"
msgstr ""

#: shortcodes.php:423
msgid "Credit Card Expiration Month Required"
msgstr ""

#: shortcodes.php:426
msgid "Credit Card Expiration Year Required"
msgstr ""

#: shortcodes.php:429
msgid "Credit Card Security Code (CVC) Required"
msgstr ""

#: shortcodes.php:432
msgid "Credit Card Cardholder's Name Required"
msgstr ""

#: shortcodes.php:449
msgid "Your credit card has been successfully updated."
msgstr ""

#: shortcodes.php:453
msgid "Error updating Credit Card information: %s"
msgstr ""

#: shortcodes.php:475
msgid "Update Credit Card"
msgstr ""

#: shortcodes.php:484
msgid "Expiration Date"
msgstr ""

#: shortcodes.php:491
msgid "Security Code (CVC)"
msgstr ""

#: shortcodes.php:496
msgid "Cardholder's Name"
msgstr ""

#: shortcodes.php:502
msgid "Update Credit Card Information"
msgstr ""

#: shortcodes.php:510
msgid "You can update your payment details through PayPal's website."
msgstr ""

#: shortcodes.php:516
msgid "Your Payment Information"
msgstr ""

#: shortcodes.php:524
msgid "Your Account is Not Currently Active"
msgstr ""

#: shortcodes.php:525
msgid ""
"To reactivate your account, please visit our <a href=\"%s\">Subscription "
"page</a>."
msgstr ""

#: shortcodes.php:590
msgid "Subscription Name:"
msgstr ""

#: shortcodes.php:591
msgid "Subscription Length:"
msgstr ""

#: shortcodes.php:592
msgid "Recurring:"
msgstr ""

#: shortcodes.php:593
msgid "Content Access:"
msgstr ""

#: shortcodes.php:608
msgid "Total:"
msgstr ""

#: shortcodes.php:616
msgid "Your Details"
msgstr ""

#: shortcodes.php:629
msgid "Email Address"
msgstr ""

#: shortcodes.php:637
msgid "Account Details"
msgstr ""

#: shortcodes.php:650
msgid "Confirm Password"
msgstr ""

#: shortcodes.php:688
msgid "Subscribe"
msgstr ""

#: subscriber-table.php:90
msgid "No Leaky Paywall subscribers found."
msgstr ""

#: subscriber-table.php:95
msgid "WordPress Username"
msgstr ""

#: subscriber-table.php:96
msgid "E-mail"
msgstr ""

#: subscriber-table.php:97
msgid "Level ID"
msgstr ""

#: subscriber-table.php:99
msgid "Price"
msgstr ""

#: subscriber-table.php:100
msgid "Plan"
msgstr ""

#: subscriber-table.php:101
msgid "Created"
msgstr ""

#: subscriber-table.php:103
msgid "Gateway"
msgstr ""

#: subscriber-table.php:131
msgid "Select User Type"
msgstr ""

#: subscriber-table.php:134
msgid "All WordPress Users"
msgstr ""

#: subscriber-table.php:137
msgid "Apply"
msgstr ""

#: subscriber-table.php:252
msgid "Non-Recurring"
msgstr ""

#: subscriber-table.php:254
msgid "Recurring every %s"
msgstr ""

#. #-#-#-#-#  issuem-leaky-paywall.pot (Leaky Paywall 4.1.3)  #-#-#-#-#
#. Plugin URI of the plugin/theme
#. #-#-#-#-#  issuem-leaky-paywall.pot (Leaky Paywall 4.1.3)  #-#-#-#-#
#. Author URI of the plugin/theme
msgid "https://zeen101.com/"
msgstr ""

#. Description of the plugin/theme
msgid ""
"The #1 metered paywall for WordPress. Sell subscriptions without sacrificing "
"search and social visibility."
msgstr ""

#. Author of the plugin/theme
msgid "zeen101 Development Team"
msgstr ""

#. Tags of the plugin/theme
msgid ""
"memberships, subscriptions, restrict access, restrict content, paywall, "
"stripe, paypal"
msgstr ""
