@use 'theme';

$off-color: #789;

.snippet-execution-button,
.snippet-activation-switch,
input[type='checkbox'].switch {
	display: block;
	position: relative;
}

.snippet-activation-switch,
input[type='checkbox'].switch {
	appearance: none;
	outline: 0;
	cursor: pointer;
	margin: 0;
	inline-size: 32px;
	block-size: 19px;
	border-radius: 34px;
	text-align: start;
	border: 1px solid $off-color;
	box-sizing: border-box;

	&::before {
		transition: all .1s;
		content: '';
		block-size: 13px;
		inline-size: 13px;
		display: inline-block;
		background-color: $off-color;
		border-radius: 50%;
		margin: 2px;
	}
}

.active-snippet .snippet-activation-switch,
input[type='checkbox'].switch:checked {
	background-color: #0073aa;

	&::before {
		background-color: white;
		transform: translateX(100%);
	}
}

.erroneous-snippet .snippet-activation-switch::before {
	content: '!';
	transform: translateX(50%);
	text-align: center;
	font-weight: bold;
	line-height: 1;
	color: #bbb;
}

a.snippet-condition-count {
	border-radius: 50%;
	border: 1.8px solid currentcolor;
	block-size: 29px;
	inline-size: 29px;
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;

	.inactive-snippet & {
		color: #ccc;
	}

	.active-snippet & {
		font-weight: bold;
		color: theme.$accent;
	}
}

.snippet-execution-button {
	margin-inline-start: 11px;
	margin-block-start: 9px;
	inline-size: 0;
	block-size: 0;
	border-block: 9px solid transparent;
	border-inline-start: 10px solid #ccc;

	&::before {
		content: '';
		position: absolute;
		inset: -14px -8px -14px -21px;
		border-radius: 50%;
		border: 1.8px solid #ccc;
		z-index: 2;
	}

	&:hover {
		border-inline-start-color: theme.$accent;
		transition: border-left-color 0.6s;

		&::before {
			border-color: theme.$accent;
			transition: border-color 0.6s;
		}
	}
}
