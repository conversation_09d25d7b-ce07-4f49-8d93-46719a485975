<?php
namespace wp_index_generator\includes;

/**
 * This file contains general function to be utilize by the plugin
 *
 */

class wpig_lib
{

    /**
     *
     * This function parse the content recursively to create all the headings and respective subheadings
     *
     * @param array $headings An array of the headings with meta data
     * @param int $index It tells about the current index level of the table
     * @param int $recursive_counter This keep track of number of iteration
     *
     * @return null Does not return anything instead its echo all the content
     *
     */
    public static function parse_content($headings, $index = 0, $recursive_counter = 0, $style=1 )
    {
        // SET A MAX ITERATION LEVEL
        if ($recursive_counter > 60 || !count($headings) ) {
            return;
        }

        $hierarachy = self::wpig_get_option("showHierarchy","wpig_adv_settings","true");

        //reset headings in case something is skipped
        $headings = array_values( $headings );

        $last_element = $index > 0 ? $headings[$index - 1] : null;
        $current_element = $headings[$index];
        $next_element = $index < count($headings) ? (isset($headings[$index + 1]) ? $headings[$index + 1] : null) : null;
        // end of recursive calls at the end of Element(heading) list
        if ($current_element == null) {
            return;
        }

        // initialize required variables
        $tag = intval($headings[$index]["tag"]);
        $id = $headings[$index]["id"];
        $classes = $headings[$index]["classes"];
        $name = $headings[$index]["name"];
        // Bail out if required
        if (isset($current_element["classes"]) && in_array("skip-wpig", $current_element["classes"])) {
            self::parse_content($headings, $index + 1, $recursive_counter + 1, $style);
            return;
        }
        // begin of main list or sublist
        if ( ( $last_element == null || $last_element["tag"] < $tag ) && ( $hierarachy == "true" )) {
            echo "<ol>";
        }

        $li_classes = "" . $id;
        if ($current_element["classes"] && in_array("wpig-bold", $current_element["classes"])) {
            $li_classes = " bold";
        }

        echo "<li class='" . esc_attr( $li_classes ) . "'>";

        if ($current_element["classes"] && in_array("wpig-bold", $current_element["classes"])) {
            echo esc_html( $name );
        } else {
            echo "<a href='#" . esc_html( $id ). "'>" . esc_html( $name ) . "</a>";
        }
        if (isset($next_element["tag"]) && intval($next_element["tag"]) > $tag) {
            self::parse_content($headings, $index + 1, $recursive_counter + 1, $style);
        }

        echo "</li>";

        if (isset($next_element["tag"]) && intval($next_element["tag"]) == $tag) {
            self::parse_content($headings, $index + 1, $recursive_counter + 1, $style);
        }

        if ( ( $next_element == null || $next_element["tag"] < $tag ) && $recursive_counter != 1 && $hierarachy == "true") {
            echo "</ol>";
        }

        if ($next_element != null && $next_element["tag"] < $tag) {
            self::parse_content($headings, $index + 1, $recursive_counter + 1, $style);
        }
    }
	
	public static function parse_stickyheadercontent($headings, $index = 0, $recursive_counter = 0 )
    {
        // SET A MAX ITERATION LEVEL
        if ($recursive_counter > 60 || !count($headings) ) {
            return;
        }

        //reset headings in case something is skipped
        $headings = array_values( $headings );

        $last_element = $index > 0 ? $headings[$index - 1] : null;
        $current_element = $headings[$index];
        $next_element = $index < count($headings) ? (isset($headings[$index + 1]) ? $headings[$index + 1] : null) : null;
        // end of recursive calls at the end of Element(heading) list
        if ($current_element == null) {
            return;
        }

        // initialize required variables
        $tag = intval($headings[$index]["tag"]);
        $id = $headings[$index]["id"];
        $name = $headings[$index]["name"];
        		
		echo "<li class='stoc-header-li ".$id."'>";
        
			echo "<a href='#" . esc_html( $id ). "' class='wpig-scrolling-stoc__link'>" . esc_html( $name ) . "</a>";
        
			if (isset($next_element["tag"]) && intval($next_element["tag"]) > $tag) {
				self::parse_stickyheadercontent($headings, $index + 1, $recursive_counter + 1);
			}
		
		echo "</li>";
		
        if (isset($next_element["tag"]) && intval($next_element["tag"]) == $tag) {
            self::parse_stickyheadercontent($headings, $index + 1, $recursive_counter + 1);
        }

        if ($next_element != null && $next_element["tag"] < $tag) {
            self::parse_stickyheadercontent($headings, $index + 1, $recursive_counter + 1);
        }
    }

    /**
     * This function create the basic HTML of the index table
     * @param string $content Provide the complete content to parse and extract headings
     * @return string $content It returns all the HTML structure of the index table
     */
    public static function create_indexing($content, $override_style = null )
    {

        $style = self::wpig_get_option( "style_type", "wpig_adv_settings", "1");
        $style = $override_style == null ? $style : $override_style ;
        $enable_tgdisplay = self::wpig_get_option( "en_toggle_display", "wpig_settings", "true") ;
        $id_number_list = self::wpig_get_option( "showNumberList", "wpig_adv_settings", "true") ;
        $requiredHeadings = self::wpig_get_option( "requiredHeadings", "wpig_settings", "2");

        $headings = SELF::get_headings($content, $style);

        if( count( $headings ) < (int) $requiredHeadings ){
            return;
        }

        ob_start();

        $total_headings = count( $headings );
        $div = round( $total_headings / 2 );
        preg_match("</(h[1-9])>", $content, $H );
        preg_match_all( "</(". $H[1]. ")>", $content,$Head );
        $tag = str_replace("h","", $H[1]);
        $hideByDefault = self::wpig_get_option("hideByDefault","wpig_settings","true" );
        $hideByDefault = $hideByDefault == "true" ? "show" : "hide" ;
        $hierarachy = self::wpig_get_option("showHierarchy","wpig_adv_settings","true");
        $main_class = $id_number_list == "true" ? "wpig-number-list " : " " ;
        $main_class .= $hierarachy == "false" ? "wpig-no-hierarachy " : "";
        $main_class .= in_the_loop() == true ? "" : "wpig-sidebar-widget";
        switch ($style) {
            case 1:
                $main_class .= " wpig-style-1";
                echo "<div id='wpig-contents' class='wpig-contents ". esc_attr( $main_class )."'>";
                echo "<span class='wpig-headline'>". SELF::wpig_get_option("title","wpig_settings","Table Of Contents") ;
                
                if( $enable_tgdisplay != "false" ){
                    echo "<span><a data-action='". esc_attr( $hideByDefault ) ."' class='wpig-btn-toggledisplay'>[ ".ucwords( esc_attr( $hideByDefault ) )." ]</a></span>";
                }

                echo "</span>";
                echo "<!-- Table of Contents -->";
                echo "<div id='wpig-table-of-content' class='wpig-table-of-content' >" ;
                if( $hierarachy == "false" ){
                    echo "<ol>";
                }
                    SELF::parse_content($headings);
                if( $hierarachy == "false" ){
                    echo "</ol>";
                }
                echo "</div>" ;
                echo "<!-- END OF Table of Contents -->";
                echo "</div>";
            break;
            case 2:
                $main_class .= " wpig-style-2";
                echo "<div id='wpig-contents' class='wpig-contents ". esc_attr( $main_class ) ."'>";
                echo "<span class='wpig-headline'>". SELF::wpig_get_option("title","wpig_settings","Table Of Contents") ;
                
                if( $enable_tgdisplay != "false" ){
                    echo "<span><a data-action='". esc_attr( $hideByDefault ) ."' class='wpig-btn-toggledisplay'>[ ".ucwords( esc_attr( $hideByDefault ) )." ]</a></span>";
                }

                echo "</span>";

                echo "<span class='wpig-innersection'>" ;
                echo "<div id='wpig-table-of-content' class='wpig-table-of-content>" ;
                echo "<!-- Table of Contents -->";
                
                echo "<ol>";
                for( $x=0; $x<$total_headings; ){
                    echo "<div class='wpig-headline-subsections'>";
                        $current_heading = array_slice($headings,$x,$div );
                        SELF::parse_content( $current_heading , 0, 0, 2 );
                    echo "</div>";
                    $x += (int)$div;
                }
                echo "</ol>";
                
                echo "</div>";
                echo "</span>";
                echo "<!-- END OF Table of Contents -->";
                echo "</div>";
            break;
            case 3:
                $main_class = str_replace( "wpig-number-list","", $main_class );
                $main_class .= " wpig-style-3";
                echo "<div id='wpig-contents' class='wpig-contents ". esc_attr( $main_class ) ."'>";
                echo "<span class='wpig-headline'>". SELF::wpig_get_option("title","wpig_settings","Table Of Contents") ;
                
                if( $enable_tgdisplay != "false" ){
                    echo "<span><a data-action='". esc_attr( $hideByDefault ) ."' class='wpig-btn-toggledisplay'>[ ".ucwords( esc_attr( $hideByDefault ) )." ]</a></span>";
                }

                echo "</span>";
                echo "<!-- Table of Contents -->";
                echo "<div id='wpig-table-of-content' class='wpig-table-of-content'>" ;
                if( $hierarachy == "false" ){
                    echo "<ol>";
                }
                    SELF::parse_content($headings);
                if( $hierarachy == "false" ){
                    echo "</ol>";
                }
                echo "</div>" ;
                echo "<!-- END OF Table of Contents -->";
                echo "</div>";
            break;
			case 110: //sticky header nav
				echo '<nav class="wpig-scrolling-stoc__nav hide-nav"">';
				echo "<ul>";
					SELF::parse_stickyheadercontent($headings);
				echo "</ul>";
                echo "</nav>";
            break;
        }

        $content = ob_get_clean();

        return $content ;

    }

    /**
     *
     * This function sanitize all heading in the giving content and generate ID and Class
     * @param string $content A string that contains all the content of post
     *
     * @return string $content A filtered content of all post with filtered headings
     *
     */
    public static function sanitize_headings($content)
    {
      // Return early if content is null or empty to avoid deprecation warning
      if ( ! $content ) {
        return '';
      }

      $content = preg_replace_callback('/(\<h[1-6](.*?))\>(.*)(<\/h[1-6]>)/i', function ($matches) {
            if (!stripos($matches[0], 'id=')) {
                $matches[0] = $matches[1] . $matches[2] . ' id="stoc-' . sanitize_title($matches[3]) . '" class="wpig-heading">' . $matches[3] . $matches[4];
            }
            if (!stripos($matches[0], 'class=')) {
                $matches[0] = $matches[1] . $matches[2] . ' class="wpig-heading">' . $matches[3] . $matches[4];
            }
            return $matches[0];
        }, $content);
        return $content;
    }

    /**
     * This function will extract heading from the WP (post) content
     * @param string $content This must be a wordpress post data (NOT WP_POST)
     *
     * @return array $headings This array variable will contains all the heading extracted from the post
     */
    public static function get_headings($content, $style=1)
    {
        $headings = array();
       
        $selected_headings = self::wpig_get_option("selectedHeading","wpig_settings", array("h1","h2","h3","h4","h5","h6") ) ;

        preg_match_all("/<h([1-6])(.*)>(.*?)<\/h[1-6]>/i", $content, $matches);

        for ($i = 0; $i < count($matches[1]); $i++) {

            if( !in_array( "h".$matches[1][$i], $selected_headings ) ){
              continue;
            }

            $headings[$i]["tag"] = $matches[1][$i];
            // get id
            $att_string = $matches[2][$i];
            preg_match("/id=\"([^\"]*)\"/", $att_string, $id_matches);
			if (isset($id_matches[1])) {
				$headings[$i]["id"] = $id_matches[1];
			} else {
				$headings[$i]["id"] = '';
			}
			
            // get classes
            $att_string = $matches[2][$i];
            preg_match_all("/class=\"([^\"]*)\"/", $att_string, $class_matches);
            for ($j = 0; $j < count($class_matches[1]); $j++) {
                $headings[$i]["classes"][] = $class_matches[1][$j];
            }
            
            $text = null;
            
            preg_match_all("/><.*>(.*)<\/.*><\/h[1-6]/", $matches[0][$i], $text ) ;
            
            if(  preg_match_all("/><.*>(.*)<\/.*><\/h[1-6]/", $matches[0][$i], $text ) != null ){
                $headings[$i]["name"] = $text[1][0] ;
            }else{
                $headings[$i]["name"] = $matches[3][$i];
            }

        }

        return $headings;
    }

    /**
     * This function generates the custom CSS according to the settings
     * It does not required any arguments nor it returns any value
     */
    public static function generate_css(){

        $contentAlignment = self::wpig_get_option( "contentAlign", "wpig_adv_settings", "left");

        if( $contentAlignment == "left" ){
            $contentAlignment = "10px auto 10px 10px";
        }else if( $contentAlignment == "right" ){
            $contentAlignment = "10px 10px 10px auto";
        }else{
            $contentAlignment = "10px auto";
        }

        $bgColor = self::wpig_get_option( "backgroundColor", "wpig_adv_settings", "transparent");
        $borderColor = self::wpig_get_option( "borderColor", "wpig_adv_settings", "green");

        $width = self::wpig_get_option( "mainContainerWidth", "wpig_adv_settings", "");

        $headingBgColor = self::wpig_get_option( "backgroundColor", "wpig_settings", "transparent");
        $headingTextColor = self::wpig_get_option( "textColor", "wpig_settings", "black");
        $headingFontsize = self::wpig_get_option( "fontSize", "wpig_settings", "14");
        $headingFontsizeType = self::wpig_get_option( "fontSizeType", "wpig_settings", "px");

        $linkColor = self::wpig_get_option( "linkColor", "wpig_adv_settings", "#45ada8");
        $linkHoverColor = self::wpig_get_option( "linkHoverColor", "wpig_adv_settings", "#8224e3");
        $fontsize = self::wpig_get_option( "fontSize", "wpig_adv_settings", "14");
        $fontsizeType = self::wpig_get_option( "fontSizeType", "wpig_adv_settings", "px");

        $style = "/**    Dynamic Stylehseet by: Sticky TOC - Advance Table Of Contents. V: ".WPIG_V." **/\n";
        $style .= "#wpig-contents,#wpig-contents2{";
            $style .= "background:" . $bgColor . ";" ;
            $style .= "border:1px solid ". $borderColor .";";
            $style .= "margin:". $contentAlignment .";";
            $style .= "font-size:". $fontsize . $fontsizeType .";";
        $style .= "}";

        // Style for heading
        $style .= "#wpig-contents .wpig-headline,#wpig-contents2 .wpig-headline{";
            $style .= "background:" . $headingBgColor . ";" ;
            $style .= "color:" . $headingTextColor . ";" ;
            $style .= "font-size:". $headingFontsize . $headingFontsizeType .";";
        $style .= "}";        
        
        // Styles for text
        $style .= "#wpig-contents li a,#wpig-contents2 li a,";
        $style .= "#wpig-contents li , #wpig-contents2 li{";
            $style .= "color:" . $linkColor . ";" ;
            $style .= "font-size:". $fontsize . $fontsizeType .";";
        $style .= "}";

        $style .= "#wpig-contents.wpig-style-2 li:before{";
            $style .= "border-color:". $linkColor .";" ;
        $style .= "}";
        
        $style .= "#wpig-contents.wpig-style-2 li:before {";
            $style .= "border-color:". $linkColor .";" ;
        $style .="}";

        $style .= "#wpig-contents li a:hover,#wpig-contents2 li a:hover,";
        $style .= "#wpig-contents.wpig-style-2 ul .wpig-heading-wrapper:hover a{";
            $style .= "color:" . $linkHoverColor . ";" ;
        $style .= "}";

        if( !empty( $width ) ){
            $style .= "#wpig-contents, #wpig-contents2{";
                $style .= "width:".$width." !important;";
            $style .= "}";
        }

        $style .= "#wpig-contents.wpig-style-3 #wpig-table-of-content > ol li.activePoint:before,";
        $style .= "#wpig-contents2.wpig-style-3 #wpig-table-of-content > ol li.activePoint:before{";
            $style .= "background-color:". $linkHoverColor . ";";
        $style .= "}";

        $style .= "#wpig-contents.wpig-style-3 #wpig-table-of-content > ol li.activePoint a,";
        $style .= "#wpig-contents2.wpig-style-3 #wpig-table-of-content > ol li.activePoint a{";
            $style .= "color:". $linkHoverColor . ";";
        $style .= "}";

        $style .= "#wpig-contents.wpig-style-3 #wpig-table-of-content > ol li:before,";
        $style .= "#wpig-contents2.wpig-style-3 #wpig-table-of-content > ol li:before{";
            $style .= "border-color:".$linkColor.";";
            if( $bgColor != "transparent" ){
                $style .= "background-color:". $bgColor .";";
            }
        $style .= "}";

        $style .= "#wpig-contents.wpig-style-3 #wpig-table-of-content > ol > li:after,";
        $style .= "#wpig-contents2.wpig-style-3 #wpig-table-of-content > ol > li:after{";
            $style .= "border-right-color:".$linkColor.";";
        $style .= "}";
		
		$style .= "html{scroll-behavior:smooth}.wpig-scrolling-stoc__nav.hide-nav {-webkit-transform: translateY(-100%);transform: translateY(-100%);}.wpig-scrolling-stoc__nav {position: fixed;top: 0;right: 0;left: 0;z-index: 999;-webkit-transform: translateY(0);transform: translateY(0);-webkit-transition: -webkit-transform .3s ease-out;transition: -webkit-transform .3s ease-out;-o-transition: transform ease-out .3s;transition: transform .3s ease-out;transition: transform .3s ease-out,-webkit-transform .3s ease-out;background-color: " . $bgColor . ";-webkit-box-shadow: none;box-shadow: none;}";
		
		$style .= ".wpig-scrolling-stoc__nav ul .stoc-header-li {margin-bottom: 0;padding: 1rem;font-size: .875rem;line-height: 16px;}.wpig-scrolling-stoc__nav ul .stoc-header-li a, wpig-scrolling-stoc__nav ul .stoc-header-li p {font-family: Open Sans,sans-serif;font-weight: 400;color: ".$linkColor.";}.wpig-scrolling-stoc__nav ul .stoc-header-li a:hover, wpig-scrolling-stoc__nav ul .stoc-header-li p:hover {color: ".$linkHoverColor.";}.wpig-scrolling-stoc__nav ul .toc-li a {text-decoration: underline;}.wpig-scrolling-stoc__nav ul .stoc-header-li.active-anchor {border-bottom: 0.1875rem solid ". $borderColor .";}";
		
		$style .= ".wpig-scrolling-stoc__nav ul {display: -webkit-box;display: -ms-flexbox;display: flex;list-style-type: none;overflow-x: hidden;overflow-y: hidden;white-space: nowrap;height: 3.375rem;width: -webkit-fit-content;width: -moz-fit-content;width: fit-content;max-width: 70%;margin: 0 auto;padding: 0;}";

        wp_add_inline_style( "wpig-common", $style );

    }

    /**
     * Get the value of a settings field
     *
     * @param string $option settings field name
     * @param string $section the section name this field belongs to
     * @param string $default default text if it's not found
     *
     * @return mixed
     */
    public static function wpig_get_option($option, $section, $default = '')
    {

        $options = get_option($section);

        if (isset( $options[$option] ) && !empty( $options[$option] ) ) {
            return $options[$option];
        }

        return $default;
    }

}
