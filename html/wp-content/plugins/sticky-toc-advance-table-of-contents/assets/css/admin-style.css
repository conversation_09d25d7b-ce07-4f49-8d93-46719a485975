#toplevel_page_wpig-main div.wp-menu-image h{
    content: "";
    background:white;
    margin: 8px;
}
.wpig-settings-wrap .metabox-holder table tbody {
    display: block;
    background: white;
    padding: 10px;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0px 0px 4px 0px;
}
div#wpig-settings-header{
    width: 100%;
    padding:10px;
}
.wpig-settings-wrap .nav-tab-wrapper a {
    border: 1px solid gray;
    border-bottom: 0px;
    background: white;
    border-radius: 6px 6px 0px 0px;
}
.wpig-settings-wrap .nav-tab-wrapper a.nav-tab-active {
    border-top: 2px solid blue;
    border-right: 1px solid gray;
    border-left: 1px solid gray;
    color: blue;
}

#wpbody-content .metabox-holder {
    padding: 10px;
    background: white;
    border: 1px solid darkgray;
    border-top: 0px;
}

.wpig-settings-wrap .wp-picker-container.wp-picker-active {
    box-shadow: 0px 0px 10px 5px grey;
    padding: 10px;
    border-radius: 10px;
}

.wpig-settings-wrap #wpig-settings-sidebar{
    padding:10px;
    float:right;
    display:block;
    height:500px;
    min-width:40%;
}
.wpig-settings-wrap.wrap .form-table label {
    color: black;
    line-height: 40px;
    font-size: 1rem;
}
div.wpig-settings-wrap table tbody td input{
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
div.wpig-settings-wrap.wrap .form-table select{
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    -moz-padding-start: calc(0.75rem - 3px);
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    /* background-image: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e); */
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
div.wpig-settings-wrap.wrap .form-table input[type='range']{
    width: 80%;
    padding: 0px;
    display: inline-flex;
}
div.wpig-settings-wrap.wrap .form-table input[type='range']::-webkit-slider-thumb {
    padding: 10px;
    transition: background .5s ease-in-out;
    cursor: w-resize;
}
div.wpig-settings-wrap.wrap .form-table input[type='range']+label{
    float:right;
    line-height: 1rem;
}
div.wpig-settings-wrap label.multicheck {
    margin: 0px !important;
    padding: 0px !important;
    height: 25px !important;
}
div.wpig-settings-wrap input.multicheck {
    display: inline-flex !important;
    width: auto !important;
}
div.wpig-settings-wrap tr.fontSize input.regular-number{
    width: 90px !important;
    float: left;
}
div.wpig-settings-wrap tr.fontSize {
    width: 55% !important;
    display: inline-flex;
}
div.wpig-settings-wrap tr.fontsizetype th {
    display: none;
}
div.wpig-settings-wrap tr.fontsizetype{
    display: inline-flex;
}

#wpig_custom_css tr:first-of-type > th,
tr.displayshortcode th{
    display:none;
}
tr.displayshortcode td{
    font-size: 12px;
    width: 100%;
}
#wpig_custom_css td{
    width: 100%;
    position: relative;
}
#wpig_custom_css textarea+.CodeMirror{
    width: 500px;
    cursor: text;
    box-shadow: 1px 1px 8px 0px gray;
}
tr.fontdesc {
    display: flex;
}
tr.fontdesc th, tr.fontdesc td {
    padding-top: 0px;
}
tr.fontSize th, tr.fontSize td, tr.fontsizetype th, tr.fontsizetype td {
    padding-bottom: 0px;
    margin-bottom: 0px;
}