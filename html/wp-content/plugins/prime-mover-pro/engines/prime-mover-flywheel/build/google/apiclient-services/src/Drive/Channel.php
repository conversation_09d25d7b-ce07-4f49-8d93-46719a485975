<?php

/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
namespace Codexonics\PrimeMoverFramework\build\Google\Service\Drive;

class Channel extends \Codexonics\PrimeMoverFramework\build\Google\Model
{
    /**
     * @var string
     */
    public $address;
    /**
     * @var string
     */
    public $expiration;
    /**
     * @var string
     */
    public $id;
    /**
     * @var string
     */
    public $kind;
    /**
     * @var string[]
     */
    public $params;
    /**
     * @var bool
     */
    public $payload;
    /**
     * @var string
     */
    public $resourceId;
    /**
     * @var string
     */
    public $resourceUri;
    /**
     * @var string
     */
    public $token;
    /**
     * @var string
     */
    public $type;
    /**
     * @param string
     */
    public function setAddress($address)
    {
        $this->address = $address;
    }
    /**
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }
    /**
     * @param string
     */
    public function setExpiration($expiration)
    {
        $this->expiration = $expiration;
    }
    /**
     * @return string
     */
    public function getExpiration()
    {
        return $this->expiration;
    }
    /**
     * @param string
     */
    public function setId($id)
    {
        $this->id = $id;
    }
    /**
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
    /**
     * @param string
     */
    public function setKind($kind)
    {
        $this->kind = $kind;
    }
    /**
     * @return string
     */
    public function getKind()
    {
        return $this->kind;
    }
    /**
     * @param string[]
     */
    public function setParams($params)
    {
        $this->params = $params;
    }
    /**
     * @return string[]
     */
    public function getParams()
    {
        return $this->params;
    }
    /**
     * @param bool
     */
    public function setPayload($payload)
    {
        $this->payload = $payload;
    }
    /**
     * @return bool
     */
    public function getPayload()
    {
        return $this->payload;
    }
    /**
     * @param string
     */
    public function setResourceId($resourceId)
    {
        $this->resourceId = $resourceId;
    }
    /**
     * @return string
     */
    public function getResourceId()
    {
        return $this->resourceId;
    }
    /**
     * @param string
     */
    public function setResourceUri($resourceUri)
    {
        $this->resourceUri = $resourceUri;
    }
    /**
     * @return string
     */
    public function getResourceUri()
    {
        return $this->resourceUri;
    }
    /**
     * @param string
     */
    public function setToken($token)
    {
        $this->token = $token;
    }
    /**
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }
    /**
     * @param string
     */
    public function setType($type)
    {
        $this->type = $type;
    }
    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }
}
// Adding a class alias for backwards compatibility with the previous class name.
\class_alias(Channel::class, 'Codexonics\\PrimeMoverFramework\\build\\Google_Service_Drive_Channel');
