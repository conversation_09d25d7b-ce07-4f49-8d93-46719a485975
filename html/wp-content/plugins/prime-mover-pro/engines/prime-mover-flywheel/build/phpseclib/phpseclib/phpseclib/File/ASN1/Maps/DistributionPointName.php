<?php

/**
 * DistributionPointName
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */
namespace Codexonics\PrimeMoverFramework\build\phpseclib3\File\ASN1\Maps;

use Codexonics\PrimeMoverFramework\build\phpseclib3\File\ASN1;
/**
 * DistributionPointName
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class DistributionPointName
{
    const MAP = ['type' => ASN1::TYPE_CHOICE, 'children' => ['fullName' => ['constant' => 0, 'optional' => \true, 'implicit' => \true] + GeneralNames::MAP, 'nameRelativeToCRLIssuer' => ['constant' => 1, 'optional' => \true, 'implicit' => \true] + RelativeDistinguishedName::MAP]];
}
