<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Codexonics\\PrimeMoverFramework\\advance\\PrimeMoverTroubleshooting' => $baseDir . '/engines/prime-mover-panel/advance/PrimeMoverTroubleshooting.php',
    'Codexonics\\PrimeMoverFramework\\advance\\PrimeMoverUploadSettings' => $baseDir . '/engines/prime-mover-panel/advance/PrimeMoverUploadSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\CreateBlogSpecificID' => $baseDir . '/engines/prime-mover-flywheel/app/CreateBlogSpecificID.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverAutomaticBackup' => $baseDir . '/engines/prime-mover-flywheel/app/PrimeMoverAutomaticBackup.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverControlPanel' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverControlPanel.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayCustomDirSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayCustomDirSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayDropBoxSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayDropBoxSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayEncryptionSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayEncryptionSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayExcludedPluginsSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayExcludedPluginsSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayExcludedUploadSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayExcludedUploadSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayGDriveSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayGDriveSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayMaintenanceSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayMaintenanceSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplayRunTimeSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplayRunTimeSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplaySecuritySettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplaySecuritySettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDisplaySettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverDisplaySettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverDropBox' => $baseDir . '/engines/prime-mover-flywheel/app/PrimeMoverDropBox.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverEncryptedMedia' => $baseDir . '/engines/prime-mover-flywheel/app/PrimeMoverEncryptedMedia.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverForceUtf8' => $baseDir . '/engines/prime-mover-flywheel/app/PrimeMoverForceUtf8.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverGearBox' => $baseDir . '/engines/prime-mover-gearbox/app/PrimeMoverGearBox.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverGearBoxDeleteTemp' => $baseDir . '/engines/prime-mover-gearbox/app/PrimeMoverGearBoxDeleteTemp.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverGearBoxExport' => $baseDir . '/engines/prime-mover-gearbox/app/PrimeMoverGearBoxExport.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverGearBoxImport' => $baseDir . '/engines/prime-mover-gearbox/app/PrimeMoverGearBoxImport.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverGoogleDrive' => $baseDir . '/engines/prime-mover-flywheel/app/PrimeMoverGoogleDrive.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverMultisiteMainSite' => $baseDir . '/engines/prime-mover-flywheel/app/PrimeMoverMultisiteMainSite.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverReset' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverReset.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverSettings' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverSettings.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverSettingsConfig' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverSettingsConfig.php',
    'Codexonics\\PrimeMoverFramework\\app\\PrimeMoverSettingsTemplate' => $baseDir . '/engines/prime-mover-panel/app/PrimeMoverSettingsTemplate.php',
    'Codexonics\\PrimeMoverFramework\\archiver\\PrimeMoverArchiver' => $baseDir . '/archiver/PrimeMoverArchiver.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\CompressBzip2' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\CompressGzip' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\CompressGzipstream' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\CompressManagerFactory' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\CompressMethod' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\CompressNone' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\Mysqldump' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\TypeAdapter' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\TypeAdapterDblib' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\TypeAdapterFactory' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\TypeAdapterMysql' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\TypeAdapterPgsql' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\Ifsnop\\Mysqldump\\TypeAdapterSqlite' => $baseDir . '/build/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php',
    'Codexonics\\PrimeMoverFramework\\build\\WPConfigTransformer' => $baseDir . '/build/wp-cli/wp-config-transformer/src/WPConfigTransformer.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\Archive' => $baseDir . '/build/splitbrain/php-archive/src/Archive.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\ArchiveCorruptedException' => $baseDir . '/build/splitbrain/php-archive/src/ArchiveCorruptedException.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\ArchiveIOException' => $baseDir . '/build/splitbrain/php-archive/src/ArchiveIOException.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\ArchiveIllegalCompressionException' => $baseDir . '/build/splitbrain/php-archive/src/ArchiveIllegalCompressionException.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\FileInfo' => $baseDir . '/build/splitbrain/php-archive/src/FileInfo.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\FileInfoException' => $baseDir . '/build/splitbrain/php-archive/src/FileInfoException.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\Tar' => $baseDir . '/build/splitbrain/php-archive/src/Tar.php',
    'Codexonics\\PrimeMoverFramework\\build\\splitbrain\\PHPArchive\\Zip' => $baseDir . '/build/splitbrain/php-archive/src/Zip.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMover' => $baseDir . '/classes/PrimeMover.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverErrorHandlers' => $baseDir . '/classes/PrimeMoverErrorHandlers.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverExporter' => $baseDir . '/classes/PrimeMoverExporter.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverHookedMethods' => $baseDir . '/classes/PrimeMoverHookedMethods.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverImporter' => $baseDir . '/classes/PrimeMoverImporter.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverProgressHandlers' => $baseDir . '/classes/PrimeMoverProgressHandlers.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverSystemAuthorization' => $baseDir . '/classes/PrimeMoverSystemAuthorization.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverSystemChecks' => $baseDir . '/classes/PrimeMoverSystemChecks.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverSystemFunctions' => $baseDir . '/classes/PrimeMoverSystemFunctions.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverSystemInitialization' => $baseDir . '/classes/PrimeMoverSystemInitialization.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverSystemProcessors' => $baseDir . '/classes/PrimeMoverSystemProcessors.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverUsers' => $baseDir . '/classes/PrimeMoverUsers.php',
    'Codexonics\\PrimeMoverFramework\\classes\\PrimeMoverValidationHandlers' => $baseDir . '/classes/PrimeMoverValidationHandlers.php',
    'Codexonics\\PrimeMoverFramework\\cli\\PrimeMoverCLIArchive' => $baseDir . '/cli/PrimeMoverCLIArchive.php',
    'Codexonics\\PrimeMoverFramework\\cli\\PrimeMoverCLIShellArchiver' => $baseDir . '/cli/PrimeMoverCLIShellArchiver.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverBuddyPressCompat' => $baseDir . '/compatibility/PrimeMoverBuddyPressCompat.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverCachingCompat' => $baseDir . '/compatibility/PrimeMoverCachingCompat.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverCleanUp' => $baseDir . '/compatibility/PrimeMoverCleanUp.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverCompatibility' => $baseDir . '/compatibility/PrimeMoverCompatibility.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverCustomConfig' => $baseDir . '/compatibility/PrimeMoverCustomConfig.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverCustomMultisite' => $baseDir . '/compatibility/PrimeMoverCustomMultisite.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverElementorCompat' => $baseDir . '/compatibility/PrimeMoverElementorCompat.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverFreemiusCompat' => $baseDir . '/compatibility/PrimeMoverFreemiusCompat.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverHotFix' => $baseDir . '/compatibility/PrimeMoverHotFix.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverMigrationOptions' => $baseDir . '/menus/PrimeMoverMigrationOptions.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverMultilingualCompat' => $baseDir . '/compatibility/PrimeMoverMultilingualCompat.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverPageBuilderCompat' => $baseDir . '/compatibility/PrimeMoverPageBuilderCompat.php',
    'Codexonics\\PrimeMoverFramework\\compatibility\\PrimeMoverWooCommerceCompat' => $baseDir . '/compatibility/PrimeMoverWooCommerceCompat.php',
    'Codexonics\\PrimeMoverFramework\\extensions\\PrimeMoverAutoUserAdjustment' => $baseDir . '/engines/prime-mover-panel/extensions/PrimeMoverAutoUserAdjustment.php',
    'Codexonics\\PrimeMoverFramework\\extensions\\PrimeMoverEDDCompat' => $baseDir . '/engines/prime-mover-panel/extensions/PrimeMoverEDDCompat.php',
    'Codexonics\\PrimeMoverFramework\\extensions\\PrimeMoverGamiPressCompat' => $baseDir . '/engines/prime-mover-panel/extensions/PrimeMoverGamiPressCompat.php',
    'Codexonics\\PrimeMoverFramework\\extensions\\PrimeMoverLearnDash' => $baseDir . '/engines/prime-mover-panel/extensions/PrimeMoverLearnDash.php',
    'Codexonics\\PrimeMoverFramework\\extensions\\PrimeMoverRelevanssi' => $baseDir . '/engines/prime-mover-panel/extensions/PrimeMoverRelevanssi.php',
    'Codexonics\\PrimeMoverFramework\\extensions\\PrimeMoverWpFusion' => $baseDir . '/engines/prime-mover-panel/extensions/PrimeMoverWpFusion.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverAutoBackupSettingsHelper' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverAutoBackupSettingsHelper.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverAutomaticBackupHelper' => $baseDir . '/engines/prime-mover-flywheel/helpers/PrimeMoverAutomaticBackupHelper.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverCustomExportDirectory' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverCustomExportDirectory.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverDownloadAuthentication' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverDownloadAuthentication.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverDownloadSecurity' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverDownloadSecurity.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverDropBoxSettings' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverDropBoxSettings.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverDropBoxUploadProgress' => $baseDir . '/engines/prime-mover-flywheel/helpers/PrimeMoverDropBoxUploadProgress.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverEncryptionKeyHandler' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverEncryptionKeyHandler.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverExcludedPlugins' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverExcludedPlugins.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverExcludedTables' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverExcludedTables.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverExcludedUploads' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverExcludedUploads.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverExcludedUploadsUtilities' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverExcludedUploadsUtilities.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGDriveUploadProgress' => $baseDir . '/engines/prime-mover-flywheel/helpers/PrimeMoverGDriveUploadProgress.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGDriveUser' => $baseDir . '/engines/prime-mover-flywheel/helpers/PrimeMoverGDriveUser.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGdriveSettings' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverGdriveSettings.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGearBoxDownloadProgress' => $baseDir . '/engines/prime-mover-gearbox/helpers/PrimeMoverGearBoxDownloadProgress.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGearBoxImportHelpers' => $baseDir . '/engines/prime-mover-gearbox/helpers/PrimeMoverGearBoxImportHelpers.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGearBoxInitialization' => $baseDir . '/engines/prime-mover-gearbox/helpers/PrimeMoverGearBoxInitialization.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverGearBoxValidationUtilities' => $baseDir . '/engines/prime-mover-gearbox/helpers/PrimeMoverGearBoxValidationUtilities.php',
    'Codexonics\\PrimeMoverFramework\\helpers\\PrimeMoverMaintenanceMode' => $baseDir . '/engines/prime-mover-panel/helpers/PrimeMoverMaintenanceMode.php',
    'Codexonics\\PrimeMoverFramework\\interfaces\\PrimeMoverExport' => $baseDir . '/interfaces/PrimeMoverExport.php',
    'Codexonics\\PrimeMoverFramework\\interfaces\\PrimeMoverImport' => $baseDir . '/interfaces/PrimeMoverImport.php',
    'Codexonics\\PrimeMoverFramework\\interfaces\\PrimeMoverSystemCheck' => $baseDir . '/interfaces/PrimeMoverSystemCheck.php',
    'Codexonics\\PrimeMoverFramework\\interfaces\\PrimeMoverSystemInitialize' => $baseDir . '/interfaces/PrimeMoverSystemInitialize.php',
    'Codexonics\\PrimeMoverFramework\\interfaces\\PrimeMoverSystemProcessor' => $baseDir . '/interfaces/PrimeMoverSystemProcessor.php',
    'Codexonics\\PrimeMoverFramework\\menus\\PrimeMoverAutoBackupEventViewer' => $baseDir . '/menus/PrimeMoverAutoBackupEventViewer.php',
    'Codexonics\\PrimeMoverFramework\\menus\\PrimeMoverBackupMenuListTable' => $baseDir . '/menus/PrimeMoverBackupMenuListTable.php',
    'Codexonics\\PrimeMoverFramework\\menus\\PrimeMoverBackupMenus' => $baseDir . '/menus/PrimeMoverBackupMenus.php',
    'Codexonics\\PrimeMoverFramework\\menus\\PrimeMoverEventViewerListTable' => $baseDir . '/menus/PrimeMoverEventViewerListTable.php',
    'Codexonics\\PrimeMoverFramework\\menus\\PrimeMoverGearBoxScreenOptions' => $baseDir . '/menus/PrimeMoverGearBoxScreenOptions.php',
    'Codexonics\\PrimeMoverFramework\\streams\\PrimeMoverDatabaseUtilities' => $baseDir . '/streams/PrimeMoverDatabaseUtilities.php',
    'Codexonics\\PrimeMoverFramework\\streams\\PrimeMoverIterators' => $baseDir . '/streams/PrimeMoverIterators.php',
    'Codexonics\\PrimeMoverFramework\\streams\\PrimeMoverResumableDownloadStream' => $baseDir . '/streams/PrimeMoverResumableDownloadStream.php',
    'Codexonics\\PrimeMoverFramework\\streams\\PrimeMoverStreamFilters' => $baseDir . '/streams/PrimeMoverStreamFilters.php',
    'Codexonics\\PrimeMoverFramework\\users\\PrimeMoverUserFunctions' => $baseDir . '/users/PrimeMoverUserFunctions.php',
    'Codexonics\\PrimeMoverFramework\\users\\PrimeMoverUserQueries' => $baseDir . '/users/PrimeMoverUserQueries.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\DupxUpdateEngine' => $baseDir . '/utilities/DupxUpdateEngine.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverAutoBackupSetting' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverAutoBackupSetting.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverAutoBackupUtilities' => $baseDir . '/engines/prime-mover-flywheel/utilities/PrimeMoverAutoBackupUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverBackupDirectorySize' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverBackupDirectorySize.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverBackupManagement' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverBackupManagement.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverBackupUtilities' => $baseDir . '/utilities/PrimeMoverBackupUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverComponentAuxiliary' => $baseDir . '/utilities/PrimeMoverComponentAuxiliary.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverConfigUtilities' => $baseDir . '/utilities/PrimeMoverConfigUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverDeleteUtilities' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverDeleteUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverDownloadUtilities' => $baseDir . '/utilities/PrimeMoverDownloadUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverExportUtilities' => $baseDir . '/utilities/PrimeMoverExportUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverFlyWheelValidationUtilities' => $baseDir . '/engines/prime-mover-flywheel/utilities/PrimeMoverFlywheelValidationUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverFreemiusIntegration' => $baseDir . '/utilities/PrimeMoverFreemiusIntegration.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverGdriveDownloader' => $baseDir . '/engines/prime-mover-flywheel/utilities/PrimeMoverGdriveDownloader.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverImportUtilities' => $baseDir . '/utilities/PrimeMoverImportUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverLockUtilities' => $baseDir . '/utilities/PrimeMoverLockUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverOpenSSLUtilities' => $baseDir . '/utilities/PrimeMoverOpenSSLUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverPanelValidationUtilities' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverPanelValidationUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverSearchReplace' => $baseDir . '/utilities/PrimeMoverSearchReplace.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverSearchReplaceUtilities' => $baseDir . '/utilities/PrimeMoverSearchReplaceUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverSettingsHelper' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverSettingsHelper.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverSettingsMarkups' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverSettingsMarkups.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverShutdownUtilities' => $baseDir . '/utilities/PrimeMoverShutdownUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverSystemCheckUtilities' => $baseDir . '/utilities/PrimeMoverSystemCheckUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverSystemUtilities' => $baseDir . '/utilities/PrimeMoverSystemUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverToolBox' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverToolBox.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverTroubleshootingMarkup' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverTroubleshootingMarkup.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverUploadSettingMarkup' => $baseDir . '/engines/prime-mover-panel/utilities/PrimeMoverUploadSettingMarkup.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverUploadUtilities' => $baseDir . '/utilities/PrimeMoverUploadUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverUserUtilities' => $baseDir . '/utilities/PrimeMoverUserUtilities.php',
    'Codexonics\\PrimeMoverFramework\\utilities\\PrimeMoverValidationUtilities' => $baseDir . '/utilities/PrimeMoverValidationUtilities.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'PrimeMoverCoreSaltDependencies' => $baseDir . '/dependency-checks/PrimeMoverCoreSaltDependencies.php',
    'PrimeMoverFileSystemDependencies' => $baseDir . '/dependency-checks/PrimeMoverFileSystemDependencies.php',
    'PrimeMoverPHPCoreFunctionDependencies' => $baseDir . '/dependency-checks/PrimeMoverPHPCoreFunctionDependencies.php',
    'PrimeMoverPHPVersionDependencies' => $baseDir . '/dependency-checks/PrimeMoverPHPVersionDependencies.php',
    'PrimeMoverPluginSlugDependencies' => $baseDir . '/dependency-checks/PrimeMoverPluginSlugDependencies.php',
    'PrimeMoverRequirementsCheck' => $baseDir . '/dependency-checks/PrimeMoverRequirementsCheck.php',
    'PrimeMoverWPCoreDependencies' => $baseDir . '/dependency-checks/PrimeMoverWPCoreDepedencies.php',
);
