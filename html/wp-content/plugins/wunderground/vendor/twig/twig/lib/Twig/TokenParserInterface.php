<?php

/*
 * This file is part of Twig.
 *
 * (c) 2010 Fabien Po<PERSON>cier
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * Interface implemented by token parsers.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface Twig_TokenParserInterface
{
    /**
     * Sets the parser associated with this token parser.
     *
     * @param Twig_Parser $parser A Twig_Parser instance
     */
    public function setParser(Twig_Parser $parser);

    /**
     * Parses a token and returns a node.
     *
     * @param Twig_Token $token A Twig_Token instance
     *
     * @return Twig_NodeInterface A Twig_NodeInterface instance
     *
     * @throws Twig_Error_Syntax
     */
    public function parse(Twig_Token $token);

    /**
     * Gets the tag name associated with this token parser.
     *
     * @return string The tag name
     */
    public function getTag();
}
