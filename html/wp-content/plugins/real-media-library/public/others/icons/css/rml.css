@font-face {
    font-family: "rml";
    src: url("../font/rml.eot?21909263");
    src:
        url("../font/rml.eot?21909263#iefix") format("embedded-opentype"),
        url("../font/rml.woff2?21909263") format("woff2"),
        url("../font/rml.woff?21909263") format("woff"),
        url("../font/rml.ttf?21909263") format("truetype"),
        url("../font/rml.svg?21909263#rml") format("svg");
    font-weight: normal;
    font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'rml';
    src: url('../font/rml.svg?21909263#rml') format('svg');
  }
}
*/

[class^="rmlicon-"]:before,
[class*=" rmlicon-"]:before {
    font-family: "rml";
    font-style: normal;
    font-weight: normal;
    speak: none;

    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: 0.2em;
    text-align: center;
    /* opacity: .8; */

    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;

    /* fix buttons height, for twitter bootstrap */
    line-height: 1em;

    /* Animation center compensation - margins should be symmetric */
    /* remove if not needed */
    margin-left: 0.2em;

    /* you can be more comfortable with increased icons size */
    /* font-size: 120%; */

    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Uncomment for 3D effect */
    /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.rmlicon-collection:before {
    content: "\e800";
} /* '' */
.rmlicon-gallery:before {
    content: "\e801";
} /* '' */
.rmlicon-share:before {
    content: "\e802";
} /* '' */
.rmlicon-archive:before {
    content: "\e803";
} /* '' */
