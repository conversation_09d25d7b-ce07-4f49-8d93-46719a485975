<script type="text/javascript">
  jQuery(function($){
    $('#mailpoet_woocommerce_customize_button')
      .insertAfter($('#email_notification_settings-description'))
      .show();
  });
</script>

<p id="mailpoet_woocommerce_customize_button" style="display: none;">
  <a class="button button-primary"
    href="?page=mailpoet-newsletter-editor&id=<%= woocommerce_template_id %>"
    data-automation-id="mailpoet_woocommerce_customize_button"
  >
    <%= _x('Customize with MailPoet', 'Button in WooCommerce settings page') %>
  </a>
</p>
