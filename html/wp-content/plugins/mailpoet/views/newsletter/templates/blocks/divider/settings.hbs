<h3><%= __('Dividers') %></h3>
<div class="mailpoet_divider_selector" data-automation-id="divider_selector">
{{#each availableStyles.dividers}}
    <div class="mailpoet_field_divider_style{{#ifCond this '==' ../model.styles.block.borderStyle}} mailpoet_active_divider_style{{/ifCond}}" data-style="{{ this }}">
        <div style="border-top-width: 5px; border-top-style: {{ this }}; border-top-color: {{ ../model.styles.block.borderColor }};"></div>
    </div>
{{/each}}
</div>
<div class="mailpoet_form_field">
    <label>
        <div class="mailpoet_form_field_input_option">
            <input type="number" name="border-width-input" class="mailpoet_input mailpoet_input_small mailpoet_field_divider_border_width_input" value="{{getNumber model.styles.block.borderWidth}}" min="1" max="30" step="1" /> px
            <input type="range" min="1" max="30" step="1" name="divider-width" class="mailpoet_range mailpoet_range_small mailpoet_field_divider_border_width" value="{{getNumber model.styles.block.borderWidth }}" />
        </div>
        <div class="mailpoet_form_field_title mailpoet_form_field_title_inline"><%= __('Divider height') %></div>
    </label>
</div>
<div class="mailpoet_form_field">
    <div class="mailpoet_form_field_input_option">
        <input type="text" name="divider-color" class="mailpoet_field_divider_border_color mailpoet_color" value="{{ model.styles.block.borderColor }}" />
    </div>
    <div class="mailpoet_form_field_title mailpoet_form_field_title_inline"><%= __('Divider color') %></div>
</div>
<div class="mailpoet_form_field">
    <div class="mailpoet_form_field_input_option">
        <input type="text" name="background-color" class="mailpoet_field_divider_background_color mailpoet_color" value="{{ model.styles.block.backgroundColor }}" />
    </div>
    <div class="mailpoet_form_field_title mailpoet_form_field_title_inline"><%= __('Background') %></div>
</div>
{{#ifCond renderOptions.hideApplyToAll '!==' true}}
<div class="mailpoet_form_field">
    <input type="button" name="apply-to-all-dividers" class="button button-secondary mailpoet_button_full mailpoet_button_divider_apply_to_all" value="<%= __('Apply to all dividers') | escape('html_attr') %>" />
</div>
{{/ifCond}}

<div class="mailpoet_form_field">
    <input type="button" class="button button-primary mailpoet_done_editing" value="<%= __('Done') | escape('html_attr') %>" />
</div>
