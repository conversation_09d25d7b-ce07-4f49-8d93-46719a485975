<div class="mailpoet_social_icon_settings">
    <div class="mailpoet_social_icon_settings_tool mailpoet_social_icon_settings_move_icon">
        <a href="javascript:;" class="mailpoet_move_block"><%= source('newsletter/templates/svg/block-tools/move-without-bg.svg') %></a>
    </div>
    <div class="mailpoet_social_icon_settings_tool mailpoet_social_icon_settings_delete_icon">
        <a href="javascript:;" class="mailpoet_delete_block"><%= source('newsletter/templates/svg/block-tools/trash-without-bg.svg') %></a>
    </div>
    <div class="mailpoet_social_icon_settings_row">
        <label>
        <div class="mailpoet_social_icon_settings_label mailpoet_social_icon_image_label">
            <img src="{{ model.image }}" onerror="if (this.src != '{{ allIconSets.default.custom }}') this.src = '{{ allIconSets.default.custom }}';" alt="{{ model.text }}" class="mailpoet_social_icon_image" />
        </div>
        <div class="mailpoet_social_icon_settings_form_element">
            <select name="iconType" class="mailpoet_social_icon_field_type">
            {{#each iconTypes}}
                <option value="{{ iconType }}" {{#ifCond iconType '==' ../model.iconType}}SELECTED{{/ifCond}}>{{ title }}</option>
            {{/each}}
            </select>
        </div>
        </label>
    </div>

    {{#ifCond iconType '==' 'custom'}}
    <div class="mailpoet_social_icon_settings_row">
        <label>
        <div class="mailpoet_social_icon_settings_label">
            <%= __('Image') %>
        </div>
        <div class="mailpoet_social_icon_settings_form_element">
            <input type="text" name="image" class="mailpoet_social_icon_field_image" value="{{ model.image }}" placeholder="http://" />
        </div>
        </label>
    </div>
    {{/ifCond}}

    <div class="mailpoet_social_icon_settings_row">
        <label>
        <div class="mailpoet_social_icon_settings_label">
            {{#if currentType.linkFieldName}}
                {{ currentType.linkFieldName }}
            {{else}}
                <%= __('Link') %>
            {{/if}}
        </div>
        <div class="mailpoet_social_icon_settings_form_element">
            {{#ifCond iconType '==' 'email'}}
            <input type="text" name="link" class="mailpoet_social_icon_field_link" value="{{emailFromMailto model.link }}" placeholder="<EMAIL>" /><br />
            {{else}}
            <input type="text" name="link" class="mailpoet_social_icon_field_link" value="{{ model.link }}" placeholder="http://" /><br />
            {{/ifCond}}
        </div>
        </label>
    </div>

    {{#ifCond iconType '==' 'custom'}}
    <div class="mailpoet_social_icon_settings_row">
        <label>
        <div class="mailpoet_social_icon_settings_label">
            <%= __('Text') %>
        </div>
        <div class="mailpoet_social_icon_settings_form_element">
            <input type="text" name="text" class="mailpoet_social_icon_field_text" value="{{ model.text }}" />
        </div>
        </label>
    </div>
    {{/ifCond}}
</div>
