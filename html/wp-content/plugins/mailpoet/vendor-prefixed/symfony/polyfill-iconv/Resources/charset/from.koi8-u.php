<?php
namespace MailPoetVendor;
if (!defined('ABSPATH')) exit;
static $data = array(' ' => ' ', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', ' ' => ' ', '
' => '
', '' => '', '' => '', '
' => '
', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', '' => '', ' ' => ' ', '!' => '!', '"' => '"', '#' => '#', '$' => '$', '%' => '%', '&' => '&', '\'' => '\'', '(' => '(', ')' => ')', '*' => '*', '+' => '+', ',' => ',', '-' => '-', '.' => '.', '/' => '/', 0 => '0', 1 => '1', 2 => '2', 3 => '3', 4 => '4', 5 => '5', 6 => '6', 7 => '7', 8 => '8', 9 => '9', ':' => ':', ';' => ';', '<' => '<', '=' => '=', '>' => '>', '?' => '?', '@' => '@', 'A' => 'A', 'B' => 'B', 'C' => 'C', 'D' => 'D', 'E' => 'E', 'F' => 'F', 'G' => 'G', 'H' => 'H', 'I' => 'I', 'J' => 'J', 'K' => 'K', 'L' => 'L', 'M' => 'M', 'N' => 'N', 'O' => 'O', 'P' => 'P', 'Q' => 'Q', 'R' => 'R', 'S' => 'S', 'T' => 'T', 'U' => 'U', 'V' => 'V', 'W' => 'W', 'X' => 'X', 'Y' => 'Y', 'Z' => 'Z', '[' => '[', '\\' => '\\', ']' => ']', '^' => '^', '_' => '_', '`' => '`', 'a' => 'a', 'b' => 'b', 'c' => 'c', 'd' => 'd', 'e' => 'e', 'f' => 'f', 'g' => 'g', 'h' => 'h', 'i' => 'i', 'j' => 'j', 'k' => 'k', 'l' => 'l', 'm' => 'm', 'n' => 'n', 'o' => 'o', 'p' => 'p', 'q' => 'q', 'r' => 'r', 's' => 's', 't' => 't', 'u' => 'u', 'v' => 'v', 'w' => 'w', 'x' => 'x', 'y' => 'y', 'z' => 'z', '{' => '{', '|' => '|', '}' => '}', '~' => '~', '' => '', '�' => '─', '�' => '│', '�' => '┌', '�' => '┐', '�' => '└', '�' => '┘', '�' => '├', '�' => '┤', '�' => '┬', '�' => '┴', '�' => '┼', '�' => '▀', '�' => '▄', '�' => '█', '�' => '▌', '�' => '▐', '�' => '░', '�' => '▒', '�' => '▓', '�' => '⌠', '�' => '■', '�' => '•', '�' => '√', '�' => '≈', '�' => '≤', '�' => '≥', '�' => ' ', '�' => '⌡', '�' => '°', '�' => '²', '�' => '·', '�' => '÷', '�' => '═', '�' => '║', '�' => '╒', '�' => 'ё', '�' => 'є', '�' => '╔', '�' => 'і', '�' => 'ї', '�' => '╗', '�' => '╘', '�' => '╙', '�' => '╚', '�' => '╛', '�' => 'ґ', '�' => '╝', '�' => '╞', '�' => '╟', '�' => '╠', '�' => '╡', '�' => 'Ё', '�' => 'Ѓ', '�' => '╣', '�' => 'І', '�' => 'Ї', '�' => '╦', '�' => '╧', '�' => '╨', '�' => '╩', '�' => '╪', '�' => 'Ґ', '�' => '╬', '�' => '©', '�' => 'ю', '�' => 'а', '�' => 'б', '�' => 'ц', '�' => 'д', '�' => 'е', '�' => 'ф', '�' => 'г', '�' => 'х', '�' => 'и', '�' => 'й', '�' => 'к', '�' => 'л', '�' => 'м', '�' => 'н', '�' => 'о', '�' => 'п', '�' => 'я', '�' => 'р', '�' => 'с', '�' => 'т', '�' => 'у', '�' => 'ж', '�' => 'в', '�' => 'ь', '�' => 'ы', '�' => 'з', '�' => 'ш', '�' => 'э', '�' => 'щ', '�' => 'ч', '�' => 'ъ', '�' => 'Ю', '�' => 'А', '�' => 'Б', '�' => 'Ц', '�' => 'Д', '�' => 'Е', '�' => 'Ф', '�' => 'Г', '�' => 'Х', '�' => 'И', '�' => 'Й', '�' => 'К', '�' => 'Л', '�' => 'М', '�' => 'Н', '�' => 'О', '�' => 'П', '�' => 'Я', '�' => 'Р', '�' => 'С', '�' => 'Т', '�' => 'У', '�' => 'Ж', '�' => 'В', '�' => 'Ь', '�' => 'Ы', '�' => 'З', '�' => 'Ш', '�' => 'Э', '�' => 'Щ', '�' => 'Ч', '�' => 'Ъ');
$result =& $data;
unset($data);
return $result;
