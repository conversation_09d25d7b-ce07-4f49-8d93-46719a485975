<?php

if (!defined('ABSPATH')) exit;


use MailPoetVendor\Twig\Environment;
use MailPoetVendor\Twig\Error\LoaderError;
use MailPoetVendor\Twig\Error\RuntimeError;
use MailPoetVendor\Twig\Extension\CoreExtension;
use MailPoetVendor\Twig\Extension\SandboxExtension;
use MailPoetVendor\Twig\Markup;
use MailPoetVendor\Twig\Sandbox\SecurityError;
use MailPoetVendor\Twig\Sandbox\SecurityNotAllowedTagError;
use MailPoetVendor\Twig\Sandbox\SecurityNotAllowedFilterError;
use MailPoetVendor\Twig\Sandbox\SecurityNotAllowedFunctionError;
use MailPoetVendor\Twig\Source;
use MailPoetVendor\Twig\Template;

/* segments/translations.html */
class __TwigTemplate_1226cb2a2a523414588d646f3936e830c6cfbe9095954afbfa0d22781d8cba5b extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        yield $this->extensions['MailPoet\Twig\I18n']->localize(["name" => $this->extensions['MailPoet\Twig\I18n']->translate("Name"), "description" => $this->extensions['MailPoet\Twig\I18n']->translate("Description"), "segmentUpdated" => $this->extensions['MailPoet\Twig\I18n']->translate("List successfully updated!"), "segmentAdded" => $this->extensions['MailPoet\Twig\I18n']->translate("List successfully added!"), "segment" => $this->extensions['MailPoet\Twig\I18n']->translate("List"), "subscribed" => $this->extensions['MailPoet\Twig\I18n']->translate("Subscribed"), "unconfirmed" => $this->extensions['MailPoet\Twig\I18n']->translate("Unconfirmed"), "unsubscribed" => $this->extensions['MailPoet\Twig\I18n']->translate("Unsubscribed"), "inactive" => $this->extensions['MailPoet\Twig\I18n']->translate("Inactive"), "bounced" => $this->extensions['MailPoet\Twig\I18n']->translate("Bounced"), "createdOn" => $this->extensions['MailPoet\Twig\I18n']->translate("Created on"), "oneSegmentTrashed" => $this->extensions['MailPoet\Twig\I18n']->translate("1 list was moved to the trash. Note that deleting a list does not delete its subscribers."), "multipleSegmentsTrashed" => $this->extensions['MailPoet\Twig\I18n']->translate("%1\$d lists were moved to the trash. Note that deleting a list does not delete its subscribers."), "oneSegmentDeleted" => $this->extensions['MailPoet\Twig\I18n']->translate("1 list was permanently deleted. Note that deleting a list does not delete its subscribers."), "multipleSegmentsDeleted" => $this->extensions['MailPoet\Twig\I18n']->translate("%1\$d lists were permanently deleted. Note that deleting a list does not delete its subscribers."), "oneSegmentRestored" => $this->extensions['MailPoet\Twig\I18n']->translate("1 list has been restored from the Trash."), "multipleSegmentsRestored" => $this->extensions['MailPoet\Twig\I18n']->translate("%1\$d lists have been restored from the Trash."), "duplicate" => $this->extensions['MailPoet\Twig\I18n']->translate("Duplicate"), "listDuplicated" => $this->extensions['MailPoet\Twig\I18n']->translate("List \"%1\$s\" has been duplicated."), "update" => $this->extensions['MailPoet\Twig\I18n']->translate("Update"), "forceSync" => $this->extensions['MailPoet\Twig\I18n']->translate("Force Sync"), "readMore" => $this->extensions['MailPoet\Twig\I18n']->translate("Read More"), "listSynchronized" => $this->extensions['MailPoet\Twig\I18n']->translate("List \"%1\$s\" has been synchronized."), "listSynchronizationWasScheduled" => $this->extensions['MailPoet\Twig\I18n']->translate("Synchronization of the \"%1\$s\" list started. It can take several minutes to finish."), "viewSubscribers" => $this->extensions['MailPoet\Twig\I18n']->translate("View subscribers"), "newSegment" => $this->extensions['MailPoet\Twig\I18n']->translate("Add new segment"), "edit" => $this->extensions['MailPoet\Twig\I18n']->translate("Edit"), "trash" => $this->extensions['MailPoet\Twig\I18n']->translate("Trash"), "moveToTrash" => $this->extensions['MailPoet\Twig\I18n']->translate("Move to trash"), "trashAndDisable" => $this->extensions['MailPoet\Twig\I18n']->translate("Trash and disable"), "restoreAndEnable" => $this->extensions['MailPoet\Twig\I18n']->translate("Restore and enable"), "listScore" => $this->extensions['MailPoet\Twig\I18n']->translate("List score"), "segmentDescriptionTip" => $this->extensions['MailPoet\Twig\I18n']->translate("This text box is for your own use and is never shown to your subscribers."), "segmentFormName" => $this->extensions['MailPoet\Twig\I18n']->translate("Public list name"), "segmentFormNameTip" => $this->extensions['MailPoet\Twig\I18n']->translate("Subscribers may see this name when managing their subscriptions."), "backToList" => $this->extensions['MailPoet\Twig\I18n']->translate("Back"), "showInManageSubscriptionPage" => $this->extensions['MailPoet\Twig\I18n']->translate("List visibility"), "showInManageSubscriptionPageTip" => $this->extensions['MailPoet\Twig\I18n']->translate("Show this list on the \"Manage Subscription\" page"), "mailpoetSubscribers" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("%s MailPoet subscribers", "number of subscribers in the plugin"), "mailpoetSubscribersTooltipFree" => $this->extensions['MailPoet\Twig\I18n']->translate("This is the total of all subscribers including %1\$d WordPress users. To exclude WordPress users, please purchase one of our premium plans."), "mailpoetSubscribersTooltipPremium" => $this->extensions['MailPoet\Twig\I18n']->translate("This is the total of all subscribers excluding all WordPress users."), "pageTitleSegments" => $this->extensions['MailPoet\Twig\I18n']->translate("Segments"), "formSegmentTitle" => $this->extensions['MailPoet\Twig\I18n']->translate("Segment"), "descriptionTip" => $this->extensions['MailPoet\Twig\I18n']->translate("This text box is for your own use and is never shown to your subscribers."), "dynamicSegmentUpdated" => $this->extensions['MailPoet\Twig\I18n']->translate("Segment successfully updated!"), "dynamicSegmentAdded" => $this->extensions['MailPoet\Twig\I18n']->translate("Segment successfully added!"), "segmentType" => $this->extensions['MailPoet\Twig\I18n']->translate("Type"), "wpUserRole" => $this->extensions['MailPoet\Twig\I18n']->translate("Subscriber"), "email" => $this->extensions['MailPoet\Twig\I18n']->translate("Email"), "loadingDynamicSegmentItems" => $this->extensions['MailPoet\Twig\I18n']->translate("Loading data…"), "notSentYet" => $this->extensions['MailPoet\Twig\I18n']->translate("Not sent yet"), "allLinksPlaceholder" => $this->extensions['MailPoet\Twig\I18n']->translate("All links"), "noLinksHint" => $this->extensions['MailPoet\Twig\I18n']->translate("Email not sent yet!"), "selectNewsletterPlaceholder" => $this->extensions['MailPoet\Twig\I18n']->translate("Search emails"), "selectUserRolePlaceholder" => $this->extensions['MailPoet\Twig\I18n']->translate("Search user roles"), "selectCustomFieldPlaceholder" => $this->extensions['MailPoet\Twig\I18n']->translate("Select custom field"), "emailActionOpens" => $this->extensions['MailPoet\Twig\I18n']->translate("opens"), "emailActionOpensSentence" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("{condition} {opens} opens", "The result will be \"more than 20 opens\""), "emailActionOpensDaysSentence" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("{timeframe} {days} days", "The result will be \"in the last 5 days\""), "moreThan" => $this->extensions['MailPoet\Twig\I18n']->translate("more than"), "lessThan" => $this->extensions['MailPoet\Twig\I18n']->translate("less than"), "higherThan" => $this->extensions['MailPoet\Twig\I18n']->translate("higher than"), "lowerThan" => $this->extensions['MailPoet\Twig\I18n']->translate("lower than"), "equals" => $this->extensions['MailPoet\Twig\I18n']->translate("equals"), "notEquals" => $this->extensions['MailPoet\Twig\I18n']->translate("not equals"), "contains" => $this->extensions['MailPoet\Twig\I18n']->translate("contains"), "notContains" => $this->extensions['MailPoet\Twig\I18n']->translate("does not contain"), "value" => $this->extensions['MailPoet\Twig\I18n']->translate("value"), "selectValue" => $this->extensions['MailPoet\Twig\I18n']->translate("Select value"), "checked" => $this->extensions['MailPoet\Twig\I18n']->translate("checked"), "unchecked" => $this->extensions['MailPoet\Twig\I18n']->translate("unchecked"), "unknown" => $this->extensions['MailPoet\Twig\I18n']->translate("unknown"), "notUnknown" => $this->extensions['MailPoet\Twig\I18n']->translate("not unknown"), "is" => $this->extensions['MailPoet\Twig\I18n']->translate("is"), "isNot" => $this->extensions['MailPoet\Twig\I18n']->translate("is not"), "startsWith" => $this->extensions['MailPoet\Twig\I18n']->translate("starts with"), "notStartsWith" => $this->extensions['MailPoet\Twig\I18n']->translate("does not start with"), "endsWith" => $this->extensions['MailPoet\Twig\I18n']->translate("ends with"), "notEndsWith" => $this->extensions['MailPoet\Twig\I18n']->translate("does not end with"), "firstName" => $this->extensions['MailPoet\Twig\I18n']->translate("First name"), "lastName" => $this->extensions['MailPoet\Twig\I18n']->translate("Last name"), "email" => $this->extensions['MailPoet\Twig\I18n']->translate("Email"), "lastClickDate" => $this->extensions['MailPoet\Twig\I18n']->translate("Last click date"), "lastEngagementDate" => $this->extensions['MailPoet\Twig\I18n']->translate("Last engagement date"), "lastOpenDate" => $this->extensions['MailPoet\Twig\I18n']->translate("Last open date"), "lastPageViewDate" => $this->extensions['MailPoet\Twig\I18n']->translate("Last page view date"), "lastPurchaseDate" => $this->extensions['MailPoet\Twig\I18n']->translate("Last purchase date"), "lastSendingDate" => $this->extensions['MailPoet\Twig\I18n']->translate("Last sending date"), "before" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("before", "Meaning: \"Subscriber subscribed before April\""), "after" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("after", "Meaning: \"Subscriber subscribed after April"), "isBlank" => $this->extensions['MailPoet\Twig\I18n']->translate("is blank"), "isNotBlank" => $this->extensions['MailPoet\Twig\I18n']->translate("is not blank"), "onOrBefore" => $this->extensions['MailPoet\Twig\I18n']->translate("on or before"), "on" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("on", "Meaning: \"Subscriber subscribed on a given date\""), "onOrAfter" => $this->extensions['MailPoet\Twig\I18n']->translate("on or after"), "notOn" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("not on", "Meaning: \"Subscriber subscribed on a date other than the given date\""), "inTheLast" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("in the last", "Meaning: \"Subscriber subscribed in the last 3 days\""), "notInTheLast" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("not in the last", "Meaning: \"Subscriber subscribed not in the last 3 days\""), "overAllTime" => $this->extensions['MailPoet\Twig\I18n']->translate("over all time"), "subscribedDate" => $this->extensions['MailPoet\Twig\I18n']->translate("subscribed date"), "subscriberScore" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("engagement score", "Subscriber engagement score"), "subscriberScorePlaceholder" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("score", "Placeholder for input: subscriber engagement score"), "subscriberScoreSentence" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("{condition} {score} %", "The result will be \"higher than 20 %\""), "subscribedToList" => $this->extensions['MailPoet\Twig\I18n']->translate("subscribed to list"), "subscribedViaForm" => $this->extensions['MailPoet\Twig\I18n']->translate("subscribed via form"), "segmentsSubscriber" => $this->extensions['MailPoet\Twig\I18n']->translate("WordPress user role"), "mailpoetCustomField" => $this->extensions['MailPoet\Twig\I18n']->translate("MailPoet custom field"), "woocommerceMemberships" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("WooCommerce Memberships", "Dynamic segment creation: User selects this to use any WooCommerce Memberships filters"), "selectWooMembership" => $this->extensions['MailPoet\Twig\I18n']->translate("Search membership plans"), "woocommerceSubscriptions" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("WooCommerce Subscriptions", "Dynamic segment creation: User selects this to use any WooCommerce Subscriptions filters"), "selectWooSubscription" => $this->extensions['MailPoet\Twig\I18n']->translate("Search subscriptions"), "searchLists" => $this->extensions['MailPoet\Twig\I18n']->translate("Search lists"), "subscriberTag" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("tag", "Subscriber tag"), "searchTags" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("Search tags"), "searchForms" => $this->extensions['MailPoet\Twig\I18n']->translate("Search forms"), "anyOf" => $this->extensions['MailPoet\Twig\I18n']->translate("any of"), "allOf" => $this->extensions['MailPoet\Twig\I18n']->translate("all of"), "noneOf" => $this->extensions['MailPoet\Twig\I18n']->translate("none of"), "automations" => $this->extensions['MailPoet\Twig\I18n']->translate("Automations"), "automationsEnteredAutomation" => $this->extensions['MailPoet\Twig\I18n']->translate("entered automation"), "automationsExitedAutomation" => $this->extensions['MailPoet\Twig\I18n']->translate("exited automation"), "searchAutomations" => $this->extensions['MailPoet\Twig\I18n']->translate("Search automations"), "wooAnyStarRating" => $this->extensions['MailPoet\Twig\I18n']->translate("any"), "wooOneStarRating" => $this->extensions['MailPoet\Twig\I18n']->translate("1 star"), "wooTwoStarRating" => $this->extensions['MailPoet\Twig\I18n']->translate("2 star"), "wooThreeStarRating" => $this->extensions['MailPoet\Twig\I18n']->translate("3 star"), "wooFourStarRating" => $this->extensions['MailPoet\Twig\I18n']->translate("4 star"), "wooFiveStarRating" => $this->extensions['MailPoet\Twig\I18n']->translate("5 star"), "moreThan" => $this->extensions['MailPoet\Twig\I18n']->translate("more than"), "moreThanOrEqual" => $this->extensions['MailPoet\Twig\I18n']->translate("more than or equal"), "lessThan" => $this->extensions['MailPoet\Twig\I18n']->translate("less than"), "lessThanOrEqual" => $this->extensions['MailPoet\Twig\I18n']->translate("less than or equal"), "wooNumberOfOrdersCount" => $this->extensions['MailPoet\Twig\I18n']->translate("count"), "daysPlaceholder" => $this->extensions['MailPoet\Twig\I18n']->translate("days"), "days" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("days", "Appears together with `inTheLast` when creating a new WooCommerce segment based on the number of orders."), "inTheLast" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("in the last", "Appears together with `days` when creating a new WooCommerce segment based on the number of orders."), "wooNumberOfOrdersOrders" => $this->extensions['MailPoet\Twig\I18n']->translate("orders"), "wooNumberOfReviewsReviews" => $this->extensions['MailPoet\Twig\I18n']->translate("reviews"), "wooSpentAmount" => $this->extensions['MailPoet\Twig\I18n']->translate("amount"), "selectWooPurchasedCategory" => $this->extensions['MailPoet\Twig\I18n']->translate("Search categories"), "selectWooPurchasedProduct" => $this->extensions['MailPoet\Twig\I18n']->translate("Search products"), "selectWooCountry" => $this->extensions['MailPoet\Twig\I18n']->translate("Search countries"), "selectWooCouponCodes" => $this->extensions['MailPoet\Twig\I18n']->translate("Search coupon codes"), "selectWooPaymentMethods" => $this->extensions['MailPoet\Twig\I18n']->translate("Search payment methods"), "selectWooShippingMethods" => $this->extensions['MailPoet\Twig\I18n']->translate("Search shipping methods"), "woocommerce" => $this->extensions['MailPoet\Twig\I18n']->translateWithContext("WooCommerce", "Dynamic segment creation: User selects this to use any woocommerce filters"), "dynamicSegmentSizeIsCalculated" => $this->extensions['MailPoet\Twig\I18n']->translate("Calculating segment size…"), "dynamicSegmentSizeCalculatingTimeout" => $this->extensions['MailPoet\Twig\I18n']->translate("It's taking longer than usual to generate the segment, which may be due to a complex configuration. Try using fewer or simpler conditions."), "dynamicSegmentSize" => $this->extensions['MailPoet\Twig\I18n']->translate("This segment has %1\$d subscribers."), "learnMore" => $this->extensions['MailPoet\Twig\I18n']->translate("Learn more."), "unknownBadgeName" => $this->extensions['MailPoet\Twig\I18n']->translate("Unknown"), "unknownBadgeTooltip" => $this->extensions['MailPoet\Twig\I18n']->translate("Not enough data."), "tooltipUnknown" => $this->extensions['MailPoet\Twig\I18n']->translate("Fewer than 3 emails sent"), "excellentBadgeName" => $this->extensions['MailPoet\Twig\I18n']->translate("Excellent"), "excellentBadgeTooltip" => $this->extensions['MailPoet\Twig\I18n']->translate("Congrats!"), "tooltipExcellent" => $this->extensions['MailPoet\Twig\I18n']->translate("50% or more"), "goodBadgeName" => $this->extensions['MailPoet\Twig\I18n']->translate("Good"), "goodBadgeTooltip" => $this->extensions['MailPoet\Twig\I18n']->translate("Good stuff."), "tooltipGood" => $this->extensions['MailPoet\Twig\I18n']->translate("between 20 and 50%"), "averageBadgeName" => $this->extensions['MailPoet\Twig\I18n']->translate("Low"), "averageBadgeTooltip" => $this->extensions['MailPoet\Twig\I18n']->translate("Something to improve."), "tooltipAverage" => $this->extensions['MailPoet\Twig\I18n']->translate("20% or fewer"), "engagementScoreDescription" => $this->extensions['MailPoet\Twig\I18n']->translate("Average percent of emails subscribers read in the last year"), "privacyProtectionNotice" => $this->extensions['MailPoet\Twig\I18n']->translate("Due to email privacy protections, some opens may not be tracked. Consider using a different engagement metric."), "premiumFeatureMultipleConditions" => $this->extensions['MailPoet\Twig\I18n']->translate("Your current MailPoet plan does not support advanced segments with multiple conditions. Upgrade to the MailPoet Business plan to more precisely target your emails based on how people engage with your business. [link]Learn more.[/link]"), "premiumBannerCtaFree" => $this->extensions['MailPoet\Twig\I18n']->translate("Upgrade"), "premiumFeatureDescription" => $this->extensions['MailPoet\Twig\I18n']->translate("Your current MailPoet plan includes advanced features, but they require the MailPoet Premium plugin to be installed and activated."), "premiumFeatureButtonDownloadPremium" => $this->extensions['MailPoet\Twig\I18n']->translate("Download MailPoet Premium plugin"), "premiumFeatureButtonActivatePremium" => $this->extensions['MailPoet\Twig\I18n']->translate("Activate MailPoet Premium plugin"), "premiumFeatureDescriptionSubscribersLimitReached" => $this->extensions['MailPoet\Twig\I18n']->translate("Congratulations, you now have [subscribersCount] subscribers! Your plan is limited to [subscribersLimit] subscribers. You need to upgrade now to be able to continue using MailPoet."), "premiumFeatureButtonUpgradePlan" => $this->extensions['MailPoet\Twig\I18n']->translate("Upgrade your plan")]);
        // line 188
        yield "
";
        return; yield '';
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "segments/translations.html";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  40 => 188,  38 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "segments/translations.html", "/home/<USER>/mailpoet/mailpoet/views/segments/translations.html");
    }
}
