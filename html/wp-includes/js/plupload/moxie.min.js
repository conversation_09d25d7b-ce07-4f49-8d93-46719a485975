var MXI_DEBUG=!1;!function(o,x){"use strict";var s={};function n(e,t){for(var i,n=[],r=0;r<e.length;++r){if(!(i=s[e[r]]||function(e){for(var t=o,i=e.split(/[.\/]/),n=0;n<i.length;++n){if(!t[i[n]])return;t=t[i[n]]}return t}(e[r])))throw"module definition dependecy not found: "+e[r];n.push(i)}t.apply(null,n)}function e(e,t,i){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(t===x)throw"invalid module definition, dependencies must be specified";if(i===x)throw"invalid module definition, definition function must be specified";n(t,function(){s[e]=i.apply(null,arguments)})}e("moxie/core/utils/Basic",[],function(){function n(i){return s(arguments,function(e,t){0<t&&s(e,function(e,t){void 0!==e&&(o(i[t])===o(e)&&~r(o(e),["array","object"])?n(i[t],e):i[t]=e)})}),i}function s(e,t){var i,n,r;if(e)if("number"===o(e.length)){for(r=0,i=e.length;r<i;r++)if(!1===t(e[r],r))return}else if("object"===o(e))for(n in e)if(e.hasOwnProperty(n)&&!1===t(e[n],n))return}function r(e,t){if(t){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e);for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i}return-1}var o=function(e){return void 0===e?"undefined":null===e?"null":e.nodeType?"node":{}.toString.call(e).match(/\s([a-z|A-Z]+)/)[1].toLowerCase()};a=0;var a;return{guid:function(e){for(var t=(new Date).getTime().toString(32),i=0;i<5;i++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"o_")+t+(a++).toString(32)},typeOf:o,extend:n,each:s,isEmptyObj:function(e){if(e&&"object"===o(e))for(var t in e)return!1;return!0},inSeries:function(e,n){var r=e.length;"function"!==o(n)&&(n=function(){}),e&&e.length||n(),function t(i){"function"===o(e[i])&&e[i](function(e){++i<r&&!e?t(i):n(e)})}(0)},inParallel:function(e,i){var n=0,r=e.length,o=new Array(r);s(e,function(e,t){e(function(e){if(e)return i(e);e=[].slice.call(arguments);e.shift(),o[t]=e,++n===r&&(o.unshift(null),i.apply(this,o))})})},inArray:r,arrayDiff:function(e,t){var i,n=[];for(i in"array"!==o(e)&&(e=[e]),"array"!==o(t)&&(t=[t]),e)-1===r(e[i],t)&&n.push(e[i]);return!!n.length&&n},arrayIntersect:function(e,t){var i=[];return s(e,function(e){-1!==r(e,t)&&i.push(e)}),i.length?i:null},toArray:function(e){for(var t=[],i=0;i<e.length;i++)t[i]=e[i];return t},trim:function(e){return e&&(String.prototype.trim?String.prototype.trim.call(e):e.toString().replace(/^\s*/,"").replace(/\s*$/,""))},sprintf:function(e){var t=[].slice.call(arguments,1);return e.replace(/%[a-z]/g,function(){var e=t.shift();return"undefined"!==o(e)?e:""})},parseSizeStr:function(e){var t,i;return"string"!=typeof e?e:(t={t:1099511627776,g:1073741824,m:1048576,k:1024},i=(e=/^([0-9\.]+)([tmgk]?)$/.exec(e.toLowerCase().replace(/[^0-9\.tmkg]/g,"")))[2],e=+e[1],t.hasOwnProperty(i)&&(e*=t[i]),Math.floor(e))}}}),e("moxie/core/utils/Env",["moxie/core/utils/Basic"],function(n){m="function",h="object",r=function(e,t){return-1!==t.toLowerCase().indexOf(e.toLowerCase())},o={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[a="name",c="version"],[/\s(opr)\/([\w\.]+)/i],[[a,"Opera"],c],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi)\/([\w\.-]+)/i],[a,c],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[a,"IE"],c],[/(edge)\/((\d+)?[\w\.]+)/i],[a,c],[/(yabrowser)\/([\w\.]+)/i],[[a,"Yandex"],c],[/(comodo_dragon)\/([\w\.]+)/i],[[a,/_/g," "],c],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i,/(uc\s?browser|qqbrowser)[\/\s]?([\w\.]+)/i],[a,c],[/(dolfin)\/([\w\.]+)/i],[[a,"Dolphin"],c],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[a,"Chrome"],c],[/XiaoMi\/MiuiBrowser\/([\w\.]+)/i],[c,[a,"MIUI Browser"]],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)/i],[c,[a,"Android Browser"]],[/FBAV\/([\w\.]+);/i],[c,[a,"Facebook"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[c,[a,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[c,a],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[a,[c,(i={rgx:function(){for(var e,t,i,n,r,o,s,a=0,u=arguments;a<u.length;a+=2){var c=u[a],l=u[a+1];if(void 0===e)for(n in e={},l)typeof(r=l[n])==h?e[r[0]]=d:e[r]=d;for(t=i=0;t<c.length;t++)if(o=c[t].exec(this.getUA())){for(n=0;n<l.length;n++)s=o[++i],typeof(r=l[n])==h&&0<r.length?2==r.length?typeof r[1]==m?e[r[0]]=r[1].call(this,s):e[r[0]]=r[1]:3==r.length?typeof r[1]!=m||r[1].exec&&r[1].test?e[r[0]]=s?s.replace(r[1],r[2]):d:e[r[0]]=s?r[1].call(this,s,r[2]):d:4==r.length&&(e[r[0]]=s?r[3].call(this,s.replace(r[1],r[2])):d):e[r]=s||d;break}if(o)break}return e},str:function(e,t){for(var i in t)if(typeof t[i]==h&&0<t[i].length){for(var n=0;n<t[i].length;n++)if(r(t[i][n],e))return"?"===i?d:i}else if(r(t[i],e))return"?"===i?d:i;return e}}).str,(e={browser:{oldsafari:{major:{1:["/8","/1","/3"],2:"/4","?":"/"},version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",RT:"ARM"}}}}).browser.oldsafari.version]],[/(konqueror)\/([\w\.]+)/i,/(webkit|khtml)\/([\w\.]+)/i],[a,c],[/(navigator|netscape)\/([\w\.-]+)/i],[[a,"Netscape"],c],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]+)*/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[a,c]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[c,[a,"EdgeHTML"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[a,c],[/rv\:([\w\.]+).*(gecko)/i],[c,a]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[a,c],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[a,[c,i.str,e.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[a,"Windows"],[c,i.str,e.os.windows.version]],[/\((bb)(10);/i],[[a,"BlackBerry"],c],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\os|qnx|bada|rim\stablet\sos|meego|contiki)[\/\s-]?([\w\.]+)*/i,/linux;.+(sailfish);/i],[a,c],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[[a,"Symbian"],c],[/\((series40);/i],[a],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[a,"Firefox OS"],c],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],[a,c],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[a,"Chromium OS"],c],[/(sunos)\s?([\w\.]+\d)*/i],[[a,"Solaris"],c],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],[a,c],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[[a,"iOS"],[c,/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i,/(macintosh|mac(?=_powerpc)\s)/i],[[a,"Mac OS"],[c,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]+)*/i,/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms)/i,/(unix)\s?([\w\.]+)*/i],[a,c]]};var d,m,h,r,i,o,e=function(e){var t=e||(window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"");this.getBrowser=function(){return i.rgx.apply(this,o.browser)},this.getEngine=function(){return i.rgx.apply(this,o.engine)},this.getOS=function(){return i.rgx.apply(this,o.os)},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS()}},this.getUA=function(){return t},this.setUA=function(e){return t=e,this},this.setUA(t)};function t(e){var t=[].slice.call(arguments);return t.shift(),"function"===n.typeOf(u[e])?u[e].apply(this,t):!!u[e]}u={define_property:!1,create_canvas:!(!(a=document.createElement("canvas")).getContext||!a.getContext("2d")),return_response_type:function(e){try{if(-1!==n.inArray(e,["","text","document"]))return!0;if(window.XMLHttpRequest){var t=new XMLHttpRequest;if(t.open("get","/"),"responseType"in t)return t.responseType=e,t.responseType===e}}catch(e){}return!1},use_data_uri:((s=new Image).onload=function(){u.use_data_uri=1===s.width&&1===s.height},setTimeout(function(){s.src="data:image/gif;base64,R0lGODlhAQABAIAAAP8AAAAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="},1),!1),use_data_uri_over32kb:function(){return u.use_data_uri&&("IE"!==l.browser||9<=l.version)},use_data_uri_of:function(e){return u.use_data_uri&&e<33e3||u.use_data_uri_over32kb()},use_fileinput:function(){var e;return!navigator.userAgent.match(/(Android (1.0|1.1|1.5|1.6|2.0|2.1))|(Windows Phone (OS 7|8.0))|(XBLWP)|(ZuneWP)|(w(eb)?OSBrowser)|(webOS)|(Kindle\/(1.0|2.0|2.5|3.0))/)&&((e=document.createElement("input")).setAttribute("type","file"),!e.disabled)}};var s,a,u,c=(new e).getResult(),l={can:t,uaParser:e,browser:c.browser.name,version:c.browser.version,os:c.os.name,osVersion:c.os.version,verComp:function(e,t,i){function n(e){return(e=(e=(""+e).replace(/[_\-+]/g,".")).replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,".")).length?e.split("."):[-8]}function r(e){return e?isNaN(e)?u[e]||-7:parseInt(e,10):0}var o,s=0,a=0,u={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1};for(e=n(e),t=n(t),o=Math.max(e.length,t.length),s=0;s<o;s++)if(e[s]!=t[s]){if(e[s]=r(e[s]),t[s]=r(t[s]),e[s]<t[s]){a=-1;break}if(e[s]>t[s]){a=1;break}}if(!i)return a;switch(i){case">":case"gt":return 0<a;case">=":case"ge":return 0<=a;case"<=":case"le":return a<=0;case"==":case"=":case"eq":return 0===a;case"<>":case"!=":case"ne":return 0!==a;case"":case"<":case"lt":return a<0;default:return null}},global_event_dispatcher:"moxie.core.EventTarget.instance.dispatchEvent"};return l.OS=l.os,MXI_DEBUG&&(l.debug={runtime:!0,events:!1},l.log=function(){var e,t,i=arguments[0];"string"===n.typeOf(i)&&(i=n.sprintf.apply(this,arguments)),window&&window.console&&window.console.log?window.console.log(i):document&&((e=document.getElementById("moxie-console"))||((e=document.createElement("pre")).id="moxie-console",document.body.appendChild(e)),-1!==n.inArray(n.typeOf(i),["object","array"])?(t=i,e.appendChild(document.createTextNode(t+"\n"))):e.appendChild(document.createTextNode(i+"\n")))}),l}),e("moxie/core/I18n",["moxie/core/utils/Basic"],function(i){var t={};return{addI18n:function(e){return i.extend(t,e)},translate:function(e){return t[e]||e},_:function(e){return this.translate(e)},sprintf:function(e){var t=[].slice.call(arguments,1);return e.replace(/%[a-z]/g,function(){var e=t.shift();return"undefined"!==i.typeOf(e)?e:""})}}}),e("moxie/core/utils/Mime",["moxie/core/utils/Basic","moxie/core/I18n"],function(a,n){var e={mimes:{},extensions:{},addMimeType:function(e){for(var t,i,n=e.split(/,/),r=0;r<n.length;r+=2){for(i=n[r+1].split(/ /),t=0;t<i.length;t++)this.mimes[i[t]]=n[r];this.extensions[n[r]]=i}},extList2mimes:function(e,t){for(var i,n,r,o=[],s=0;s<e.length;s++)for(i=e[s].extensions.split(/\s*,\s*/),n=0;n<i.length;n++){if("*"===i[n])return[];if((r=this.mimes[i[n]])&&-1===a.inArray(r,o)&&o.push(r),t&&/^\w+$/.test(i[n]))o.push("."+i[n]);else if(!r)return[]}return o},mimes2exts:function(e){var n=this,r=[];return a.each(e,function(e){if("*"===e)return!(r=[]);var i=e.match(/^(\w+)\/(\*|\w+)$/);i&&("*"===i[2]?a.each(n.extensions,function(e,t){new RegExp("^"+i[1]+"/").test(t)&&[].push.apply(r,n.extensions[t])}):n.extensions[e]&&[].push.apply(r,n.extensions[e]))}),r},mimes2extList:function(e){var t=[],i=[];return"string"===a.typeOf(e)&&(e=a.trim(e).split(/\s*,\s*/)),i=this.mimes2exts(e),t.push({title:n.translate("Files"),extensions:i.length?i.join(","):"*"}),t.mimes=e,t},getFileExtension:function(e){e=e&&e.match(/\.([^.]+)$/);return e?e[1].toLowerCase():""},getFileMime:function(e){return this.mimes[this.getFileExtension(e)]||""}};return e.addMimeType("application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats-officedocument.wordprocessingml.document,docx,application/vnd.openxmlformats-officedocument.wordprocessingml.template,dotx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,xlsx,application/vnd.openxmlformats-officedocument.presentationml.presentation,pptx,application/vnd.openxmlformats-officedocument.presentationml.template,potx,application/vnd.openxmlformats-officedocument.presentationml.slideshow,ppsx,application/x-javascript,js,application/json,json,audio/mpeg,mp3 mpga mpega mp2,audio/x-wav,wav,audio/x-m4a,m4a,audio/ogg,oga ogg,audio/aiff,aiff aif,audio/flac,flac,audio/aac,aac,audio/ac3,ac3,audio/x-ms-wma,wma,image/bmp,bmp,image/gif,gif,image/jpeg,jpg jpeg jpe,image/photoshop,psd,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/plain,asc txt text diff log,text/html,htm html xhtml,text/css,css,text/csv,csv,text/rtf,rtf,video/mpeg,mpeg mpg mpe m2v,video/quicktime,qt mov,video/mp4,mp4,video/x-m4v,m4v,video/x-flv,flv,video/x-ms-wmv,wmv,video/avi,avi,video/webm,webm,video/3gpp,3gpp 3gp,video/3gpp2,3g2,video/vnd.rn-realvideo,rv,video/ogg,ogv,video/x-matroska,mkv,application/vnd.oasis.opendocument.formula-template,otf,application/octet-stream,exe"),e}),e("moxie/core/utils/Dom",["moxie/core/utils/Env"],function(c){function i(e,t){return!!e.className&&new RegExp("(^|\\s+)"+t+"(\\s+|$)").test(e.className)}return{get:function(e){return"string"!=typeof e?e:document.getElementById(e)},hasClass:i,addClass:function(e,t){i(e,t)||(e.className=e.className?e.className.replace(/\s+$/,"")+" "+t:t)},removeClass:function(e,t){e.className&&(t=new RegExp("(^|\\s+)"+t+"(\\s+|$)"),e.className=e.className.replace(t,function(e,t,i){return" "===t&&" "===i?" ":""}))},getStyle:function(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle?window.getComputedStyle(e,null)[t]:void 0},getPos:function(e,t){var i,n,r,o=0,s=0,a=document;function u(e){var t,i=0,n=0;return e&&(e=e.getBoundingClientRect(),t="CSS1Compat"===a.compatMode?a.documentElement:a.body,i=e.left+t.scrollLeft,n=e.top+t.scrollTop),{x:i,y:n}}if(t=t||a.body,e&&e.getBoundingClientRect&&"IE"===c.browser&&(!a.documentMode||a.documentMode<8))return n=u(e),r=u(t),{x:n.x-r.x,y:n.y-r.y};for(i=e;i&&i!=t&&i.nodeType;)o+=i.offsetLeft||0,s+=i.offsetTop||0,i=i.offsetParent;for(i=e.parentNode;i&&i!=t&&i.nodeType;)o-=i.scrollLeft||0,s-=i.scrollTop||0,i=i.parentNode;return{x:o,y:s}},getSize:function(e){return{w:e.offsetWidth||e.clientWidth,h:e.offsetHeight||e.clientHeight}}}}),e("moxie/core/Exceptions",["moxie/core/utils/Basic"],function(e){function t(e,t){for(var i in e)if(e[i]===t)return i;return null}return{RuntimeError:(a={NOT_INIT_ERR:1,NOT_SUPPORTED_ERR:9,JS_ERR:4},e.extend(d,a),d.prototype=Error.prototype,d),OperationNotAllowedException:(e.extend(l,{NOT_ALLOWED_ERR:1}),l.prototype=Error.prototype,l),ImageError:(s={WRONG_FORMAT:1,MAX_RESOLUTION_ERR:2,INVALID_META_ERR:3},e.extend(c,s),c.prototype=Error.prototype,c),FileException:(o={NOT_FOUND_ERR:1,SECURITY_ERR:2,ABORT_ERR:3,NOT_READABLE_ERR:4,ENCODING_ERR:5,NO_MODIFICATION_ALLOWED_ERR:6,INVALID_STATE_ERR:7,SYNTAX_ERR:8},e.extend(u,o),u.prototype=Error.prototype,u),DOMException:(r={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25},e.extend(n,r),n.prototype=Error.prototype,n),EventException:(e.extend(i,{UNSPECIFIED_EVENT_TYPE_ERR:0}),i.prototype=Error.prototype,i)};function i(e){this.code=e,this.name="EventException"}function n(e){this.code=e,this.name=t(r,e),this.message=this.name+": DOMException "+this.code}var r,o,s,a;function u(e){this.code=e,this.name=t(o,e),this.message=this.name+": FileException "+this.code}function c(e){this.code=e,this.name=t(s,e),this.message=this.name+": ImageError "+this.code}function l(e){this.code=e,this.name="OperationNotAllowedException"}function d(e){this.code=e,this.name=t(a,e),this.message=this.name+": RuntimeError "+this.code}}),e("moxie/core/EventTarget",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic"],function(c,l,d){function e(){var u={};d.extend(this,{uid:null,init:function(){this.uid||(this.uid=d.guid("uid_"))},addEventListener:function(e,t,i,n){var r,o=this;this.hasOwnProperty("uid")||(this.uid=d.guid("uid_")),e=d.trim(e),/\s/.test(e)?d.each(e.split(/\s+/),function(e){o.addEventListener(e,t,i,n)}):(e=e.toLowerCase(),i=parseInt(i,10)||0,(r=u[this.uid]&&u[this.uid][e]||[]).push({fn:t,priority:i,scope:n||this}),u[this.uid]||(u[this.uid]={}),u[this.uid][e]=r)},hasEventListener:function(e){e=e?u[this.uid]&&u[this.uid][e]:u[this.uid];return e||!1},removeEventListener:function(e,t){e=e.toLowerCase();var i,n=u[this.uid]&&u[this.uid][e];if(n){if(t){for(i=n.length-1;0<=i;i--)if(n[i].fn===t){n.splice(i,1);break}}else n=[];n.length||(delete u[this.uid][e],d.isEmptyObj(u[this.uid])&&delete u[this.uid])}},removeAllEventListeners:function(){u[this.uid]&&delete u[this.uid]},dispatchEvent:function(e){var t,i,n,r,o,s={},a=!0;if("string"!==d.typeOf(e)){if(r=e,"string"!==d.typeOf(r.type))throw new l.EventException(l.EventException.UNSPECIFIED_EVENT_TYPE_ERR);e=r.type,void 0!==r.total&&void 0!==r.loaded&&(s.total=r.total,s.loaded=r.loaded),s.async=r.async||!1}return-1!==e.indexOf("::")?(r=e.split("::"),t=r[0],e=r[1]):t=this.uid,e=e.toLowerCase(),(i=u[t]&&u[t][e])&&(i.sort(function(e,t){return t.priority-e.priority}),(n=[].slice.call(arguments)).shift(),s.type=e,n.unshift(s),MXI_DEBUG&&c.debug.events&&c.log("Event '%s' fired on %u",s.type,t),o=[],d.each(i,function(t){n[0].target=t.scope,o.push(s.async?function(e){setTimeout(function(){e(!1===t.fn.apply(t.scope,n))},1)}:function(e){e(!1===t.fn.apply(t.scope,n))})}),o.length)&&d.inSeries(o,function(e){a=!e}),a},bind:function(){this.addEventListener.apply(this,arguments)},unbind:function(){this.removeEventListener.apply(this,arguments)},unbindAll:function(){this.removeAllEventListeners.apply(this,arguments)},trigger:function(){return this.dispatchEvent.apply(this,arguments)},handleEventProps:function(e){var t=this;this.bind(e.join(" "),function(e){e="on"+e.type.toLowerCase();"function"===d.typeOf(this[e])&&this[e].apply(this,arguments)}),d.each(e,function(e){e="on"+e.toLowerCase(e),"undefined"===d.typeOf(t[e])&&(t[e]=null)})}})}return e.instance=new e,e}),e("moxie/runtime/Runtime",["moxie/core/utils/Env","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/EventTarget"],function(c,l,d,i){var n={},m={};function h(e,t,r,i,n){var o,s,a=this,u=l.guid(t+"_"),n=n||"browser";e=e||{},m[u]=this,r=l.extend({access_binary:!1,access_image_binary:!1,display_media:!1,do_cors:!1,drag_and_drop:!1,filter_by_extension:!0,resize_image:!1,report_upload_progress:!1,return_response_headers:!1,return_response_type:!1,return_status_code:!0,send_custom_headers:!1,select_file:!1,select_folder:!1,select_multiple:!0,send_binary_string:!1,send_browser_cookies:!0,send_multipart:!0,slice_blob:!1,stream_upload:!1,summon_file_dialog:!1,upload_filesize:!0,use_http_method:!0},r),e.preferred_caps&&(n=h.getMode(i,e.preferred_caps,n)),MXI_DEBUG&&c.debug.runtime&&c.log("\tdefault mode: %s",n),s={},o={exec:function(e,t,i,n){if(o[t]&&(s[e]||(s[e]={context:this,instance:new o[t]}),s[e].instance[i]))return s[e].instance[i].apply(this,n)},removeInstance:function(e){delete s[e]},removeAllInstances:function(){var i=this;l.each(s,function(e,t){"function"===l.typeOf(e.instance.destroy)&&e.instance.destroy.call(e.context),i.removeInstance(t)})}},l.extend(this,{initialized:!1,uid:u,type:t,mode:h.getMode(i,e.required_caps,n),shimid:u+"_container",clients:0,options:e,can:function(e,t){var i,n=arguments[2]||r;if("string"===l.typeOf(e)&&"undefined"===l.typeOf(t)&&(e=h.parseCaps(e)),"object"!==l.typeOf(e))return"function"===l.typeOf(n[e])?n[e].call(this,t):t===n[e];for(i in e)if(!this.can(i,e[i],n))return!1;return!0},getShimContainer:function(){var e,t=d.get(this.shimid);return t||(e=this.options.container?d.get(this.options.container):document.body,(t=document.createElement("div")).id=this.shimid,t.className="moxie-shim moxie-shim-"+this.type,l.extend(t.style,{position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.appendChild(t),e=null),t},getShim:function(){return o},shimExec:function(e,t){var i=[].slice.call(arguments,2);return a.getShim().exec.call(this,this.uid,e,t,i)},exec:function(e,t){var i=[].slice.call(arguments,2);return a[e]&&a[e][t]?a[e][t].apply(this,i):a.shimExec.apply(this,arguments)},destroy:function(){var e;a&&((e=d.get(this.shimid))&&e.parentNode.removeChild(e),o&&o.removeAllInstances(),this.unbindAll(),delete m[this.uid],this.uid=null,a=o=null)}}),this.mode&&e.required_caps&&!this.can(e.required_caps)&&(this.mode=!1)}return h.order="html5,html4",h.getRuntime=function(e){return m[e]||!1},h.addConstructor=function(e,t){t.prototype=i.instance,n[e]=t},h.getConstructor=function(e){return n[e]||null},h.getInfo=function(e){var t=h.getRuntime(e);return t?{uid:t.uid,type:t.type,mode:t.mode,can:function(){return t.can.apply(t,arguments)}}:null},h.parseCaps=function(e){var t={};return"string"!==l.typeOf(e)?e||{}:(l.each(e.split(","),function(e){t[e]=!0}),t)},h.can=function(e,t){var e=h.getConstructor(e);return!!e&&(t=(e=new e({required_caps:t})).mode,e.destroy(),!!t)},h.thatCan=function(e,t){var i,n=(t||h.order).split(/\s*,\s*/);for(i in n)if(h.can(n[i],e))return n[i];return null},h.getMode=function(n,e,t){var r=null;if("undefined"===l.typeOf(t)&&(t="browser"),e&&!l.isEmptyObj(n)){if(l.each(e,function(e,t){if(n.hasOwnProperty(t)){var i=n[t](e);if("string"==typeof i&&(i=[i]),r){if(!(r=l.arrayIntersect(r,i)))return MXI_DEBUG&&c.debug.runtime&&c.log("\t\t%c: %v (conflicting mode requested: %s)",t,e,i),r=!1}else r=i}MXI_DEBUG&&c.debug.runtime&&c.log("\t\t%c: %v (compatible modes: %s)",t,e,r)}),r)return-1!==l.inArray(t,r)?t:r[0];if(!1===r)return!1}return t},h.capTrue=function(){return!0},h.capFalse=function(){return!1},h.capTest=function(e){return function(){return!!e}},h}),e("moxie/runtime/RuntimeClient",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/Runtime"],function(a,u,t,c){return function(){var s;t.extend(this,{connectRuntime:function(r){var e,o=this;if("string"===t.typeOf(r)?e=r:"string"===t.typeOf(r.ruid)&&(e=r.ruid),e){if(s=c.getRuntime(e))return s.clients++,s;throw new u.RuntimeError(u.RuntimeError.NOT_INIT_ERR)}!function e(t){var i,n;t.length?(i=t.shift().toLowerCase(),(n=c.getConstructor(i))?(MXI_DEBUG&&a.debug.runtime&&(a.log("Trying runtime: %s",i),a.log(r)),(s=new n(r)).bind("Init",function(){s.initialized=!0,MXI_DEBUG&&a.debug.runtime&&a.log("Runtime '%s' initialized",s.type),setTimeout(function(){s.clients++,o.trigger("RuntimeInit",s)},1)}),s.bind("Error",function(){MXI_DEBUG&&a.debug.runtime&&a.log("Runtime '%s' failed to initialize",s.type),s.destroy(),e(t)}),MXI_DEBUG&&a.debug.runtime&&a.log("\tselected mode: %s",s.mode),s.mode?s.init():s.trigger("Error")):e(t)):(o.trigger("RuntimeError",new u.RuntimeError(u.RuntimeError.NOT_INIT_ERR)),s=null)}((r.runtime_order||c.order).split(/\s*,\s*/))},disconnectRuntime:function(){s&&--s.clients<=0&&s.destroy(),s=null},getRuntime:function(){return s&&s.uid?s:s=null},exec:function(){return s?s.exec.apply(this,arguments):null}})}}),e("moxie/file/FileInput",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/I18n","moxie/runtime/Runtime","moxie/runtime/RuntimeClient"],function(o,i,n,s,a,e,u,c,l){var d=["ready","change","cancel","mouseenter","mouseleave","mousedown","mouseup"];function t(r){MXI_DEBUG&&i.log("Instantiating FileInput...");var e,t=this;if(-1!==o.inArray(o.typeOf(r),["string","node"])&&(r={browse_button:r}),!(e=s.get(r.browse_button)))throw new a.DOMException(a.DOMException.NOT_FOUND_ERR);e={accept:[{title:u.translate("All Files"),extensions:"*"}],name:"file",multiple:!1,required_caps:!1,container:e.parentNode||document.body},"string"==typeof(r=o.extend({},e,r)).required_caps&&(r.required_caps=c.parseCaps(r.required_caps)),"string"==typeof r.accept&&(r.accept=n.mimes2extList(r.accept)),e=(e=s.get(r.container))||document.body,"static"===s.getStyle(e,"position")&&(e.style.position="relative"),e=null,l.call(t),o.extend(t,{uid:o.guid("uid_"),ruid:null,shimid:null,files:null,init:function(){t.bind("RuntimeInit",function(e,n){t.ruid=n.uid,t.shimid=n.shimid,t.bind("Ready",function(){t.trigger("Refresh")},999),t.bind("Refresh",function(){var e,t=s.get(r.browse_button),i=s.get(n.shimid);t&&(e=s.getPos(t,s.get(r.container)),t=s.getSize(t),i)&&o.extend(i.style,{top:e.y+"px",left:e.x+"px",width:t.w+"px",height:t.h+"px"})}),n.exec.call(t,"FileInput","init",r)}),t.connectRuntime(o.extend({},r,{required_caps:{select_file:!0}}))},disable:function(e){var t=this.getRuntime();t&&t.exec.call(this,"FileInput","disable","undefined"===o.typeOf(e)||e)},refresh:function(){t.trigger("Refresh")},destroy:function(){var e=this.getRuntime();e&&(e.exec.call(this,"FileInput","destroy"),this.disconnectRuntime()),"array"===o.typeOf(this.files)&&o.each(this.files,function(e){e.destroy()}),this.files=null,this.unbindAll()}}),this.handleEventProps(d)}return t.prototype=e.instance,t}),e("moxie/core/utils/Encode",[],function(){function d(e){return unescape(encodeURIComponent(e))}function m(e){return decodeURIComponent(escape(e))}return{utf8_encode:d,utf8_decode:m,atob:function(e,t){if("function"==typeof window.atob)return t?m(window.atob(e)):window.atob(e);var i,n,r,o,s,a,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",c=0,l=0,d=[];if(!e)return e;for(e+="";i=(s=u.indexOf(e.charAt(c++))<<18|u.indexOf(e.charAt(c++))<<12|(r=u.indexOf(e.charAt(c++)))<<6|(o=u.indexOf(e.charAt(c++))))>>16&255,n=s>>8&255,s=255&s,d[l++]=64==r?String.fromCharCode(i):64==o?String.fromCharCode(i,n):String.fromCharCode(i,n,s),c<e.length;);return a=d.join(""),t?m(a):a},btoa:function(e,t){if(t&&(e=d(e)),"function"==typeof window.btoa)return window.btoa(e);var i,n,r,o,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,u=0,t="",c=[];if(!e)return e;for(;i=(o=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>12&63,n=o>>6&63,r=63&o,c[u++]=s.charAt(o>>18&63)+s.charAt(i)+s.charAt(n)+s.charAt(r),a<e.length;);var t=c.join(""),l=e.length%3;return(l?t.slice(0,l-3):t)+"===".slice(l||3)}}}),e("moxie/file/Blob",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient"],function(o,i,n){var s={};return function r(e,t){n.call(this),e&&this.connectRuntime(e),t?"string"===o.typeOf(t)&&(t={data:t}):t={},o.extend(this,{uid:t.uid||o.guid("uid_"),ruid:e,size:t.size||0,type:t.type||"",slice:function(e,t,i){return this.isDetached()?function(e,t,i){var n=s[this.uid];return"string"===o.typeOf(n)&&n.length?((i=new r(null,{type:i,size:t-e})).detach(n.substr(e,i.size)),i):null}.apply(this,arguments):this.getRuntime().exec.call(this,"Blob","slice",this.getSource(),e,t,i)},getSource:function(){return s[this.uid]||null},detach:function(e){var t;this.ruid&&(this.getRuntime().exec.call(this,"Blob","destroy"),this.disconnectRuntime(),this.ruid=null),"data:"==(e=e||"").substr(0,5)&&(t=e.indexOf(";base64,"),this.type=e.substring(5,t),e=i.atob(e.substring(t+8))),this.size=e.length,s[this.uid]=e},isDetached:function(){return!this.ruid&&"string"===o.typeOf(s[this.uid])},destroy:function(){this.detach(),delete s[this.uid]}}),t.data?this.detach(t.data):s[this.uid]=t}}),e("moxie/file/File",["moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/file/Blob"],function(r,o,s){function e(e,t){var i,n;t=t||{},s.apply(this,arguments),this.type||(this.type=o.getFileMime(t.name)),t.name?n=(n=t.name.replace(/\\/g,"/")).substr(n.lastIndexOf("/")+1):this.type&&(i=this.type.split("/")[0],n=r.guid((""!==i?i:"file")+"_"),o.extensions[this.type])&&(n+="."+o.extensions[this.type][0]),r.extend(this,{name:n||r.guid("file_"),relativePath:"",lastModifiedDate:t.lastModifiedDate||(new Date).toLocaleString()})}return e.prototype=s.prototype,e}),e("moxie/file/FileDrop",["moxie/core/I18n","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/core/utils/Env","moxie/file/File","moxie/runtime/RuntimeClient","moxie/core/EventTarget","moxie/core/utils/Mime"],function(t,r,e,o,s,i,a,n,u){var c=["ready","dragenter","dragleave","drop","error"];function l(i){MXI_DEBUG&&s.log("Instantiating FileDrop...");var e,n=this;"string"==typeof i&&(i={drop_zone:i}),e={accept:[{title:t.translate("All Files"),extensions:"*"}],required_caps:{drag_and_drop:!0}},(i="object"==typeof i?o.extend({},e,i):e).container=r.get(i.drop_zone)||document.body,"static"===r.getStyle(i.container,"position")&&(i.container.style.position="relative"),"string"==typeof i.accept&&(i.accept=u.mimes2extList(i.accept)),a.call(n),o.extend(n,{uid:o.guid("uid_"),ruid:null,files:null,init:function(){n.bind("RuntimeInit",function(e,t){n.ruid=t.uid,t.exec.call(n,"FileDrop","init",i),n.dispatchEvent("ready")}),n.connectRuntime(i)},destroy:function(){var e=this.getRuntime();e&&(e.exec.call(this,"FileDrop","destroy"),this.disconnectRuntime()),this.files=null,this.unbindAll()}}),this.handleEventProps(c)}return l.prototype=n.instance,l}),e("moxie/file/FileReader",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/Exceptions","moxie/core/EventTarget","moxie/file/Blob","moxie/runtime/RuntimeClient"],function(e,n,r,t,o,i){var s=["loadstart","progress","load","abort","error","loadend"];function a(){function t(e,t){if(this.trigger("loadstart"),this.readyState===a.LOADING)this.trigger("error",new r.DOMException(r.DOMException.INVALID_STATE_ERR)),this.trigger("loadend");else if(t instanceof o)if(this.result=null,this.readyState=a.LOADING,t.isDetached()){var i=t.getSource();switch(e){case"readAsText":case"readAsBinaryString":this.result=i;break;case"readAsDataURL":this.result="data:"+t.type+";base64,"+n.btoa(i)}this.readyState=a.DONE,this.trigger("load"),this.trigger("loadend")}else this.connectRuntime(t.ruid),this.exec("FileReader","read",e,t);else this.trigger("error",new r.DOMException(r.DOMException.NOT_FOUND_ERR)),this.trigger("loadend")}i.call(this),e.extend(this,{uid:e.guid("uid_"),readyState:a.EMPTY,result:null,error:null,readAsBinaryString:function(e){t.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){t.call(this,"readAsDataURL",e)},readAsText:function(e){t.call(this,"readAsText",e)},abort:function(){this.result=null,-1===e.inArray(this.readyState,[a.EMPTY,a.DONE])&&(this.readyState===a.LOADING&&(this.readyState=a.DONE),this.exec("FileReader","abort"),this.trigger("abort"),this.trigger("loadend"))},destroy:function(){this.abort(),this.exec("FileReader","destroy"),this.disconnectRuntime(),this.unbindAll()}}),this.handleEventProps(s),this.bind("Error",function(e,t){this.readyState=a.DONE,this.error=t},999),this.bind("Load",function(e){this.readyState=a.DONE},999)}return a.EMPTY=0,a.LOADING=1,a.DONE=2,a.prototype=t.instance,a}),e("moxie/core/utils/Url",[],function(){function s(e,t){for(var i=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],n=i.length,r={},o=/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/.exec(e||"");n--;)o[n]&&(r[i[n]]=o[n]);return r.scheme||(t&&"string"!=typeof t||(t=s(t||document.location.href)),r.scheme=t.scheme,r.host=t.host,r.port=t.port,e="",/^[^\/]/.test(r.path)&&(e=t.path,e=/\/[^\/]*\.[^\/]*$/.test(e)?e.replace(/\/[^\/]+$/,"/"):e.replace(/\/?$/,"/")),r.path=e+(r.path||"")),r.port||(r.port={http:80,https:443}[r.scheme]||80),r.port=parseInt(r.port,10),r.path||(r.path="/"),delete r.source,r}return{parseUrl:s,resolveUrl:function(e){e="object"==typeof e?e:s(e);return e.scheme+"://"+e.host+(e.port!=={http:80,https:443}[e.scheme]?":"+e.port:"")+e.path+(e.query||"")},hasSameOrigin:function(e){function t(e){return[e.scheme,e.host,e.port].join("/")}return"string"==typeof e&&(e=s(e)),t(s())===t(e)}}}),e("moxie/runtime/RuntimeTarget",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i){function n(){this.uid=e.guid("uid_"),t.call(this),this.destroy=function(){this.disconnectRuntime(),this.unbindAll()}}return n.prototype=i.instance,n}),e("moxie/file/FileReaderSync",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/utils/Encode"],function(e,i,a){return function(){function t(e,t){var i;if(!t.isDetached())return i=this.connectRuntime(t.ruid).exec.call(this,"FileReaderSync","read",e,t),this.disconnectRuntime(),i;var n=t.getSource();switch(e){case"readAsBinaryString":return n;case"readAsDataURL":return"data:"+t.type+";base64,"+a.btoa(n);case"readAsText":for(var r="",o=0,s=n.length;o<s;o++)r+=String.fromCharCode(n[o]);return r}}i.call(this),e.extend(this,{uid:e.guid("uid_"),readAsBinaryString:function(e){return t.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){return t.call(this,"readAsDataURL",e)},readAsText:function(e){return t.call(this,"readAsText",e)}})}}),e("moxie/xhr/FormData",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/file/Blob"],function(e,s,a){return function(){var r,o=[];s.extend(this,{append:function(i,e){var n=this,t=s.typeOf(e);e instanceof a?r={name:i,value:e}:"array"===t?(i+="[]",s.each(e,function(e){n.append(i,e)})):"object"===t?s.each(e,function(e,t){n.append(i+"["+t+"]",e)}):"null"===t||"undefined"===t||"number"===t&&isNaN(e)?n.append(i,"false"):o.push({name:i,value:e.toString()})},hasBlob:function(){return!!this.getBlob()},getBlob:function(){return r&&r.value||null},getBlobName:function(){return r&&r.name||null},each:function(t){s.each(o,function(e){t(e.value,e.name)}),r&&t(r.value,r.name)},destroy:function(){r=null,o=[]}})}}),e("moxie/xhr/XMLHttpRequest",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/utils/Encode","moxie/core/utils/Url","moxie/runtime/Runtime","moxie/runtime/RuntimeTarget","moxie/file/Blob","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/core/utils/Env","moxie/core/utils/Mime"],function(_,b,e,A,I,T,S,r,t,O,D,N){var C={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Reserved",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",510:"Not Extended"};function M(){this.uid=_.guid("uid_")}M.prototype=e.instance;var L=["loadstart","progress","abort","error","load","timeout","loadend"];function F(){var o,s,a,u,c,t,i=this,n={timeout:0,readyState:F.UNSENT,withCredentials:!1,status:0,statusText:"",responseType:"",responseXML:null,responseText:null,response:null},l=!0,d={},m=null,h=null,f=!1,p=!1,g=!1,x=!1,E=!1,y=!1,w={},v="";function R(e,t){if(n.hasOwnProperty(e))return 1===arguments.length?(D.can("define_property")?n:i)[e]:void(D.can("define_property")?n[e]=t:i[e]=t)}_.extend(this,n,{uid:_.guid("uid_"),upload:new M,open:function(e,t,i,n,r){if(!e||!t)throw new b.DOMException(b.DOMException.SYNTAX_ERR);if(/[\u0100-\uffff]/.test(e)||A.utf8_encode(e)!==e)throw new b.DOMException(b.DOMException.SYNTAX_ERR);if(~_.inArray(e.toUpperCase(),["CONNECT","DELETE","GET","HEAD","OPTIONS","POST","PUT","TRACE","TRACK"])&&(s=e.toUpperCase()),~_.inArray(s,["CONNECT","TRACE","TRACK"]))throw new b.DOMException(b.DOMException.SECURITY_ERR);if(t=A.utf8_encode(t),e=I.parseUrl(t),y=I.hasSameOrigin(e),o=I.resolveUrl(t),(n||r)&&!y)throw new b.DOMException(b.DOMException.INVALID_ACCESS_ERR);if(a=n||e.user,u=r||e.pass,!1===(l=i||!0)&&(R("timeout")||R("withCredentials")||""!==R("responseType")))throw new b.DOMException(b.DOMException.INVALID_ACCESS_ERR);f=!l,p=!1,d={},function(){R("responseText",""),R("responseXML",null),R("response",null),R("status",0),R("statusText",""),0}.call(this),R("readyState",F.OPENED),this.dispatchEvent("readystatechange")},setRequestHeader:function(e,t){if(R("readyState")!==F.OPENED||p)throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);if(/[\u0100-\uffff]/.test(e)||A.utf8_encode(e)!==e)throw new b.DOMException(b.DOMException.SYNTAX_ERR);return e=_.trim(e).toLowerCase(),!~_.inArray(e,["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","content-transfer-encoding","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"])&&!/^(proxy\-|sec\-)/.test(e)&&(d[e]?d[e]+=", "+t:d[e]=t,!0)},getAllResponseHeaders:function(){return v||""},getResponseHeader:function(e){return e=e.toLowerCase(),!E&&!~_.inArray(e,["set-cookie","set-cookie2"])&&v&&""!==v&&(t||(t={},_.each(v.split(/\r\n/),function(e){e=e.split(/:\s+/);2===e.length&&(e[0]=_.trim(e[0]),t[e[0].toLowerCase()]={header:e[0],value:_.trim(e[1])})})),t.hasOwnProperty(e))?t[e].header+": "+t[e].value:null},overrideMimeType:function(e){var t,i;if(~_.inArray(R("readyState"),[F.LOADING,F.DONE]))throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);if(e=_.trim(e.toLowerCase()),/;/.test(e)&&(t=e.match(/^([^;]+)(?:;\scharset\=)?(.*)$/))&&(e=t[1],t[2])&&(i=t[2]),!N.mimes[e])throw new b.DOMException(b.DOMException.SYNTAX_ERR);0},send:function(e,t){if(w="string"===_.typeOf(t)?{ruid:t}:t||{},this.readyState!==F.OPENED||p)throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);e instanceof r?(w.ruid=e.ruid,h=e.type||"application/octet-stream"):e instanceof O?e.hasBlob()&&(t=e.getBlob(),w.ruid=t.ruid,h=t.type||"application/octet-stream"):"string"==typeof e&&(m="UTF-8",h="text/plain;charset=UTF-8",e=A.utf8_encode(e)),this.withCredentials||(this.withCredentials=w.required_caps&&w.required_caps.send_browser_cookies&&!y),g=!f&&this.upload.hasEventListener(),E=!1,x=!e,f||(p=!0),function(e){var i=this;function n(){c&&(c.destroy(),c=null),i.dispatchEvent("loadend"),i=null}function r(t){c.bind("LoadStart",function(e){R("readyState",F.LOADING),i.dispatchEvent("readystatechange"),i.dispatchEvent(e),g&&i.upload.dispatchEvent(e)}),c.bind("Progress",function(e){R("readyState")!==F.LOADING&&(R("readyState",F.LOADING),i.dispatchEvent("readystatechange")),i.dispatchEvent(e)}),c.bind("UploadProgress",function(e){g&&i.upload.dispatchEvent({type:"progress",lengthComputable:!1,total:e.total,loaded:e.loaded})}),c.bind("Load",function(e){R("readyState",F.DONE),R("status",Number(t.exec.call(c,"XMLHttpRequest","getStatus")||0)),R("statusText",C[R("status")]||""),R("response",t.exec.call(c,"XMLHttpRequest","getResponse",R("responseType"))),~_.inArray(R("responseType"),["text",""])?R("responseText",R("response")):"document"===R("responseType")&&R("responseXML",R("response")),v=t.exec.call(c,"XMLHttpRequest","getAllResponseHeaders"),i.dispatchEvent("readystatechange"),0<R("status")?(g&&i.upload.dispatchEvent(e),i.dispatchEvent(e)):(E=!0,i.dispatchEvent("error")),n()}),c.bind("Abort",function(e){i.dispatchEvent(e),n()}),c.bind("Error",function(e){E=!0,R("readyState",F.DONE),i.dispatchEvent("readystatechange"),x=!0,i.dispatchEvent(e),n()}),t.exec.call(c,"XMLHttpRequest","send",{url:o,method:s,async:l,user:a,password:u,headers:d,mimeType:h,encoding:m,responseType:i.responseType,withCredentials:i.withCredentials,options:w},e)}(new Date).getTime(),c=new S,"string"==typeof w.required_caps&&(w.required_caps=T.parseCaps(w.required_caps));w.required_caps=_.extend({},w.required_caps,{return_response_type:i.responseType}),e instanceof O&&(w.required_caps.send_multipart=!0);_.isEmptyObj(d)||(w.required_caps.send_custom_headers=!0);y||(w.required_caps.do_cors=!0);w.ruid?r(c.connectRuntime(w)):(c.bind("RuntimeInit",function(e,t){r(t)}),c.bind("RuntimeError",function(e,t){i.dispatchEvent("RuntimeError",t)}),c.connectRuntime(w))}.call(this,e)},abort:function(){if(f=!(E=!0),~_.inArray(R("readyState"),[F.UNSENT,F.OPENED,F.DONE]))R("readyState",F.UNSENT);else{if(R("readyState",F.DONE),p=!1,!c)throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);c.getRuntime().exec.call(c,"XMLHttpRequest","abort",x),x=!0}},destroy:function(){c&&("function"===_.typeOf(c.destroy)&&c.destroy(),c=null),this.unbindAll(),this.upload&&(this.upload.unbindAll(),this.upload=null)}}),this.handleEventProps(L.concat(["readystatechange"])),this.upload.handleEventProps(L)}return F.UNSENT=0,F.OPENED=1,F.HEADERS_RECEIVED=2,F.LOADING=3,F.DONE=4,F.prototype=e.instance,F}),e("moxie/runtime/Transporter",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(m,t,e,i){function h(){var o,n,s,a,r,u;function c(){a=r=0,s=this.result=null}function l(e,t){var i=this;n=t,i.bind("TransportingProgress",function(e){(r=e.loaded)<a&&-1===m.inArray(i.state,[h.IDLE,h.DONE])&&d.call(i)},999),i.bind("TransportingComplete",function(){r=a,i.state=h.DONE,s=null,i.result=n.exec.call(i,"Transporter","getAsBlob",e||"")},999),i.state=h.BUSY,i.trigger("TransportingStarted"),d.call(i)}function d(){var e=a-r;e<u&&(u=e),e=t.btoa(s.substr(r,u)),n.exec.call(this,"Transporter","receive",e,a)}e.call(this),m.extend(this,{uid:m.guid("uid_"),state:h.IDLE,result:null,transport:function(e,i,t){var n,r=this;t=m.extend({chunk_size:204798},t),(o=t.chunk_size%3)&&(t.chunk_size+=3-o),u=t.chunk_size,c.call(this),a=(s=e).length,"string"===m.typeOf(t)||t.ruid?l.call(r,i,this.connectRuntime(t)):(n=function(e,t){r.unbind("RuntimeInit",n),l.call(r,i,t)},this.bind("RuntimeInit",n),this.connectRuntime(t))},abort:function(){this.state=h.IDLE,n&&(n.exec.call(this,"Transporter","clear"),this.trigger("TransportingAborted")),c.call(this)},destroy:function(){this.unbindAll(),n=null,this.disconnectRuntime(),c.call(this)}})}return h.IDLE=0,h.BUSY=1,h.DONE=2,h.prototype=i.instance,h}),e("moxie/image/Image",["moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/file/FileReaderSync","moxie/xhr/XMLHttpRequest","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/runtime/Transporter","moxie/core/utils/Env","moxie/core/EventTarget","moxie/file/Blob","moxie/file/File","moxie/core/utils/Encode"],function(a,n,u,e,o,s,t,c,l,i,d,m,h){var f=["progress","load","error","resize","embedded"];function p(){function i(e){var t=a.typeOf(e);try{if(e instanceof p){if(!e.size)throw new u.DOMException(u.DOMException.INVALID_STATE_ERR);!function(e,t){var i=this.connectRuntime(e.ruid);this.ruid=i.uid,i.exec.call(this,"Image","loadFromImage",e,"undefined"===a.typeOf(t)||t)}.apply(this,arguments)}else if(e instanceof d){if(!~a.inArray(e.type,["image/jpeg","image/png"]))throw new u.ImageError(u.ImageError.WRONG_FORMAT);r.apply(this,arguments)}else if(-1!==a.inArray(t,["blob","file"]))i.call(this,new m(null,e),arguments[1]);else if("string"===t)"data:"===e.substr(0,5)?i.call(this,new d(null,{data:e}),arguments[1]):function(e,t){var i,n=this;(i=new o).open("get",e),i.responseType="blob",i.onprogress=function(e){n.trigger(e)},i.onload=function(){r.call(n,i.response,!0)},i.onerror=function(e){n.trigger(e)},i.onloadend=function(){i.destroy()},i.bind("RuntimeError",function(e,t){n.trigger("RuntimeError",t)}),i.send(null,t)}.apply(this,arguments);else{if("node"!==t||"img"!==e.nodeName.toLowerCase())throw new u.DOMException(u.DOMException.TYPE_MISMATCH_ERR);i.call(this,e.src,arguments[1])}}catch(e){this.trigger("error",e.code)}}function r(t,e){var i=this;function n(e){i.ruid=e.uid,e.exec.call(i,"Image","loadFromBlob",t)}i.name=t.name||"",t.isDetached()?(this.bind("RuntimeInit",function(e,t){n(t)}),e&&"string"==typeof e.required_caps&&(e.required_caps=s.parseCaps(e.required_caps)),this.connectRuntime(a.extend({required_caps:{access_image_binary:!0,resize_image:!0}},e))):n(this.connectRuntime(t.ruid))}t.call(this),a.extend(this,{uid:a.guid("uid_"),ruid:null,name:"",size:0,width:0,height:0,type:"",meta:{},clone:function(){this.load.apply(this,arguments)},load:function(){i.apply(this,arguments)},downsize:function(e){var t={width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90,crop:!1,preserveHeaders:!0,resample:!1};e="object"==typeof e?a.extend(t,e):a.extend(t,{width:arguments[0],height:arguments[1],crop:arguments[2],preserveHeaders:arguments[3]});try{if(!this.size)throw new u.DOMException(u.DOMException.INVALID_STATE_ERR);if(this.width>p.MAX_RESIZE_WIDTH||this.height>p.MAX_RESIZE_HEIGHT)throw new u.ImageError(u.ImageError.MAX_RESOLUTION_ERR);this.exec("Image","downsize",e.width,e.height,e.crop,e.preserveHeaders)}catch(e){this.trigger("error",e.code)}},crop:function(e,t,i){this.downsize(e,t,!0,i)},getAsCanvas:function(){if(l.can("create_canvas"))return this.connectRuntime(this.ruid).exec.call(this,"Image","getAsCanvas");throw new u.RuntimeError(u.RuntimeError.NOT_SUPPORTED_ERR)},getAsBlob:function(e,t){if(this.size)return this.exec("Image","getAsBlob",e||"image/jpeg",t||90);throw new u.DOMException(u.DOMException.INVALID_STATE_ERR)},getAsDataURL:function(e,t){if(this.size)return this.exec("Image","getAsDataURL",e||"image/jpeg",t||90);throw new u.DOMException(u.DOMException.INVALID_STATE_ERR)},getAsBinaryString:function(e,t){e=this.getAsDataURL(e,t);return h.atob(e.substring(e.indexOf("base64,")+7))},embed:function(r,e){var o,s=this;e=a.extend({width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90},e||{});try{if(!(r=n.get(r)))throw new u.DOMException(u.DOMException.INVALID_NODE_TYPE_ERR);if(!this.size)throw new u.DOMException(u.DOMException.INVALID_STATE_ERR);this.width>p.MAX_RESIZE_WIDTH||this.height;var t=new p;return t.bind("Resize",function(){!function(e,t){var i=this;if(l.can("create_canvas")){var n=i.getAsCanvas();if(n)return r.appendChild(n),i.destroy(),void s.trigger("embedded")}if(!(n=i.getAsDataURL(e,t)))throw new u.ImageError(u.ImageError.WRONG_FORMAT);l.can("use_data_uri_of",n.length)?(r.innerHTML='<img src="'+n+'" width="'+i.width+'" height="'+i.height+'" />',i.destroy(),s.trigger("embedded")):((t=new c).bind("TransportingComplete",function(){o=s.connectRuntime(this.result.ruid),s.bind("Embedded",function(){a.extend(o.getShimContainer().style,{top:"0px",left:"0px",width:i.width+"px",height:i.height+"px"}),o=null},999),o.exec.call(s,"ImageView","display",this.result.uid,width,height),i.destroy()}),t.transport(h.atob(n.substring(n.indexOf("base64,")+7)),e,{required_caps:{display_media:!0},runtime_order:"flash,silverlight",container:r}))}.call(this,e.type,e.quality)}),t.bind("Load",function(){t.downsize(e)}),this.meta.thumb&&this.meta.thumb.width>=e.width&&this.meta.thumb.height>=e.height?t.load(this.meta.thumb.data):t.clone(this,!1),t}catch(e){this.trigger("error",e.code)}},destroy:function(){this.ruid&&(this.getRuntime().exec.call(this,"Image","destroy"),this.disconnectRuntime()),this.unbindAll()}}),this.handleEventProps(f),this.bind("Load Resize",function(){!function(e){e=e||this.exec("Image","getInfo");this.size=e.size,this.width=e.width,this.height=e.height,this.type=e.type,this.meta=e.meta,""===this.name&&(this.name=e.name)}.call(this)},999)}return p.MAX_RESIZE_WIDTH=8192,p.MAX_RESIZE_HEIGHT=8192,p.prototype=i.instance,p}),e("moxie/runtime/html5/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(s,e,a,u){var c={};return a.addConstructor("html5",function(e){var t,i=this,n=a.capTest,r=a.capTrue,o=s.extend({access_binary:n(window.FileReader||window.File&&window.File.getAsDataURL),access_image_binary:function(){return i.can("access_binary")&&!!c.Image},display_media:n(u.can("create_canvas")||u.can("use_data_uri_over32kb")),do_cors:n(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest),drag_and_drop:n(("draggable"in(o=document.createElement("div"))||"ondragstart"in o&&"ondrop"in o)&&("IE"!==u.browser||u.verComp(u.version,9,">"))),filter_by_extension:n("Chrome"===u.browser&&u.verComp(u.version,28,">=")||"IE"===u.browser&&u.verComp(u.version,10,">=")||"Safari"===u.browser&&u.verComp(u.version,7,">=")),return_response_headers:r,return_response_type:function(e){return!("json"!==e||!window.JSON)||u.can("return_response_type",e)},return_status_code:r,report_upload_progress:n(window.XMLHttpRequest&&(new XMLHttpRequest).upload),resize_image:function(){return i.can("access_binary")&&u.can("create_canvas")},select_file:function(){return u.can("use_fileinput")&&window.File},select_folder:function(){return i.can("select_file")&&"Chrome"===u.browser&&u.verComp(u.version,21,">=")},select_multiple:function(){return i.can("select_file")&&!("Safari"===u.browser&&"Windows"===u.os)&&!("iOS"===u.os&&u.verComp(u.osVersion,"7.0.0",">")&&u.verComp(u.osVersion,"8.0.0","<"))},send_binary_string:n(window.XMLHttpRequest&&((new XMLHttpRequest).sendAsBinary||window.Uint8Array&&window.ArrayBuffer)),send_custom_headers:n(window.XMLHttpRequest),send_multipart:function(){return!!(window.XMLHttpRequest&&(new XMLHttpRequest).upload&&window.FormData)||i.can("send_binary_string")},slice_blob:n(window.File&&(File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice)),stream_upload:function(){return i.can("slice_blob")&&i.can("send_multipart")},summon_file_dialog:function(){return i.can("select_file")&&("Firefox"===u.browser&&u.verComp(u.version,4,">=")||"Opera"===u.browser&&u.verComp(u.version,12,">=")||"IE"===u.browser&&u.verComp(u.version,10,">=")||!!~s.inArray(u.browser,["Chrome","Safari"]))},upload_filesize:r},arguments[2]);a.call(this,e,arguments[1]||"html5",o),s.extend(this,{init:function(){this.trigger("Init")},destroy:(t=this.destroy,function(){t.call(i),t=i=null})}),s.extend(this.getShim(),c)}),c}),e("moxie/core/utils/Events",["moxie/core/utils/Basic"],function(o){var s={},a="moxie_"+o.guid();function u(){this.returnValue=!1}function c(){this.cancelBubble=!0}function r(t,e,i){if(e=e.toLowerCase(),t[a]&&s[t[a]]&&s[t[a]][e]){for(var n,r=(n=s[t[a]][e]).length-1;0<=r&&(n[r].orig!==i&&n[r].key!==i||(t.removeEventListener?t.removeEventListener(e,n[r].func,!1):t.detachEvent&&t.detachEvent("on"+e,n[r].func),n[r].orig=null,n[r].func=null,n.splice(r,1),void 0===i));r--);if(n.length||delete s[t[a]][e],o.isEmptyObj(s[t[a]])){delete s[t[a]];try{delete t[a]}catch(e){t[a]=void 0}}}}return{addEvent:function(e,t,i,n){var r;t=t.toLowerCase(),e.addEventListener?e.addEventListener(t,r=i,!1):e.attachEvent&&e.attachEvent("on"+t,r=function(){var e=window.event;e.target||(e.target=e.srcElement),e.preventDefault=u,e.stopPropagation=c,i(e)}),e[a]||(e[a]=o.guid()),s.hasOwnProperty(e[a])||(s[e[a]]={}),(e=s[e[a]]).hasOwnProperty(t)||(e[t]=[]),e[t].push({func:r,orig:i,key:n})},removeEvent:r,removeAllEvents:function(i,n){i&&i[a]&&o.each(s[i[a]],function(e,t){r(i,t,n)})}}}),e("moxie/runtime/html5/file/FileInput",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,a,u,c,l,d,m){return e.FileInput=function(){var s;u.extend(this,{init:function(e){var t,i,n,r=this,o=r.getRuntime(),e=(s=e).accept.mimes||d.extList2mimes(s.accept,o.can("filter_by_extension"));(t=o.getShimContainer()).innerHTML='<input id="'+o.uid+'" type="file" style="font-size:999px;opacity:0;"'+(s.multiple&&o.can("select_multiple")?"multiple":"")+(s.directory&&o.can("select_folder")?"webkitdirectory directory":"")+(e?' accept="'+e.join(",")+'"':"")+" />",e=c.get(o.uid),u.extend(e.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),i=c.get(s.browse_button),o.can("summon_file_dialog")&&("static"===c.getStyle(i,"position")&&(i.style.position="relative"),n=parseInt(c.getStyle(i,"z-index"),10)||1,i.style.zIndex=n,t.style.zIndex=n-1,l.addEvent(i,"click",function(e){var t=c.get(o.uid);t&&!t.disabled&&t.click(),e.preventDefault()},r.uid)),n=o.can("summon_file_dialog")?i:t,l.addEvent(n,"mouseover",function(){r.trigger("mouseenter")},r.uid),l.addEvent(n,"mouseout",function(){r.trigger("mouseleave")},r.uid),l.addEvent(n,"mousedown",function(){r.trigger("mousedown")},r.uid),l.addEvent(c.get(s.container),"mouseup",function(){r.trigger("mouseup")},r.uid),e.onchange=function e(t){var i;r.files=[],u.each(this.files,function(e){var t="";if(s.directory&&"."==e.name)return!0;e.webkitRelativePath&&(t="/"+e.webkitRelativePath.replace(/^\//,"")),(e=new a(o.uid,e)).relativePath=t,r.files.push(e)}),"IE"!==m.browser&&"IEMobile"!==m.browser?this.value="":(i=this.cloneNode(!0),this.parentNode.replaceChild(i,this),i.onchange=e),r.files.length&&r.trigger("change")},r.trigger({type:"ready",async:!0})},disable:function(e){var t=this.getRuntime();(t=c.get(t.uid))&&(t.disabled=!!e)},destroy:function(){var e=this.getRuntime(),t=e.getShim(),e=e.getShimContainer();l.removeAllEvents(e,this.uid),l.removeAllEvents(s&&c.get(s.container),this.uid),l.removeAllEvents(s&&c.get(s.browse_button),this.uid),e&&(e.innerHTML=""),t.removeInstance(this.uid),s=null}})}}),e("moxie/runtime/html5/file/Blob",["moxie/runtime/html5/Runtime","moxie/file/Blob"],function(e,t){return e.Blob=function(){this.slice=function(){return new t(this.getRuntime().uid,function(t,i,n){var e;if(!window.File.prototype.slice)return(e=window.File.prototype.webkitSlice||window.File.prototype.mozSlice)?e.call(t,i,n):null;try{return t.slice(),t.slice(i,n)}catch(e){return t.slice(i,n-i)}}.apply(this,arguments))}}}),e("moxie/runtime/html5/file/FileDrop",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime"],function(e,r,l,i,d,m){return e.FileDrop=function(){var t,n,o=[],s=[];function a(e){if(e.dataTransfer&&e.dataTransfer.types)return e=l.toArray(e.dataTransfer.types||[]),-1!==l.inArray("Files",e)||-1!==l.inArray("public.file-url",e)||-1!==l.inArray("application/x-moz-file",e)}function u(e,t){var i;i=e,s.length&&(i=m.getFileExtension(i.name))&&-1===l.inArray(i,s)||((i=new r(n,e)).relativePath=t||"",o.push(i))}function c(e,t){var i=[];l.each(e,function(s){i.push(function(e){{var t,n,r;(o=e,(i=s).isFile)?i.file(function(e){u(e,i.fullPath),o()},function(){o()}):i.isDirectory?(t=o,n=[],r=(e=i).createReader(),function t(i){r.readEntries(function(e){e.length?([].push.apply(n,e),t(i)):i()},i)}(function(){c(n,t)})):o()}var i,o})}),l.inSeries(i,function(){t()})}l.extend(this,{init:function(e){var r=this;t=e,n=r.ruid,s=function(e){for(var t=[],i=0;i<e.length;i++)[].push.apply(t,e[i].extensions.split(/\s*,\s*/));return-1===l.inArray("*",t)?t:[]}(t.accept),e=t.container,d.addEvent(e,"dragover",function(e){a(e)&&(e.preventDefault(),e.dataTransfer.dropEffect="copy")},r.uid),d.addEvent(e,"drop",function(e){var t,i,n;a(e)&&(e.preventDefault(),o=[],e.dataTransfer.items&&e.dataTransfer.items[0].webkitGetAsEntry?(t=e.dataTransfer.items,i=function(){r.files=o,r.trigger("drop")},n=[],l.each(t,function(e){var t=e.webkitGetAsEntry();t&&(t.isFile?u(e.getAsFile(),t.fullPath):n.push(t))}),n.length?c(n,i):i()):(l.each(e.dataTransfer.files,function(e){u(e)}),r.files=o,r.trigger("drop")))},r.uid),d.addEvent(e,"dragenter",function(e){r.trigger("dragenter")},r.uid),d.addEvent(e,"dragleave",function(e){r.trigger("dragleave")},r.uid)},destroy:function(){d.removeAllEvents(t&&i.get(t.container),this.uid),n=o=s=t=null}})}}),e("moxie/runtime/html5/file/FileReader",["moxie/runtime/html5/Runtime","moxie/core/utils/Encode","moxie/core/utils/Basic"],function(e,o,s){return e.FileReader=function(){var n,r=!1;s.extend(this,{read:function(e,t){var i=this;i.result="",(n=new window.FileReader).addEventListener("progress",function(e){i.trigger(e)}),n.addEventListener("load",function(e){var t;i.result=r?(t=n.result,o.atob(t.substring(t.indexOf("base64,")+7))):n.result,i.trigger(e)}),n.addEventListener("error",function(e){i.trigger(e,n.error)}),n.addEventListener("loadend",function(e){n=null,i.trigger(e)}),"function"===s.typeOf(n[e])?(r=!1,n[e](t.getSource())):"readAsBinaryString"===e&&(r=!0,n.readAsDataURL(t.getSource()))},abort:function(){n&&n.abort()},destroy:function(){n=null}})}}),e("moxie/runtime/html5/xhr/XMLHttpRequest",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/core/utils/Url","moxie/file/File","moxie/file/Blob","moxie/xhr/FormData","moxie/core/Exceptions","moxie/core/utils/Env"],function(e,m,u,h,f,p,g,x,E){return e.XMLHttpRequest=function(){var c,l,d=this;m.extend(this,{send:function(e,t){var i,n=this,r="Mozilla"===E.browser&&E.verComp(E.version,4,">=")&&E.verComp(E.version,7,"<"),o="Android Browser"===E.browser,s=!1;if(l=e.url.replace(/^.+?\/([\w\-\.]+)$/,"$1").toLowerCase(),(c=!window.XMLHttpRequest||"IE"===E.browser&&E.verComp(E.version,8,"<")?function(){for(var e=["Msxml2.XMLHTTP.6.0","Microsoft.XMLHTTP"],t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(e){}}():new window.XMLHttpRequest).open(e.method,e.url,e.async,e.user,e.password),t instanceof p)t.isDetached()&&(s=!0),t=t.getSource();else if(t instanceof g){if(t.hasBlob())if(t.getBlob().isDetached())t=function(e){var i="----moxieboundary"+(new Date).getTime(),n="\r\n",r="";if(this.getRuntime().can("send_binary_string"))return c.setRequestHeader("Content-Type","multipart/form-data; boundary="+i),e.each(function(e,t){e instanceof p?r+="--"+i+n+'Content-Disposition: form-data; name="'+t+'"; filename="'+unescape(encodeURIComponent(e.name||"blob"))+'"'+n+"Content-Type: "+(e.type||"application/octet-stream")+n+n+e.getSource()+n:r+="--"+i+n+'Content-Disposition: form-data; name="'+t+'"'+n+n+unescape(encodeURIComponent(e))+n}),r+="--"+i+"--"+n;throw new x.RuntimeError(x.RuntimeError.NOT_SUPPORTED_ERR)}.call(n,t),s=!0;else if((r||o)&&"blob"===m.typeOf(t.getBlob().getSource())&&window.FileReader)return void function(e,t){var i,n,r=this;i=t.getBlob().getSource(),(n=new window.FileReader).onload=function(){t.append(t.getBlobName(),new p(null,{type:i.type,data:n.result})),d.send.call(r,e,t)},n.readAsBinaryString(i)}.call(n,e,t);t instanceof g&&(i=new window.FormData,t.each(function(e,t){e instanceof p?i.append(t,e.getSource()):i.append(t,e)}),t=i)}if(c.upload?(e.withCredentials&&(c.withCredentials=!0),c.addEventListener("load",function(e){n.trigger(e)}),c.addEventListener("error",function(e){n.trigger(e)}),c.addEventListener("progress",function(e){n.trigger(e)}),c.upload.addEventListener("progress",function(e){n.trigger({type:"UploadProgress",loaded:e.loaded,total:e.total})})):c.onreadystatechange=function(){switch(c.readyState){case 1:case 2:break;case 3:var t,i;try{h.hasSameOrigin(e.url)&&(t=c.getResponseHeader("Content-Length")||0),c.responseText&&(i=c.responseText.length)}catch(e){t=i=0}n.trigger({type:"progress",lengthComputable:!!t,total:parseInt(t,10),loaded:i});break;case 4:c.onreadystatechange=function(){},0===c.status?n.trigger("error"):n.trigger("load")}},m.isEmptyObj(e.headers)||m.each(e.headers,function(e,t){c.setRequestHeader(t,e)}),""!==e.responseType&&"responseType"in c&&("json"!==e.responseType||E.can("return_response_type","json")?c.responseType=e.responseType:c.responseType="text"),s)if(c.sendAsBinary)c.sendAsBinary(t);else{for(var a=new Uint8Array(t.length),u=0;u<t.length;u++)a[u]=255&t.charCodeAt(u);c.send(a.buffer)}else c.send(t);n.trigger("loadstart")},getStatus:function(){try{if(c)return c.status}catch(e){}return 0},getResponse:function(e){var t=this.getRuntime();try{switch(e){case"blob":var i,n=new f(t.uid,c.response),r=c.getResponseHeader("Content-Disposition");return r&&(i=r.match(/filename=([\'\"'])([^\1]+)\1/))&&(l=i[2]),n.name=l,n.type||(n.type=u.getFileMime(l)),n;case"json":return E.can("return_response_type","json")?c.response:200===c.status&&window.JSON?JSON.parse(c.responseText):null;case"document":var o=c,s=o.responseXML,a=o.responseText;return"IE"===E.browser&&a&&s&&!s.documentElement&&/[^\/]+\/[^\+]+\+xml/.test(o.getResponseHeader("Content-Type"))&&((s=new window.ActiveXObject("Microsoft.XMLDOM")).async=!1,s.validateOnParse=!1,s.loadXML(a)),s&&("IE"===E.browser&&0!==s.parseError||!s.documentElement||"parsererror"===s.documentElement.tagName)?null:s;default:return""!==c.responseText?c.responseText:null}}catch(e){return null}},getAllResponseHeaders:function(){try{return c.getAllResponseHeaders()}catch(e){}return""},abort:function(){c&&c.abort()},destroy:function(){d=l=null}})}}),e("moxie/runtime/html5/utils/BinaryReader",["moxie/core/utils/Basic"],function(t){function e(e){(e instanceof ArrayBuffer?function(r){var o=new DataView(r);t.extend(this,{readByteAt:function(e){return o.getUint8(e)},writeByteAt:function(e,t){o.setUint8(e,t)},SEGMENT:function(e,t,i){switch(arguments.length){case 2:return r.slice(e,e+t);case 1:return r.slice(e);case 3:if((i=null===i?new ArrayBuffer:i)instanceof ArrayBuffer){var n=new Uint8Array(this.length()-t+i.byteLength);0<e&&n.set(new Uint8Array(r.slice(0,e)),0),n.set(new Uint8Array(i),e),n.set(new Uint8Array(r.slice(e+t)),e+i.byteLength),this.clear(),r=n.buffer,o=new DataView(r);break}default:return r}},length:function(){return r?r.byteLength:0},clear:function(){o=r=null}})}:function(n){function r(e,t,i){i=3===arguments.length?i:n.length-t-1,n=n.substr(0,t)+e+n.substr(i+t)}t.extend(this,{readByteAt:function(e){return n.charCodeAt(e)},writeByteAt:function(e,t){r(String.fromCharCode(t),e,1)},SEGMENT:function(e,t,i){switch(arguments.length){case 1:return n.substr(e);case 2:return n.substr(e,t);case 3:r(null!==i?i:"",e,t);break;default:return n}},length:function(){return n?n.length:0},clear:function(){n=null}})}).apply(this,arguments)}return t.extend(e.prototype,{littleEndian:!1,read:function(e,t){var i,n,r;if(e+t>this.length())throw new Error("You are trying to read outside the source boundaries.");for(n=this.littleEndian?0:-8*(t-1),i=r=0;r<t;r++)i|=this.readByteAt(e+r)<<Math.abs(n+8*r);return i},write:function(e,t,i){var n,r;if(e>this.length())throw new Error("You are trying to write outside the source boundaries.");for(n=this.littleEndian?0:-8*(i-1),r=0;r<i;r++)this.writeByteAt(e+r,t>>Math.abs(n+8*r)&255)},BYTE:function(e){return this.read(e,1)},SHORT:function(e){return this.read(e,2)},LONG:function(e){return this.read(e,4)},SLONG:function(e){e=this.read(e,4);return 2147483647<e?e-4294967296:e},CHAR:function(e){return String.fromCharCode(this.read(e,1))},STRING:function(e,t){return this.asArray("CHAR",e,t).join("")},asArray:function(e,t,i){for(var n=[],r=0;r<i;r++)n[r]=this[e](t+r);return n}}),e}),e("moxie/runtime/html5/image/JPEGHeaders",["moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(a,u){return function o(e){var r,t,i,s=[],n=new a(e);if(65496!==n.SHORT(0))throw n.clear(),new u.ImageError(u.ImageError.WRONG_FORMAT);for(r=2;r<=n.length();)if(65488<=(t=n.SHORT(r))&&t<=65495)r+=2;else{if(65498===t||65497===t)break;i=n.SHORT(r+2)+2,65505<=t&&t<=65519&&s.push({hex:t,name:"APP"+(15&t),start:r,length:i,segment:n.SEGMENT(r,i)}),r+=i}return n.clear(),{headers:s,restore:function(e){var t,i,n=new a(e);for(r=65504==n.SHORT(2)?4+n.SHORT(4):2,i=0,t=s.length;i<t;i++)n.SEGMENT(r,0,s[i].segment),r+=s[i].length;return e=n.SEGMENT(),n.clear(),e},strip:function(e){var t,i,n=new o(e),r=n.headers;for(n.purge(),t=new a(e),i=r.length;i--;)t.SEGMENT(r[i].start,r[i].length,"");return e=t.SEGMENT(),t.clear(),e},get:function(e){for(var t=[],i=0,n=s.length;i<n;i++)s[i].name===e.toUpperCase()&&t.push(s[i].segment);return t},set:function(e,t){var i,n,r,o=[];for("string"==typeof t?o.push(t):o=t,i=n=0,r=s.length;i<r&&(s[i].name===e.toUpperCase()&&(s[i].segment=o[n],s[i].length=o[n].length,n++),!(n>=o.length));i++);},purge:function(){this.headers=s=[]}}}}),e("moxie/runtime/html5/image/ExifParser",["moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(p,o,g){function s(e){var t,l,h,f,i;if(o.call(this,e),l={tiff:{274:"Orientation",270:"ImageDescription",271:"Make",272:"Model",305:"Software",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"},exif:{36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"},thumb:{513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength"}},h={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}},n=(f={tiffHeader:10}).tiffHeader,t={clear:this.clear},p.extend(this,{read:function(){try{return s.prototype.read.apply(this,arguments)}catch(e){throw new g.ImageError(g.ImageError.INVALID_META_ERR)}},write:function(){try{return s.prototype.write.apply(this,arguments)}catch(e){throw new g.ImageError(g.ImageError.INVALID_META_ERR)}},UNDEFINED:function(){return this.BYTE.apply(this,arguments)},RATIONAL:function(e){return this.LONG(e)/this.LONG(e+4)},SRATIONAL:function(e){return this.SLONG(e)/this.SLONG(e+4)},ASCII:function(e){return this.CHAR(e)},TIFF:function(){return i||null},EXIF:function(){var e=null;if(f.exifIFD){try{e=r.call(this,f.exifIFD,l.exif)}catch(e){return null}if(e.ExifVersion&&"array"===p.typeOf(e.ExifVersion)){for(var t=0,i="";t<e.ExifVersion.length;t++)i+=String.fromCharCode(e.ExifVersion[t]);e.ExifVersion=i}}return e},GPS:function(){var e=null;if(f.gpsIFD){try{e=r.call(this,f.gpsIFD,l.gps)}catch(e){return null}e.GPSVersionID&&"array"===p.typeOf(e.GPSVersionID)&&(e.GPSVersionID=e.GPSVersionID.join("."))}return e},thumb:function(){if(f.IFD1)try{var e=r.call(this,f.IFD1,l.thumb);if("JPEGInterchangeFormat"in e)return this.SEGMENT(f.tiffHeader+e.JPEGInterchangeFormat,e.JPEGInterchangeFormatLength)}catch(e){}return null},setExif:function(e,t){return("PixelXDimension"===e||"PixelYDimension"===e)&&function(e,t,i){var n,r,o,s=0;if("string"==typeof t){var a,u=l[e.toLowerCase()];for(a in u)if(u[a]===t){t=a;break}}n=f[e.toLowerCase()+"IFD"],r=this.SHORT(n);for(var c=0;c<r;c++)if(o=n+12*c+2,this.SHORT(o)==t){s=o+8;break}if(!s)return!1;try{this.write(s,i,4)}catch(e){return!1}return!0}.call(this,"exif",e,t)},clear:function(){t.clear(),e=l=h=i=f=t=null}}),65505!==this.SHORT(0)||"EXIF\0"!==this.STRING(4,5).toUpperCase())throw new g.ImageError(g.ImageError.INVALID_META_ERR);if(this.littleEndian=18761==this.SHORT(n),42!==this.SHORT(n+=2))throw new g.ImageError(g.ImageError.INVALID_META_ERR);f.IFD0=f.tiffHeader+this.LONG(n+=2),"ExifIFDPointer"in(i=r.call(this,f.IFD0,l.tiff))&&(f.exifIFD=f.tiffHeader+i.ExifIFDPointer,delete i.ExifIFDPointer),"GPSInfoIFDPointer"in i&&(f.gpsIFD=f.tiffHeader+i.GPSInfoIFDPointer,delete i.GPSInfoIFDPointer),p.isEmptyObj(i)&&(i=null);var n=this.LONG(f.IFD0+12*this.SHORT(f.IFD0)+2);function r(e,t){for(var i,n,r,o,s,a=this,u={},c={1:"BYTE",7:"UNDEFINED",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",9:"SLONG",10:"SRATIONAL"},l={BYTE:1,UNDEFINED:1,ASCII:1,SHORT:2,LONG:4,RATIONAL:8,SLONG:4,SRATIONAL:8},d=a.SHORT(e),m=0;m<d;m++)if((i=t[a.SHORT(r=e+2+12*m)])!==x){if(o=c[a.SHORT(r+=2)],n=a.LONG(r+=2),!(s=l[o]))throw new g.ImageError(g.ImageError.INVALID_META_ERR);if(r+=4,(r=4<s*n?a.LONG(r)+f.tiffHeader:r)+s*n>=this.length())throw new g.ImageError(g.ImageError.INVALID_META_ERR);"ASCII"===o?u[i]=p.trim(a.STRING(r,n).replace(/\0$/,"")):(s=a.asArray(o,r,n),o=1==n?s[0]:s,h.hasOwnProperty(i)&&"object"!=typeof o?u[i]=h[i][o]:u[i]=o)}return u}n&&(f.IFD1=f.tiffHeader+n)}return s.prototype=o.prototype,s}),e("moxie/runtime/html5/image/JPEG",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEGHeaders","moxie/runtime/html5/utils/BinaryReader","moxie/runtime/html5/image/ExifParser"],function(s,a,u,c,l){return function(e){var i,n,t,r=new c(e);if(65496!==r.SHORT(0))throw new a.ImageError(a.ImageError.WRONG_FORMAT);i=new u(e);try{n=new l(i.get("app1")[0])}catch(e){}function o(e){var t,i=0;for(e=e||r;i<=e.length();){if(65472<=(t=e.SHORT(i+=2))&&t<=65475)return i+=5,{height:e.SHORT(i),width:e.SHORT(i+=2)};t=e.SHORT(i+=2),i+=t-2}return null}t=o.call(this),s.extend(this,{type:"image/jpeg",size:r.length(),width:t&&t.width||0,height:t&&t.height||0,setExif:function(e,t){if(!n)return!1;"object"===s.typeOf(e)?s.each(e,function(e,t){n.setExif(t,e)}):n.setExif(e,t),i.set("app1",n.SEGMENT())},writeHeaders:function(){return arguments.length?i.restore(arguments[0]):i.restore(e)},stripHeaders:function(e){return i.strip(e)},purge:function(){!function(){n&&i&&r&&(n.clear(),i.purge(),r.clear(),t=i=n=r=null)}.call(this)}}),n&&(this.meta={tiff:n.TIFF(),exif:n.EXIF(),gps:n.GPS(),thumb:function(){var e,t,i=n.thumb();if(i&&(e=new c(i),t=o(e),e.clear(),t))return t.data=i,t;return null}()})}}),e("moxie/runtime/html5/image/PNG",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader"],function(a,u,c){return function(e){for(var t,r=new c(e),i=0,n=0,o=[35152,20039,3338,6666],n=0;n<o.length;n++,i+=2)if(o[n]!=r.SHORT(i))throw new a.ImageError(a.ImageError.WRONG_FORMAT);function s(){r&&(r.clear(),e=t=r=null)}t=function(){var e=function(e){var t,i,n;return t=r.LONG(e),i=r.STRING(e+=4,4),n=e+=4,e=r.LONG(e+t),{length:t,type:i,start:n,CRC:e}}.call(this,8);return"IHDR"==e.type?(e=e.start,{width:r.LONG(e),height:r.LONG(e+=4)}):null}.call(this),u.extend(this,{type:"image/png",size:r.length(),width:t.width,height:t.height,purge:function(){s.call(this)}}),s.call(this)}}),e("moxie/runtime/html5/image/ImageInfo",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEG","moxie/runtime/html5/image/PNG"],function(n,r,o,s){return function(t){var i=[o,s],e=function(){for(var e=0;e<i.length;e++)try{return new i[e](t)}catch(e){}throw new r.ImageError(r.ImageError.WRONG_FORMAT)}();n.extend(this,{type:"",size:0,width:0,height:0,setExif:function(){},writeHeaders:function(e){return e},stripHeaders:function(e){return e},purge:function(){t=null}}),n.extend(this,e),this.purge=function(){e.purge(),e=null}}}),e("moxie/runtime/html5/image/MegaPixel",[],function(){function R(e){var t,i=e.naturalWidth;return 1048576<i*e.naturalHeight&&((t=document.createElement("canvas")).width=t.height=1,(t=t.getContext("2d")).drawImage(e,1-i,0),0===t.getImageData(0,0,1,1).data[3])}return{isSubsampled:R,renderTo:function(e,t,i){for(var n=e.naturalWidth,r=e.naturalHeight,o=i.width,s=i.height,a=i.x||0,u=i.y||0,c=t.getContext("2d"),l=(R(e)&&(n/=2,r/=2),1024),d=document.createElement("canvas"),m=(d.width=d.height=l,d.getContext("2d")),h=function(e,t){var i=document.createElement("canvas"),n=(i.width=1,i.height=t,i.getContext("2d")),r=(n.drawImage(e,0,0),n.getImageData(0,0,1,t).data),o=0,s=t,a=t;for(;o<a;)0===r[4*(a-1)+3]?s=a:o=a,a=s+o>>1;i=null;e=a/t;return 0==e?1:e}(e,r),f=0;f<r;){for(var p=r<f+l?r-f:l,g=0;g<n;){var x=n<g+l?n-g:l,E=(m.clearRect(0,0,l,l),m.drawImage(e,-g,-f),g*o/n+a<<0),y=Math.ceil(x*o/n),w=f*s/r/h+u<<0,v=Math.ceil(p*s/r/h);c.drawImage(d,0,0,x,p,E,w,y,v),g+=l}f+=l}}}}),e("moxie/runtime/html5/image/Image",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/runtime/html5/image/ImageInfo","moxie/runtime/html5/image/MegaPixel","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,g,d,x,t,E,y,w,v,R){return e.Image=function(){var i,n,m,r,o,s=this,h=!1,f=!0;function p(){if(m||i)return m||i;throw new d.ImageError(d.DOMException.INVALID_STATE_ERR)}function a(e){return x.atob(e.substring(e.indexOf("base64,")+7))}function u(e){var t=this;(i=new Image).onerror=function(){l.call(this),t.trigger("error",d.ImageError.WRONG_FORMAT)},i.onload=function(){t.trigger("load")},i.src="data:"==e.substr(0,5)?e:"data:"+(o.type||"")+";base64,"+x.btoa(e)}function c(e,t,i,n){var r,o,s,a=0,u=0;if(f=n,o=this.meta&&this.meta.tiff&&this.meta.tiff.Orientation||1,-1!==g.inArray(o,[5,6,7,8])&&(s=e,e=t,t=s),s=p(),!(1<(r=i?(e=Math.min(e,s.width),t=Math.min(t,s.height),Math.max(e/s.width,t/s.height)):Math.min(e/s.width,t/s.height))&&!i&&n)){if(m=m||document.createElement("canvas"),n=Math.round(s.width*r),r=Math.round(s.height*r),i?(m.width=e,m.height=t,e<n&&(a=Math.round((n-e)/2)),t<r&&(u=Math.round((r-t)/2))):(m.width=n,m.height=r),!f){var c=m.width,l=m.height,i=o;switch(i){case 5:case 6:case 7:case 8:m.width=l,m.height=c;break;default:m.width=c,m.height=l}var d=m.getContext("2d");switch(i){case 2:d.translate(c,0),d.scale(-1,1);break;case 3:d.translate(c,l),d.rotate(Math.PI);break;case 4:d.translate(0,l),d.scale(1,-1);break;case 5:d.rotate(.5*Math.PI),d.scale(1,-1);break;case 6:d.rotate(.5*Math.PI),d.translate(0,-l);break;case 7:d.rotate(.5*Math.PI),d.translate(c,-l),d.scale(-1,1);break;case 8:d.rotate(-.5*Math.PI),d.translate(-c,0)}}!function(e,t,i,n,r,o){"iOS"===R.OS?w.renderTo(e,t,{width:r,height:o,x:i,y:n}):t.getContext("2d").drawImage(e,i,n,r,o)}.call(this,s,m,-a,-u,n,r),this.width=m.width,this.height=m.height,h=!0}this.trigger("Resize")}function l(){n&&(n.purge(),n=null),r=i=m=o=null,h=!1}g.extend(this,{loadFromBlob:function(e){var t=this,i=t.getRuntime(),n=!(1<arguments.length)||arguments[1];if(!i.can("access_binary"))throw new d.RuntimeError(d.RuntimeError.NOT_SUPPORTED_ERR);(o=e).isDetached()?(r=e.getSource(),u.call(this,r)):function(e,t){var i,n=this;{if(!window.FileReader)return t(e.getAsDataURL());(i=new FileReader).onload=function(){t(this.result)},i.onerror=function(){n.trigger("error",d.ImageError.WRONG_FORMAT)},i.readAsDataURL(e)}}.call(this,e.getSource(),function(e){n&&(r=a(e)),u.call(t,e)})},loadFromImage:function(e,t){this.meta=e.meta,o=new E(null,{name:e.name,size:e.size,type:e.type}),u.call(this,t?r=e.getAsBinaryString():e.getAsDataURL())},getInfo:function(){var e=this.getRuntime();return!n&&r&&e.can("access_image_binary")&&(n=new y(r)),!(e={width:p().width||0,height:p().height||0,type:o.type||v.getFileMime(o.name),size:r&&r.length||o.size||0,name:o.name||"",meta:n&&n.meta||this.meta||{}}).meta||!e.meta.thumb||e.meta.thumb.data instanceof t||(e.meta.thumb.data=new t(null,{type:"image/jpeg",data:e.meta.thumb.data})),e},downsize:function(){c.apply(this,arguments)},getAsCanvas:function(){return m&&(m.id=this.uid+"_canvas"),m},getAsBlob:function(e,t){return e!==this.type&&c.call(this,this.width,this.height,!1),new E(null,{name:o.name||"",type:e,data:s.getAsBinaryString.call(this,e,t)})},getAsDataURL:function(e){var t=arguments[1]||90;if(!h)return i.src;if("image/jpeg"!==e)return m.toDataURL("image/png");try{return m.toDataURL("image/jpeg",t/100)}catch(e){return m.toDataURL("image/jpeg")}},getAsBinaryString:function(e,t){if(!h)return r=r||a(s.getAsDataURL(e,t));if("image/jpeg"!==e)r=a(s.getAsDataURL(e,t));else{var i;t=t||90;try{i=m.toDataURL("image/jpeg",t/100)}catch(e){i=m.toDataURL("image/jpeg")}r=a(i),n&&(r=n.stripHeaders(r),f&&(n.meta&&n.meta.exif&&n.setExif({PixelXDimension:this.width,PixelYDimension:this.height}),r=n.writeHeaders(r)),n.purge(),n=null)}return h=!1,r},destroy:function(){s=null,l.call(this),this.getRuntime().getShim().removeInstance(this.uid)}})}}),e("moxie/runtime/flash/Runtime",[],function(){return{}}),e("moxie/runtime/silverlight/Runtime",[],function(){return{}}),e("moxie/runtime/html4/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(o,e,s,a){var u={};return s.addConstructor("html4",function(e){var t,i=this,n=s.capTest,r=s.capTrue;s.call(this,e,"html4",{access_binary:n(window.FileReader||window.File&&File.getAsDataURL),access_image_binary:!1,display_media:n(u.Image&&(a.can("create_canvas")||a.can("use_data_uri_over32kb"))),do_cors:!1,drag_and_drop:!1,filter_by_extension:n("Chrome"===a.browser&&a.verComp(a.version,28,">=")||"IE"===a.browser&&a.verComp(a.version,10,">=")||"Safari"===a.browser&&a.verComp(a.version,7,">=")),resize_image:function(){return u.Image&&i.can("access_binary")&&a.can("create_canvas")},report_upload_progress:!1,return_response_headers:!1,return_response_type:function(e){return!("json"!==e||!window.JSON)||!!~o.inArray(e,["text","document",""])},return_status_code:function(e){return!o.arrayDiff(e,[200,404])},select_file:function(){return a.can("use_fileinput")},select_multiple:!1,send_binary_string:!1,send_custom_headers:!1,send_multipart:!0,slice_blob:!1,stream_upload:function(){return i.can("select_file")},summon_file_dialog:function(){return i.can("select_file")&&("Firefox"===a.browser&&a.verComp(a.version,4,">=")||"Opera"===a.browser&&a.verComp(a.version,12,">=")||"IE"===a.browser&&a.verComp(a.version,10,">=")||!!~o.inArray(a.browser,["Chrome","Safari"]))},upload_filesize:r,use_http_method:function(e){return!o.arrayDiff(e,["GET","POST"])}}),o.extend(this,{init:function(){this.trigger("Init")},destroy:(t=this.destroy,function(){t.call(i),t=i=null})}),o.extend(this.getShim(),u)}),u}),e("moxie/runtime/html4/file/FileInput",["moxie/runtime/html4/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,d,m,h,f,s,p){return e.FileInput=function(){var a,u,c=[];function l(){var e,t,i,n=this,r=n.getRuntime(),o=m.guid("uid_"),s=r.getShimContainer();a&&(e=h.get(a+"_form"))&&m.extend(e.style,{top:"100%"}),(t=document.createElement("form")).setAttribute("id",o+"_form"),t.setAttribute("method","post"),t.setAttribute("enctype","multipart/form-data"),t.setAttribute("encoding","multipart/form-data"),m.extend(t.style,{overflow:"hidden",position:"absolute",top:0,left:0,width:"100%",height:"100%"}),(i=document.createElement("input")).setAttribute("id",o),i.setAttribute("type","file"),i.setAttribute("name",u.name||"Filedata"),i.setAttribute("accept",c.join(",")),m.extend(i.style,{fontSize:"999px",opacity:0}),t.appendChild(i),s.appendChild(t),m.extend(i.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),"IE"===p.browser&&p.verComp(p.version,10,"<")&&m.extend(i.style,{filter:"progid:DXImageTransform.Microsoft.Alpha(opacity=0)"}),i.onchange=function(){var e;if(this.value){if(this.files){if(0===(e=this.files[0]).size)return void t.parentNode.removeChild(t)}else e={name:this.value};e=new d(r.uid,e),this.onchange=function(){},l.call(n),n.files=[e],i.setAttribute("id",e.uid),t.setAttribute("id",e.uid+"_form"),n.trigger("change"),i=t=null}},r.can("summon_file_dialog")&&(e=h.get(u.browse_button),f.removeEvent(e,"click",n.uid),f.addEvent(e,"click",function(e){i&&!i.disabled&&i.click(),e.preventDefault()},n.uid)),a=o}m.extend(this,{init:function(e){var t,i,n,r=this,o=r.getRuntime();c=(u=e).accept.mimes||s.extList2mimes(e.accept,o.can("filter_by_extension")),t=o.getShimContainer(),n=h.get(e.browse_button),o.can("summon_file_dialog")&&("static"===h.getStyle(n,"position")&&(n.style.position="relative"),i=parseInt(h.getStyle(n,"z-index"),10)||1,n.style.zIndex=i,t.style.zIndex=i-1),i=o.can("summon_file_dialog")?n:t,f.addEvent(i,"mouseover",function(){r.trigger("mouseenter")},r.uid),f.addEvent(i,"mouseout",function(){r.trigger("mouseleave")},r.uid),f.addEvent(i,"mousedown",function(){r.trigger("mousedown")},r.uid),f.addEvent(h.get(e.container),"mouseup",function(){r.trigger("mouseup")},r.uid),l.call(this),t=null,r.trigger({type:"ready",async:!0})},disable:function(e){var t;(t=h.get(a))&&(t.disabled=!!e)},destroy:function(){var e=this.getRuntime(),t=e.getShim(),e=e.getShimContainer();f.removeAllEvents(e,this.uid),f.removeAllEvents(u&&h.get(u.container),this.uid),f.removeAllEvents(u&&h.get(u.browse_button),this.uid),e&&(e.innerHTML=""),t.removeInstance(this.uid),a=c=u=null}})}}),e("moxie/runtime/html4/file/FileReader",["moxie/runtime/html4/Runtime","moxie/runtime/html5/file/FileReader"],function(e,t){return e.FileReader=t}),e("moxie/runtime/html4/xhr/XMLHttpRequest",["moxie/runtime/html4/Runtime","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Url","moxie/core/Exceptions","moxie/core/utils/Events","moxie/file/Blob","moxie/xhr/FormData"],function(e,m,h,f,p,g,x,E){return e.XMLHttpRequest=function(){var u,c,l;function d(t){var e,i,n,r=this,o=!1;if(l){if(e=l.id.replace(/_iframe$/,""),e=h.get(e+"_form")){for(n=(i=e.getElementsByTagName("input")).length;n--;)switch(i[n].getAttribute("type")){case"hidden":i[n].parentNode.removeChild(i[n]);break;case"file":o=!0}i=[],o||e.parentNode.removeChild(e),e=null}setTimeout(function(){g.removeEvent(l,"load",r.uid),l.parentNode&&l.parentNode.removeChild(l);var e=r.getRuntime().getShimContainer();e.children.length||e.parentNode.removeChild(e),e=l=null,t()},1)}}m.extend(this,{send:function(t,e){var i,n,r,o,s=this,a=s.getRuntime();if(u=c=null,e instanceof E&&e.hasBlob()){if(o=e.getBlob(),i=o.uid,r=h.get(i),!(n=h.get(i+"_form")))throw new p.DOMException(p.DOMException.NOT_FOUND_ERR)}else i=m.guid("uid_"),(n=document.createElement("form")).setAttribute("id",i+"_form"),n.setAttribute("method",t.method),n.setAttribute("enctype","multipart/form-data"),n.setAttribute("encoding","multipart/form-data"),a.getShimContainer().appendChild(n);n.setAttribute("target",i+"_iframe"),e instanceof E&&e.each(function(e,t){var i;e instanceof x?r&&r.setAttribute("name",t):(i=document.createElement("input"),m.extend(i,{type:"hidden",name:t,value:e}),r?n.insertBefore(i,r):n.appendChild(i))}),n.setAttribute("action",t.url),e=a.getShimContainer()||document.body,(a=document.createElement("div")).innerHTML='<iframe id="'+i+'_iframe" name="'+i+'_iframe" src="javascript:&quot;&quot;" style="display:none"></iframe>',l=a.firstChild,e.appendChild(l),g.addEvent(l,"load",function(){var e;try{e=l.contentWindow.document||l.contentDocument||window.frames[l.id].document,/^4(0[0-9]|1[0-7]|2[2346])\s/.test(e.title)?u=e.title.replace(/^(\d+).*$/,"$1"):(u=200,c=m.trim(e.body.innerHTML),s.trigger({type:"progress",loaded:c.length,total:c.length}),o&&s.trigger({type:"uploadprogress",loaded:o.size||1025,total:o.size||1025}))}catch(e){if(!f.hasSameOrigin(t.url))return void d.call(s,function(){s.trigger("error")});u=404}d.call(s,function(){s.trigger("load")})},s.uid),n.submit(),s.trigger("loadstart")},getStatus:function(){return u},getResponse:function(e){if("json"===e&&"string"===m.typeOf(c)&&window.JSON)try{return JSON.parse(c.replace(/^\s*<pre[^>]*>/,"").replace(/<\/pre>\s*$/,""))}catch(e){return null}return c},abort:function(){var e=this;l&&l.contentWindow&&(l.contentWindow.stop?l.contentWindow.stop():l.contentWindow.document.execCommand?l.contentWindow.document.execCommand("Stop"):l.src="about:blank"),d.call(this,function(){e.dispatchEvent("abort")})}})}}),e("moxie/runtime/html4/image/Image",["moxie/runtime/html4/Runtime","moxie/runtime/html5/image/Image"],function(e,t){return e.Image=t});for(var t=["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/I18n","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/file/FileInput","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/file/FileDrop","moxie/file/FileReader","moxie/core/utils/Url","moxie/runtime/RuntimeTarget","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/xhr/XMLHttpRequest","moxie/runtime/Transporter","moxie/image/Image","moxie/core/utils/Events"],i=0;i<t.length;i++){for(var r=o,a=t[i],u=a.split(/[.\/]/),c=0;c<u.length-1;++c)r[u[c]]===x&&(r[u[c]]={}),r=r[u[c]];r[u[u.length-1]]=s[a]}}(this),function(e){"use strict";var r={},o=e.moxie.core.utils.Basic.inArray;!function e(t){var i,n;for(i in t)"object"!=(n=typeof t[i])||~o(i,["Exceptions","Env","Mime"])?"function"==n&&(r[i]=t[i]):e(t[i])}(e.moxie),r.Env=e.moxie.core.utils.Env,r.Mime=e.moxie.core.utils.Mime,r.Exceptions=e.moxie.core.Exceptions,e.mOxie=r,e.o||(e.o=r)}(this);